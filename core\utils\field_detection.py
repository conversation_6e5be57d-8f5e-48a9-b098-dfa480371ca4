"""
Field Type Detection Utilities
Централизованная логика определения типов полей
"""

import bpy
from typing import Optional


def detect_field_type_from_modifier(field_modifier) -> str:
    """
    Определить тип поля по модификатору
    
    Args:
        field_modifier: Модификатор поля
        
    Returns:
        str: Тип поля ("SPHERE", "BOX", "RING", "OBJECT") или "SPHERE" как fallback
    """
    if not field_modifier or not field_modifier.node_group:
        return "SPHERE"  # Fallback

    # Проверяем по имени node group
    field_type = detect_field_type_from_name(field_modifier.node_group.name)
    if field_type != "SPHERE":  # Если нашли что-то кроме fallback
        return field_type

    # Проверяем по имени модификатора как fallback
    return detect_field_type_from_name(field_modifier.name)


def detect_field_type_from_name(name: str) -> str:
    """
    Определить тип поля по имени (node group или модификатора)
    
    Args:
        name: Имя для анализа
        
    Returns:
        str: Тип поля ("SPHERE", "BOX", "RING", "OBJECT") или "SPHERE" как fallback
    """
    if not name:
        return "SPHERE"
    
    name_lower = name.lower()
    
    # Проверяем по ключевым словам в имени
    if 'sphere' in name_lower:
        return "SPHERE"
    elif 'box' in name_lower:
        return "BOX"
    elif 'ring' in name_lower:
        return "RING"
    elif 'object' in name_lower:
        return "OBJECT"
    
    # Fallback
    return "SPHERE"


def is_field_modifier(modifier) -> bool:
    """
    Проверить, является ли модификатор полем
    
    Args:
        modifier: Модификатор для проверки
        
    Returns:
        bool: True если это модификатор поля
    """
    if not modifier:
        return False
        
    if modifier.type != 'NODES' or not modifier.node_group:
        return False
        
    # Проверяем по имени node group
    node_group_name = modifier.node_group.name.lower()
    field_keywords = ['field', 'sphere', 'box', 'ring']
    
    return any(keyword in node_group_name for keyword in field_keywords)


def get_field_modifier_display_name(modifier) -> str:
    """
    Получить отображаемое имя модификатора поля
    
    Args:
        modifier: Модификатор поля
        
    Returns:
        str: Отображаемое имя
    """
    if not modifier:
        return "Unknown Field"
        
    field_type = detect_field_type_from_modifier(modifier)
    return f"{field_type.title()} Field"


# =============================================================================
# LEGACY COMPATIBILITY
# =============================================================================

def detect_field_type_legacy(field_modifier) -> str:
    """
    Legacy функция для обратной совместимости
    Использует новую централизованную логику
    """
    return detect_field_type_from_modifier(field_modifier)
