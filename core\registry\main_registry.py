"""
ClonerPro Main Registry System

Центральная система регистрации всех компонентов ClonerPro.
Современная классовая архитектура с wrapper поддержкой.

Все компоненты (клонеры, эффекторы, поля) используют единую wrapper архитектуру
для консистентного поведения и управления.
"""

import bpy
from typing import Dict, Type, Optional, List, Union

# Импортируем специализированные реестры
from .cloner_registry import CLONER_REGISTRY, get_cloner_class, list_cloner_types
from .effector_registry import EFFECTOR_REGISTRY, get_effector_class, list_effector_types  
from .field_registry import FIELD_REGISTRY, get_field_class, list_field_types


# ======================================================================
# MAIN REGISTRY - Объединенный реестр всех компонентов
# ======================================================================

class ClonerProMainRegistry:
    """
    Главный реестр ClonerPro
    
    Предоставляет единый интерфейс для работы со всеми типами компонентов:
    - Клонеры (Cloners)
    - Эффекторы (Effectors) 
    - Поля (Fields)
    """
    
    def __init__(self):
        """Инициализация главного реестра"""
        self._cloners = CLONER_REGISTRY
        self._effectors = EFFECTOR_REGISTRY
        self._fields = FIELD_REGISTRY
    
    # ==================== КЛОНЕРЫ ====================
    
    def get_cloner(self, cloner_type: str) -> Optional[Type]:
        """Получить класс клонера по типу"""
        return get_cloner_class(cloner_type)
    
    def list_cloners(self) -> List[str]:
        """Получить список всех доступных клонеров"""
        return list_cloner_types()
    
    def is_cloner_registered(self, cloner_type: str) -> bool:
        """Проверить, зарегистрирован ли клонер"""
        return cloner_type in self._cloners
    
    # ==================== ЭФФЕКТОРЫ ====================
    
    def get_effector(self, effector_type: str) -> Optional[Type]:
        """Получить класс эффектора по типу"""
        return get_effector_class(effector_type)
    
    def list_effectors(self) -> List[str]:
        """Получить список всех доступных эффекторов"""
        return list_effector_types()
    
    def is_effector_registered(self, effector_type: str) -> bool:
        """Проверить, зарегистрирован ли эффектор"""
        return effector_type in self._effectors
    
    # ==================== ПОЛЯ ====================
    
    def get_field(self, field_type: str) -> Optional[Type]:
        """Получить класс поля по типу"""
        return get_field_class(field_type)
    
    def list_fields(self) -> List[str]:
        """Получить список всех доступных полей"""
        return list_field_types()
    
    def is_field_registered(self, field_type: str) -> bool:
        """Проверить, зарегистрировано ли поле"""
        return field_type in self._fields
    
    # ==================== УНИВЕРСАЛЬНЫЕ МЕТОДЫ ====================
    
    def get_component(self, component_type: str, registry_type: str) -> Optional[Type]:
        """
        Универсальный метод получения компонента
        
        Args:
            component_type: Тип компонента
            registry_type: Тип реестра ('cloner', 'effector', 'field')
            
        Returns:
            Класс компонента или None
        """
        if registry_type == 'cloner':
            return self.get_cloner(component_type)
        elif registry_type == 'effector':
            return self.get_effector(component_type)
        elif registry_type == 'field':
            return self.get_field(component_type)
        else:
            print(f"⚠️ [MAIN_REGISTRY] Unknown registry type: {registry_type}")
            return None
    
    def list_all_components(self) -> Dict[str, List[str]]:
        """Получить список всех компонентов по категориям"""
        return {
            'cloners': self.list_cloners(),
            'effectors': self.list_effectors(),
            'fields': self.list_fields()
        }
    
    def get_component_info(self, component_type: str, registry_type: str) -> Optional[Dict]:
        """
        Получить информацию о компоненте
        
        Args:
            component_type: Тип компонента
            registry_type: Тип реестра
            
        Returns:
            Словарь с информацией о компоненте
        """
        component_class = self.get_component(component_type, registry_type)
        if not component_class:
            return None
        
        return {
            'type': component_type,
            'registry': registry_type,
            'class': component_class,
            'name': getattr(component_class, 'bl_label', component_type),
            'description': getattr(component_class, 'bl_description', ''),
            'has_wrapper': hasattr(component_class, 'create_node_group'),
            'architecture': 'CLASS_BASED_WRAPPER'
        }
    
    def validate_all_registrations(self) -> bool:
        """
        Валидация всех регистраций компонентов
        
        Returns:
            True если все компоненты корректно зарегистрированы
        """
        print("🔍 [MAIN_REGISTRY] Validating all component registrations...")
        
        all_valid = True
        
        # Валидация клонеров
        for cloner_type in self.list_cloners():
            cloner_class = self.get_cloner(cloner_type)
            if self._validate_component_class(cloner_class, cloner_type, 'cloner'):
                print(f"✅ [MAIN_REGISTRY] Cloner '{cloner_type}' -> {cloner_class.__name__}")
            else:
                all_valid = False
        
        # Валидация эффекторов
        for effector_type in self.list_effectors():
            effector_class = self.get_effector(effector_type)
            if self._validate_component_class(effector_class, effector_type, 'effector'):
                print(f"✅ [MAIN_REGISTRY] Effector '{effector_type}' -> {effector_class.__name__}")
            else:
                all_valid = False
        
        # Валидация полей
        for field_type in self.list_fields():
            field_class = self.get_field(field_type)
            if self._validate_component_class(field_class, field_type, 'field'):
                print(f"✅ [MAIN_REGISTRY] Field '{field_type}' -> {field_class.__name__}")
            else:
                all_valid = False
        
        if all_valid:
            print("✅ [MAIN_REGISTRY] All component registrations are valid")
        else:
            print("⚠️ [MAIN_REGISTRY] Some component registrations have issues")
        
        return all_valid
    
    def _validate_component_class(self, component_class: Type, component_type: str, registry_type: str) -> bool:
        """Валидация класса компонента"""
        if not component_class:
            print(f"❌ [MAIN_REGISTRY] {registry_type.title()} '{component_type}' not found")
            return False
        
        # Проверяем, что это класс
        if not hasattr(component_class, '__name__'):
            print(f"❌ [MAIN_REGISTRY] {registry_type.title()} '{component_type}' is not a class")
            return False
        
        # Проверяем наличие обязательных методов
        required_methods = ['create_node_group']
        for method in required_methods:
            if not hasattr(component_class, method):
                print(f"❌ [MAIN_REGISTRY] {registry_type.title()} '{component_type}' missing method: {method}")
                return False
        
        return True
    
    def print_registry_stats(self):
        """Вывести статистику реестра"""
        print("\n📊 [MAIN_REGISTRY] Registry Statistics:")
        print(f"   🟢 Cloners: {len(self.list_cloners())}")
        print(f"   🟣 Effectors: {len(self.list_effectors())}")
        print(f"   🌸 Fields: {len(self.list_fields())}")
        print(f"   📦 Total Components: {len(self.list_cloners()) + len(self.list_effectors()) + len(self.list_fields())}")


# ======================================================================
# ГЛОБАЛЬНЫЙ ЭКЗЕМПЛЯР РЕЕСТРА
# ======================================================================

# Создаем глобальный экземпляр главного реестра
main_registry = ClonerProMainRegistry()


# ======================================================================
# УДОБНЫЕ ФУНКЦИИ
# ======================================================================

def get_component_class(component_type: str, registry_type: str) -> Optional[Type]:
    """Получить класс компонента"""
    return main_registry.get_component(component_type, registry_type)


def list_available_components(registry_type: str = None) -> Union[List[str], Dict[str, List[str]]]:
    """Получить список доступных компонентов"""
    if registry_type:
        if registry_type == 'cloner':
            return main_registry.list_cloners()
        elif registry_type == 'effector':
            return main_registry.list_effectors()
        elif registry_type == 'field':
            return main_registry.list_fields()
        else:
            return []
    else:
        return main_registry.list_all_components()


def validate_component_registration() -> bool:
    """Валидация всех регистраций"""
    return main_registry.validate_all_registrations()


# ======================================================================
# АВТОМАТИЧЕСКАЯ ВАЛИДАЦИЯ
# ======================================================================

# Автоматическая валидация при импорте модуля (только в режиме разработки)
if __name__ != "__main__":
    try:
        main_registry.print_registry_stats()
        # main_registry.validate_all_registrations()  # Отключено для производительности
    except Exception as e:
        print(f"⚠️ [MAIN_REGISTRY] Validation error: {e}")
