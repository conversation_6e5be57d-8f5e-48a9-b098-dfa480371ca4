"""
Effectors module for ClonerPro

This module contains all effector implementations for the ClonerPro addon.
Supports both class-based and function-based architectures.
"""

# Class-based effectors (new)
from .random import RandomEffector, create_random_effector
from .noise import NoiseEffector, create_noise_effector
from .step import StepEffector, create_step_effector

__all__ = [
    # Class-based effectors
    "RandomEffector",
    "create_random_effector",
    "NoiseEffector",
    "create_noise_effector",
    "StepEffector",
    "create_step_effector"
]


def register():
    """Register effector components"""
    print("✅ ClonerPro: Effectors module registered")
    print("  - Class-based effectors: RandomEffector, NoiseEffector, StepEffector")
    # Эффекторы регистрируются через registry систему


def unregister():
    """Unregister effector components"""
    print("✅ ClonerPro: Effectors module unregistered")
    # Здесь можно добавить логику очистки если потребуется