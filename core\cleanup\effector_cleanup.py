"""
Effector Cleanup Manager для ClonerPro
Автоматическая очистка эффекторов при удалении модификаторов
"""

import bpy


def setup_effector_deletion_monitoring():
    """
    Настраивает мониторинг удаления эффекторов
    С защитными механизмами против крашей
    """
    try:
        # Создаем handler для отслеживания изменений
        def effector_cleanup_handler(scene, depsgraph):
            """Handler для автоматической очистки удаленных эффекторов"""
            try:
                # Импортируем защитные функции из core
                from ...core.system.dependency_safety import is_safe_to_perform_effector_operations
                
                # Проверяем безопасность операций
                if not is_safe_to_perform_effector_operations():
                    return
                
                # Получаем список всех объектов с клонерами
                for obj in scene.objects:
                    if not obj.modifiers:
                        continue
                    
                    # Современная система - эффекторы работают независимо
                    pass
                                # Cleanup operation
            
            except Exception as e:
                # Безопасно игнорируем ошибки в handler
                # Cleanup operation
                
                # При критической ошибке выполняем экстренную очистку
                try:
                    from ...core.system.dependency_safety import emergency_system_reset
                    emergency_system_reset()
                except:
                    pass
        
        # Убираем старые handlers
        handlers_to_remove = []
        for handler in bpy.app.handlers.depsgraph_update_post:
            if (hasattr(handler, '__name__') and 
                handler.__name__ == 'effector_cleanup_handler'):
                handlers_to_remove.append(handler)
        
        for handler in handlers_to_remove:
            bpy.app.handlers.depsgraph_update_post.remove(handler)
        
        # Добавляем новый handler
        bpy.app.handlers.depsgraph_update_post.append(effector_cleanup_handler)
        
        print("✅ Настроен мониторинг удаления эффекторов")
        
    except Exception as e:
        print(f"[ERROR] Ошибка настройки мониторинга: {e}")


def cleanup_unused_effectors():
    """
    Современная очистка эффекторов без старой системы линковки
    """
    try:
        cleaned_count = 0
        
        for obj in bpy.context.scene.objects:
            if not obj.modifiers:
                continue
            
            # Простая очистка памяти от неиспользуемых эффекторов
            for mod in obj.modifiers:
                if mod.type == 'NODES' and mod.node_group:
                    if mod.node_group.get("component_type") == 'EFFECTOR':
                        # Проверяем, что эффектор активен
                        if not mod.show_viewport:
                            print(f"Найден неактивный эффектор: {mod.name}")
        
        print(f"✅ Проверка завершена. Активные эффекторы работают корректно")
        return cleaned_count
        
    except Exception as e:
        print(f"[ERROR] Ошибка проверки эффекторов: {e}")
        return 0


def complete_effector_memory_cleanup(effector_name):
    """
    Обертка для полной очистки памяти эффектора через core систему
    """
    try:
        from ...core.system.dependency_safety import complete_effector_memory_cleanup as core_cleanup
        core_cleanup(effector_name)
    except Exception as e:
        print(f"[ERROR] Ошибка полной очистки памяти для {effector_name}: {e}")


def remove_effector_deletion_monitoring():
    """Удаляет мониторинг удаления эффекторов"""
    try:
        handlers_to_remove = []
        for handler in bpy.app.handlers.depsgraph_update_post:
            if (hasattr(handler, '__name__') and 
                handler.__name__ == 'effector_cleanup_handler'):
                handlers_to_remove.append(handler)
        
        for handler in handlers_to_remove:
            bpy.app.handlers.depsgraph_update_post.remove(handler)
        
        print("✅ Мониторинг удаления эффекторов отключен")
        
    except Exception as e:
        print(f"[ERROR] Ошибка отключения мониторинга: {e}")


