"""
Registry System - Центральная система регистрации компонентов ClonerPro

Современная классовая архитектура с wrapper поддержкой.
Все компоненты используют единую wrapper архитектуру.
"""

# Импортируем новый главный реестр
from .main_registry import (
    main_registry,
    ClonerProMainRegistry,
    get_component_class,
    list_available_components,
    validate_component_registration
)

# Импортируем специализированные реестры для прямого доступа
from .cloner_registry import CLONER_REGISTRY, get_cloner_class, list_cloner_types
from .effector_registry import EFFECTOR_REGISTRY, get_effector_class, list_effector_types
from .field_registry import FIELD_REGISTRY, get_field_class, list_field_types

# Современные функции - используйте main_registry для новых проектов

__all__ = [
    # Главный реестр - рекомендуемый способ
    'main_registry',
    'ClonerProMainRegistry',
    'get_component_class',
    'list_available_components',
    'validate_component_registration',

    # Специализированные реестры - для прямого доступа
    'CLONER_REGISTRY',
    'EFFECTOR_REGISTRY',
    'FIELD_REGISTRY',
    'get_cloner_class',
    'get_effector_class',
    'get_field_class',
    'list_cloner_types',
    'list_effector_types',
    'list_field_types'
]