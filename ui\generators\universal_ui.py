"""
Universal UI Generator для ClonerPro
Унифицированная система отрисовки UI для всех типов компонентов
"""

import bpy
from typing import Dict, List, Any, Optional


class UniversalComponentUI:
    """
    Универсальный генератор UI для всех компонентов ClonerPro
    
    Поддерживает:
    - Динамическое создание UI из реестра
    - Группировку параметров
    - Состояния раскрытия/сворачивания
    - Специфичные иконки для каждого типа компонента
    """
    
    COMPONENT_ICONS = {
        'CLONER': 'MESH_GRID',
        'EFFECTOR': 'FORCE_VORTEX', 
        'FIELD': 'OUTLINER_OB_FORCE_FIELD'
    }
    
    @staticmethod
    def draw_component_ui(layout, modifier, component_type: str, registry_type: str):
        """
        Универсальная отрисовка UI компонента
        
        Args:
            layout: UI layout
            modifier: Модификатор geometry nodes
            component_type: Тип компонента (например, "RANDOM", "SPHERE")
            registry_type: Тип реестра ("effector", "field", "cloner")
        """
        try:
            # Получаем класс компонента из соответствующего реестра
            component_class = UniversalComponentUI._get_component_class(component_type, registry_type)
            
            if not component_class:
                layout.label(text=f"Unknown {registry_type} type: {component_type}", icon='ERROR')
                return
            
            # Создаем экземпляр и используем его метод draw_ui
            component_instance = component_class()
            
            # Проверяем, есть ли у компонента собственный метод draw_ui
            if hasattr(component_instance, 'draw_ui'):
                component_instance.draw_ui(layout, None, modifier)
            else:
                # Fallback: используем универсальную отрисовку
                UniversalComponentUI._draw_universal_ui(layout, modifier, component_instance, registry_type)
                
        except Exception as e:
            layout.label(text=f"Error loading {component_type} UI: {str(e)}", icon='ERROR')
            print(f"[UNIVERSAL_UI] Error drawing {component_type} UI: {e}")
    
    @staticmethod
    def _get_component_class(component_type: str, registry_type: str):
        """Получение класса компонента из соответствующего реестра"""
        try:
            if registry_type == "effector":
                from ...core.registry.effector_registry import get_effector_class
                return get_effector_class(component_type)
            elif registry_type == "field":
                from ...core.registry.field_registry import get_field_class
                return get_field_class(component_type)
            elif registry_type == "cloner":
                from ...core.registry.cloner_registry import get_cloner_class
                return get_cloner_class(component_type)
            else:
                print(f"[UNIVERSAL_UI] Unknown registry type: {registry_type}")
                return None
        except Exception as e:
            print(f"[UNIVERSAL_UI] Error getting component class: {e}")
            return None
    
    @staticmethod
    def _draw_universal_ui(layout, modifier, component_instance, registry_type: str):
        """
        Универсальная отрисовка UI когда у компонента нет собственного draw_ui
        """
        from ..utils.ui_helpers import display_socket_prop
        
        # Получаем группы параметров
        if hasattr(component_instance, 'get_parameter_groups'):
            parameter_groups = component_instance.get_parameter_groups()
        else:
            # Fallback: создаем базовую группу
            parameter_groups = {"Settings": []}
        
        # Отображаем группы параметров
        for group_name, param_names in parameter_groups.items():
            if param_names:
                # Создаем бокс для группы
                box = layout.box()
                col = box.column(align=True)
                
                # Заголовок группы с иконкой
                icon = UniversalComponentUI._get_group_icon(group_name, registry_type)
                col.label(text=f"{group_name}:", icon=icon)
                
                # Добавляем параметры
                col.separator()
                for param_name in param_names:
                    display_socket_prop(col, modifier, param_name, text=param_name)
    
    @staticmethod
    def _get_group_icon(group_name: str, registry_type: str) -> str:
        """Получение иконки для группы параметров"""
        # Специфичные иконки для групп
        group_icons = {
            "Effector Settings": "SETTINGS",
            "Field Settings": "SETTINGS", 
            "Transform": "ORIENTATION_GIMBAL",
            "Random Settings": "FORCE_HARMONIC",
            "Spatial Parameters": "EMPTY_ARROWS",
            "Falloff Controls": "SMOOTHCURVE",
            "Collection Random": "COLLECTION_NEW",
            "Global Transform": "WORLD",
            "Anti-Recursion": "LOOP_BACK"
        }
        
        # Возвращаем специфичную иконку или базовую для типа компонента
        return group_icons.get(group_name, UniversalComponentUI.COMPONENT_ICONS.get(registry_type.upper(), "DOT"))


class DynamicEnumGenerator:
    """
    Генератор динамических EnumProperty для операторов
    """
    
    @staticmethod
    def get_effector_enum_items(self, context):
        """Динамическое получение списка эффекторов из реестра"""
        from ...core.registry.effector_registry import list_effector_types, get_effector_info
        
        items = []
        effector_types = list_effector_types()
        
        for effector_type in effector_types:
            effector_info = get_effector_info(effector_type)
            display_name = effector_info.get('display_name', effector_type.title())
            description = effector_info.get('description', f"{display_name} effector")
            items.append((effector_type, display_name, description))
        
        # Fallback если реестр пуст
        if not items:
            items.append(('RANDOM', "Random", "Random effector (fallback)"))
        
        return items
    
    @staticmethod
    def get_field_enum_items(self, context):
        """Динамическое получение списка полей из реестра"""
        from ...core.registry.field_registry import list_field_types, get_field_info
        
        items = []
        field_types = list_field_types()
        
        for field_type in field_types:
            field_info = get_field_info(field_type)
            display_name = field_info.get('display_name', field_type.title())
            description = field_info.get('description', f"{display_name} field")
            items.append((field_type, display_name, description))
        
        # Fallback если реестр пуст
        if not items:
            items.append(('SPHERE', "Sphere", "Sphere field (fallback)"))
        
        return items
    
    @staticmethod
    def get_cloner_enum_items(self, context):
        """Динамическое получение списка клонеров из реестра"""
        from ...core.registry.cloner_registry import list_cloner_types, get_cloner_class
        
        items = []
        cloner_types = list_cloner_types()
        
        for cloner_type in cloner_types:
            cloner_class = get_cloner_class(cloner_type)
            if cloner_class:
                display_name = getattr(cloner_class, 'bl_label', cloner_type.title())
                description = f"{display_name} cloner"
                items.append((cloner_type, display_name, description))
        
        # Fallback если реестр пуст
        if not items:
            items.append(('GRID', "Grid", "Grid cloner (fallback)"))
        
        return items


# Удобные функции для использования в других модулях
def draw_effector_ui_universal(layout, modifier, effector_type):
    """Универсальная отрисовка UI эффектора"""
    UniversalComponentUI.draw_component_ui(layout, modifier, effector_type, "effector")


def draw_field_ui_universal(layout, modifier, field_type):
    """Универсальная отрисовка UI поля"""
    UniversalComponentUI.draw_component_ui(layout, modifier, field_type, "field")


def draw_cloner_ui_universal(layout, modifier, cloner_type):
    """Универсальная отрисовка UI клонера"""
    UniversalComponentUI.draw_component_ui(layout, modifier, cloner_type, "cloner")
