bl_info = {
    "name": "<PERSON>lonerPro",
    "description": "Professional cloning tools for Blender - Simplified Architecture",
    "author": "ClonerPro Team",
    "version": (1, 0, 0),
    "blender": (4, 0, 0),
    "location": "View3D > Sidebar > ClonerPro",
    "category": "Mesh",
    "support": "COMMUNITY",
}

import bpy
from . import ui
from . import core
from . import components
from .core.system.logging import log_info, log_error, log_success, log_operation_start, log_operation_complete


def register():
    """Register all addon components"""
    log_operation_start("ClonerPro registration", "ADDON")
    
    # Initialize safety systems first
    try:
        from .core.system.dependency_safety import register_dependency_safety_handlers
        
        log_info("Initializing safety systems", "SAFETY")
        register_dependency_safety_handlers()
        log_success("Safety systems initialized", "SAFETY")
        
    except Exception as e:
        log_error("Failed to initialize safety systems", "SAFETY", e)
        import traceback
        traceback.print_exc()
        # Continue registration without safety systems (not ideal but better than crash)
    
    # Register UI properties FIRST (required for panels)
    register_ui_properties()

    # Register custom icons
    log_info("Loading custom icons", "ICONS")
    from .ui.icons import register as register_icons
    register_icons()

    # Register core components
    log_info("Registering core components", "CORE")
    core.register()
    
    # Configurations removed - using class-based architecture
    
    # Register components
    if hasattr(components, 'register'):
        log_info("Registering components", "COMPONENTS")
        components.register()
    
    # Register UI components
    log_info("Registering UI components", "UI")
    ui.register()
    
    # Register field UI properties
    try:
        from .ui.generators.field_ui import register_field_ui_properties
        register_field_ui_properties()
        log_success("Field UI properties registered", "UI")
    except Exception as e:
        log_error("Failed to register field UI properties", "UI", e)
    
    # Register field synchronization service
    try:
        from .core.services.field_synchronization import register_field_synchronization
        register_field_synchronization()
        log_success("Field synchronization service registered", "FIELDS")
    except Exception as e:
        log_error("Failed to register field synchronization service", "FIELDS", e)
    
    # Setup additional monitoring systems
    try:
        from .core.cleanup.effector_cleanup import setup_effector_deletion_monitoring
        setup_effector_deletion_monitoring()
        log_success("Effector cleanup monitoring enabled", "CLEANUP")
    except Exception as e:
        log_error("Effector cleanup setup failed", "CLEANUP", e)
    
    log_operation_complete("ClonerPro registration", "ADDON")


def register_ui_properties():
    """Register UI properties required for panels"""
    from bpy.props import BoolProperty

    # Section collapse states for improved UI organization
    bpy.types.Scene.clonerpro_browser_section_expanded = BoolProperty(
        name="Browser Section Expanded",
        description="Controls if the browser section is expanded or collapsed",
        default=True
    )
    
    bpy.types.Scene.clonerpro_settings_section_expanded = BoolProperty(
        name="Settings Section Expanded",
        description="Controls if the settings section is expanded or collapsed", 
        default=True
    )
    
    bpy.types.Scene.clonerpro_creation_section_expanded = BoolProperty(
        name="Creation Section Expanded",
        description="Controls if the creation section is expanded or collapsed",
        default=True
    )
    
    # Effector section states
    bpy.types.Scene.clonerpro_effector_creation_expanded = BoolProperty(
        name="Effector Creation Section Expanded",
        description="Controls if the effector creation section is expanded or collapsed",
        default=False
    )
    
    bpy.types.Scene.clonerpro_effector_settings_expanded = BoolProperty(
        name="Effector Settings Section Expanded",
        description="Controls if the effector settings section is expanded or collapsed",
        default=True
    )
    
    # Field section states
    bpy.types.Scene.clonerpro_field_creation_expanded = BoolProperty(
        name="Field Creation Section Expanded",
        description="Controls if the field creation section is expanded or collapsed",
        default=False
    )
    
    bpy.types.Scene.clonerpro_field_settings_expanded = BoolProperty(
        name="Field Settings Section Expanded",
        description="Controls if the field settings section is expanded or collapsed",
        default=True
    )
    
    # Tab system properties
    bpy.types.Scene.clonerpro_active_tab = bpy.props.EnumProperty(
        name="Active Tab",
        description="Currently active tab in ClonerPro interface",
        items=[
            ('CREATE', "Create", "Create new cloners, effectors, and fields"),
            ('MANAGE', "Manage", "Browse and manage existing elements")
        ],
        default='CREATE'
    )
    
    # New tabbed section states
    bpy.types.Scene.clonerpro_cloners_creation_expanded = BoolProperty(
        name="Cloners Creation Section Expanded",
        description="Controls if the cloners creation section is expanded or collapsed in tabbed view",
        default=True
    )

    bpy.types.Scene.clonerpro_effectors_creation_expanded = BoolProperty(
        name="Effectors Creation Section Expanded",
        description="Controls if the effectors creation section is expanded or collapsed in tabbed view",
        default=True  # Изменено с False на True
    )

    bpy.types.Scene.clonerpro_fields_creation_expanded = BoolProperty(
        name="Fields Creation Section Expanded",
        description="Controls if the fields creation section is expanded or collapsed in tabbed view",
        default=True  # Изменено с False на True
    )

    # === НОВЫЕ СВОЙСТВА ДЛЯ УЛУЧШЕННОЙ НАВИГАЦИИ ===

    # Фильтры для управления компонентами
    bpy.types.Scene.clonerpro_filter_cloners = BoolProperty(
        name="Show Cloners",
        description="Show cloners in browser",
        default=True
    )

    bpy.types.Scene.clonerpro_filter_effectors = BoolProperty(
        name="Show Effectors",
        description="Show effectors in browser",
        default=True
    )

    bpy.types.Scene.clonerpro_filter_fields = BoolProperty(
        name="Show Fields",
        description="Show fields in browser",
        default=True
    )

    # Поиск по компонентам
    bpy.types.Scene.clonerpro_search_text = bpy.props.StringProperty(
        name="Search",
        description="Search components by name",
        default=""
    )

    # Активная категория пресетов
    bpy.types.Scene.clonerpro_active_preset_category = bpy.props.StringProperty(
        name="Active Preset Category",
        description="Currently active preset category",
        default="ARCHITECTURAL"
    )


def unregister_ui_properties():
    """Unregister UI properties"""
    try:
        del bpy.types.Scene.clonerpro_browser_section_expanded
        del bpy.types.Scene.clonerpro_settings_section_expanded
        del bpy.types.Scene.clonerpro_creation_section_expanded
        del bpy.types.Scene.clonerpro_effector_creation_expanded
        del bpy.types.Scene.clonerpro_effector_settings_expanded
        del bpy.types.Scene.clonerpro_field_creation_expanded
        del bpy.types.Scene.clonerpro_field_settings_expanded
        # Tab system properties
        del bpy.types.Scene.clonerpro_active_tab
        del bpy.types.Scene.clonerpro_cloners_creation_expanded
        del bpy.types.Scene.clonerpro_effectors_creation_expanded
        del bpy.types.Scene.clonerpro_fields_creation_expanded
        # Новые свойства
        del bpy.types.Scene.clonerpro_filter_cloners
        del bpy.types.Scene.clonerpro_filter_effectors
        del bpy.types.Scene.clonerpro_filter_fields
        del bpy.types.Scene.clonerpro_search_text
        del bpy.types.Scene.clonerpro_active_preset_category
    except AttributeError:
        # Properties were not registered or already removed
        pass


def unregister():
    """Unregister all addon components"""
    log_operation_start("ClonerPro unregistration", "ADDON")
    
    # Safely disable safety systems first
    try:
        from .core.system.dependency_safety import unregister_dependency_safety_handlers, emergency_reset_all_locks
        
        log_info("Disabling safety systems", "SAFETY")
        unregister_dependency_safety_handlers()
        emergency_reset_all_locks()
        log_success("Safety systems disabled", "SAFETY")
        
    except Exception as e:
        log_error("Error disabling safety systems", "SAFETY", e)
    
    # Disable monitoring systems
    try:
        from .core.cleanup.effector_cleanup import remove_effector_deletion_monitoring
        remove_effector_deletion_monitoring()
        log_success("Effector cleanup monitoring disabled", "CLEANUP")
    except Exception as e:
        log_error("Effector cleanup removal failed", "CLEANUP", e)
    
    # Unregister field UI properties
    try:
        from .ui.generators.field_ui import unregister_field_ui_properties
        unregister_field_ui_properties()
        log_success("Field UI properties unregistered", "UI")
    except Exception as e:
        log_error("Failed to unregister field UI properties", "UI", e)
    
    # Unregister field synchronization service
    try:
        from .core.services.field_synchronization import unregister_field_synchronization
        unregister_field_synchronization()
        log_success("Field synchronization service unregistered", "FIELDS")
    except Exception as e:
        log_error("Failed to unregister field synchronization service", "FIELDS", e)
    
    # Unregister in reverse order
    log_info("Unregistering UI components", "UI")
    ui.unregister()
    
    if hasattr(components, 'unregister'):
        log_info("Unregistering components", "COMPONENTS")
        components.unregister()
        
    # Configurations removed - using class-based architecture
        
    log_info("Unregistering core components", "CORE")
    core.unregister()

    # Unregister custom icons
    log_info("Unloading custom icons", "ICONS")
    from .ui.icons import unregister as unregister_icons
    unregister_icons()

    # Unregister UI properties LAST
    unregister_ui_properties()
    
    log_operation_complete("ClonerPro unregistration", "ADDON")


if __name__ == "__main__":
    register()