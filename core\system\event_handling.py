"""
Унифицированная система управления событиями для ClonerPro
Портировано из advanced_cloners/core/utils/event_handling/unified_event_manager.py

КРИТИЧНО: Эта система предотвращает конфликты обработчиков событий и краши
"""

import bpy
import time
from typing import Dict, Set, Optional, Callable, List
from .error_handling import get_error_manager


class UnifiedEventManager:
    """
    Централизованная система управления обработчиками событий.
    
    Этот класс объединяет множественные обработчики событий в одну
    эффективную систему с правильной обработкой ошибок и оптимизацией производительности.
    """
    
    # Singleton экземпляр
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        
        # Состояние обработчиков событий
        self._handlers_registered = False
        self._last_update_time = 0.0
        self._update_cooldown = 0.1  # 100мс минимальный cooldown для предотвращения бесконечных циклов
        self._handler_blocked = False
        
        # Отслеживание производительности
        self._effector_last_parameters: Dict[str, Dict[str, any]] = {}
        self._dependency_state: Dict[str, any] = {}
        
        # Отслеживание регистрации обработчиков
        self._registered_handlers: Set[str] = set()
    
    def register_all_handlers(self):
        """Регистрирует все обработчики событий с правильной обработкой ошибок."""
        if self._handlers_registered:
            return
        
        try:
            # Регистрируем основные обработчики
            self._register_core_handlers()
            
            # Регистрируем обработчики безопасности dependency
            self._register_dependency_handlers()
            
            # Регистрируем обработчики оптимизации
            self._register_optimization_handlers()
            
            self._handlers_registered = True
            print("[INFO] All event handlers registered successfully")
            
        except Exception as e:
            get_error_manager().log_operation_result("Event Handler Registration", False, str(e))
    
    def unregister_all_handlers(self):
        """Безопасно отменяет регистрацию всех обработчиков событий."""
        if not self._handlers_registered:
            return
        
        try:
            # Удаляем все зарегистрированные обработчики
            for handler_name in list(self._registered_handlers):
                self._unregister_handler(handler_name)
            
            self._handlers_registered = False
            self._registered_handlers.clear()
            print("[INFO] All event handlers unregistered successfully")
            
        except Exception as e:
            get_error_manager().log_operation_result("Event Handler Unregistration", False, str(e))
    
    def _register_core_handlers(self):
        """Регистрирует основные обработчики событий."""
        # Главный обработчик обновлений - заменяет optimized_event_handler
        if self._register_handler('depsgraph_update_post', self._unified_update_handler):
            print("[DEBUG] Core update handler registered")
    
    def _register_dependency_handlers(self):
        """Регистрирует обработчики безопасности dependency."""
        # Обработчики безопасности Undo
        if self._register_handler('undo_pre', self._undo_pre_safety_handler):
            print("[DEBUG] Undo pre-safety handler registered")
        
        if self._register_handler('undo_post', self._undo_post_safety_handler):
            print("[DEBUG] Undo post-safety handler registered")
    
    def _register_optimization_handlers(self):
        """Регистрирует обработчики оптимизации и кэширования."""
        # Обработчики загрузки для управления кэшем
        if self._register_handler('load_post', self._load_post_handler):
            print("[DEBUG] Load post handler registered")
    
    def _register_handler(self, handler_type: str, handler_func: Callable) -> bool:
        """
        Регистрирует один обработчик с обработкой ошибок.
        
        Args:
            handler_type: Тип обработчика ('depsgraph_update_post', 'undo_pre', etc.)
            handler_func: Функция обработчика
            
        Returns:
            bool: True если регистрация успешна
        """
        try:
            handler_collection = getattr(bpy.app.handlers, handler_type)
            
            # Проверяем, не зарегистрирован ли уже
            if handler_func not in handler_collection:
                handler_collection.append(handler_func)
                self._registered_handlers.add(f"{handler_type}:{handler_func.__name__}")
                return True
            
            return False
            
        except Exception as e:
            get_error_manager().log_operation_result(
                f"Handler Registration ({handler_type})", 
                False, 
                str(e)
            )
            return False
    
    def _unregister_handler(self, handler_name: str):
        """Отменяет регистрацию определенного обработчика."""
        try:
            handler_type, func_name = handler_name.split(':')
            handler_collection = getattr(bpy.app.handlers, handler_type)
            
            # Находим и удаляем обработчик
            for handler in list(handler_collection):
                if hasattr(handler, '__name__') and handler.__name__ == func_name:
                    handler_collection.remove(handler)
                    break
            
            self._registered_handlers.discard(handler_name)
            
        except Exception as e:
            get_error_manager().log_operation_result(f"Handler Unregistration ({handler_name})", False, str(e))
    
    @bpy.app.handlers.persistent
    def _unified_update_handler(self, scene, depsgraph):
        """
        КРИТИЧЕСКИЙ ОБРАБОТЧИК: Унифицированный обработчик обновлений, заменяющий множественные отдельные обработчики.
        
        Этот обработчик объединяет:
        - Обновления параметров эффекторов
        - Изменения состояния компонентов
        - Обновления браузера
        - Оптимизации производительности
        """
        current_time = time.time()
        
        # Ограничение частоты с минимальным cooldown
        if current_time - self._last_update_time < self._update_cooldown:
            return
        
        # Предотвращаем рекурсивные вызовы
        if self._handler_blocked:
            return
        
        self._handler_blocked = True
        
        try:
            # Быстрая предварительная проверка для эффективности
            if not depsgraph.id_type_updated('OBJECT'):
                return
            
            # Собираем все релевантные изменения за один проход
            changes = self._collect_unified_changes(depsgraph)
            
            if changes:
                # Эффективно обрабатываем изменения
                self._process_unified_changes(changes)
                self._last_update_time = current_time
        
        except Exception as e:
            get_error_manager().handle_component_error("EventHandler", "unified_update", e)
        
        finally:
            self._handler_blocked = False
    
    def _collect_unified_changes(self, depsgraph) -> Dict[str, any]:
        """
        Собирает все релевантные изменения за один проход depsgraph.
        
        Returns:
            Словарь с категоризированными изменениями
        """
        changes = {
            'effectors': {},
            'cloners': {},
            'ui_updates_needed': False
        }
        
        try:
            for update in depsgraph.updates:
                if not hasattr(update.id, 'original'):
                    continue
                
                obj = update.id.original
                if not isinstance(obj, bpy.types.Object) or not hasattr(obj, 'modifiers'):
                    continue
                
                # Проверяем модификаторы на изменения
                for mod in obj.modifiers:
                    if self._is_effector_modifier(mod):
                        if self._check_parameter_changes(obj, mod, 'effector'):
                            changes['effectors'][f"{obj.name}:{mod.name}"] = {
                                'object': obj,
                                'modifier': mod,
                                'type': 'effector'
                            }
                            changes['ui_updates_needed'] = True
                    
                    elif self._is_cloner_modifier(mod):
                        if self._check_parameter_changes(obj, mod, 'cloner'):
                            changes['cloners'][f"{obj.name}:{mod.name}"] = {
                                'object': obj,
                                'modifier': mod,
                                'type': 'cloner'
                            }
                            changes['ui_updates_needed'] = True
        
        except Exception as e:
            get_error_manager().log_operation_result("Change Collection", False, str(e))
        
        return changes
    
    def _process_unified_changes(self, changes: Dict[str, any]):
        """Эффективно обрабатывает собранные изменения."""
        try:
            # Обрабатываем изменения эффекторов
            if changes['effectors']:
                self._process_effector_changes(changes['effectors'])
            
            # Обрабатываем изменения клонеров
            if changes['cloners']:
                self._process_cloner_changes(changes['cloners'])
            
            # Обновляем UI при необходимости
            if changes['ui_updates_needed']:
                self._trigger_ui_updates()
        
        except Exception as e:
            get_error_manager().log_operation_result("Change Processing", False, str(e))
    
    def _process_effector_changes(self, effector_changes: Dict[str, any]):
        """Обрабатывает изменения, специфичные для эффекторов."""
        for key, change_info in effector_changes.items():
            try:
                obj = change_info['object']
                mod = change_info['modifier']
                
                # Запускаем зависимые обновления
                self._trigger_dependent_updates(obj, mod)
                
            except Exception as e:
                get_error_manager().handle_modifier_error(key, e, "effector change processing")
    
    def _process_cloner_changes(self, cloner_changes: Dict[str, any]):
        """Обрабатывает изменения, специфичные для клонеров."""
        for key, change_info in cloner_changes.items():
            try:
                obj = change_info['object']
                mod = change_info['modifier']
                
                # Обновляем состояние клонера
                self._update_cloner_state(obj, mod)
                
            except Exception as e:
                get_error_manager().handle_modifier_error(key, e, "cloner change processing")
    
    def _trigger_ui_updates(self):
        """Запускает необходимые обновления UI."""
        try:
            # Предотвращаем чрезмерные обновления UI - запускаем только если прошло достаточно времени
            current_time = time.time()
            if not hasattr(self, '_last_ui_update_time'):
                self._last_ui_update_time = 0.0
            
            if current_time - self._last_ui_update_time < 0.5:  # 500мс минимум между обновлениями UI
                return
            
            self._last_ui_update_time = current_time
            
            # Обновляем браузер при необходимости (но не форсируем циклы обновления)
            if hasattr(bpy.context, 'scene'):
                scene = bpy.context.scene
                # Устанавливаем флаг обновления только если он не установлен для предотвращения циклов
                if hasattr(scene, 'clonerpro_browser_needs_refresh'):
                    if not scene.get('clonerpro_browser_needs_refresh', False):
                        scene['clonerpro_browser_needs_refresh'] = True
        
        except Exception as e:
            get_error_manager().log_operation_result("UI Update Trigger", False, str(e))
    
    def _is_effector_modifier(self, mod) -> bool:
        """Проверяет, является ли модификатор эффектором."""
        try:
            if mod.type != 'NODES' or not mod.node_group:
                return False
            
            # Проверяем метаданные
            component_type = mod.node_group.get("component_type")
            if component_type == 'EFFECTOR':
                return True
            
            # Fallback: проверяем по имени
            name_lower = mod.node_group.name.lower()
            return any(pattern in name_lower for pattern in ['effector', 'random', 'noise', 'delay'])
        
        except:
            return False
    
    def _is_cloner_modifier(self, mod) -> bool:
        """Проверяет, является ли модификатор клонером."""
        try:
            if mod.type != 'NODES' or not mod.node_group:
                return False
            
            # Проверяем метаданные
            component_type = mod.node_group.get("component_type")
            if component_type == 'CLONER':
                return True
            
            # Fallback: проверяем по имени
            name_lower = mod.node_group.name.lower()
            return any(pattern in name_lower for pattern in ['cloner', 'grid', 'linear', 'circle'])
        
        except:
            return False
    
    def _check_parameter_changes(self, obj, mod, component_type: str) -> bool:
        """Проверяет, изменились ли параметры компонента."""
        try:
            # Получаем текущие параметры
            current_params = self._get_modifier_parameters(mod)
            
            # Сравниваем с сохраненными параметрами
            key = f"{obj.name}:{mod.name}"
            stored_params = self._effector_last_parameters.get(key, {})
            
            # Проверяем на изменения
            if current_params != stored_params:
                self._effector_last_parameters[key] = current_params.copy()
                return True
            
            return False
        
        except Exception as e:
            get_error_manager().handle_parameter_error(f"{obj.name}:{mod.name}", e, "change detection")
            return False
    
    def _get_modifier_parameters(self, mod) -> Dict[str, any]:
        """Получает все параметры из модификатора."""
        params = {}
        
        try:
            if mod.type == 'NODES' and mod.node_group:
                for item in mod.node_group.interface.items_tree:
                    if item.item_type == 'SOCKET' and item.in_out == 'INPUT':
                        try:
                            # Получаем значение параметра
                            param_value = mod[f'["{item.identifier}"]']
                            params[item.name] = param_value
                        except:
                            # Параметр может быть недоступен
                            pass
        
        except Exception as e:
            get_error_manager().handle_modifier_error(mod.name, e, "parameter extraction")
        
        return params
    
    def _trigger_dependent_updates(self, obj, mod):
        """Запускает обновления для зависимых компонентов."""
        # Это бы реализовало логику для обновления зависимых клонеров/эффекторов
        pass
    
    def _update_cloner_state(self, obj, mod):
        """Обновляет внутреннее состояние для клонера."""
        # Это бы реализовало управление состоянием клонера
        pass
    
    @bpy.app.handlers.persistent
    def _undo_pre_safety_handler(self, scene, *args):
        """Обрабатывает операции безопасности pre-undo."""
        try:
            print("[DEBUG] Preparing for safe undo operation")
            
            # Устанавливаем флаги подготовки undo
            if hasattr(bpy.context, 'scene'):
                bpy.context.scene['clonerpro_undo_in_progress'] = True
            
            # Временно отключаем проблематичные соединения
            self._prepare_for_undo()
        
        except Exception as e:
            get_error_manager().log_operation_result("Undo Pre-Safety", False, str(e))
    
    @bpy.app.handlers.persistent
    def _undo_post_safety_handler(self, scene, *args):
        """Обрабатывает операции восстановления post-undo."""
        try:
            pass  # Post-undo recovery
            
            # Очищаем флаги undo
            if hasattr(bpy.context, 'scene'):
                bpy.context.scene['clonerpro_undo_in_progress'] = False
                bpy.context.scene['clonerpro_forced_refresh'] = True
            
            # Очищаем кэши
            self._effector_last_parameters.clear()
            self._dependency_state.clear()
            
            # Запускаем полное обновление системы
            self._post_undo_recovery()
        
        except Exception as e:
            get_error_manager().log_operation_result("Undo Post-Safety", False, str(e))
    
    @bpy.app.handlers.persistent
    def _load_post_handler(self, dummy):
        """Обрабатывает операции post-load."""
        try:
            print("[DEBUG] Executing post-load initialization")
            
            # Очищаем все кэшированные данные
            self._effector_last_parameters.clear()
            self._dependency_state.clear()
            
            # Инициализируем состояние системы
            self._initialize_post_load()
        
        except Exception as e:
            get_error_manager().log_operation_result("Load Post Handler", False, str(e))
    
    def _prepare_for_undo(self):
        """Подготавливает систему для операции undo."""
        # Реализация для подготовки undo
        pass
    
    def _post_undo_recovery(self):
        """Восстанавливает состояние системы после undo."""
        # Реализация для восстановления post-undo
        pass
    
    def _initialize_post_load(self):
        """Инициализирует систему после загрузки файла."""
        # Реализация для инициализации post-load
        pass


# Глобальный экземпляр
event_manager = UnifiedEventManager()


# Публичные API функции для управления регистрацией
def register_event_handlers():
    """Регистрирует все обработчики событий."""
    event_manager.register_all_handlers()


def unregister_event_handlers():
    """Отменяет регистрацию всех обработчиков событий."""
    event_manager.unregister_all_handlers()


def is_handler_registered() -> bool:
    """Проверяет, зарегистрированы ли обработчики в данный момент."""
    return event_manager._handlers_registered


def force_update_all_handlers():
    """Принудительно обновляет все обработчики."""
    try:
        # Очищаем все кэши
        event_manager._effector_last_parameters.clear()
        event_manager._dependency_state.clear()
        
        # Сбрасываем таймеры
        event_manager._last_update_time = 0.0
        if hasattr(event_manager, '_last_ui_update_time'):
            event_manager._last_ui_update_time = 0.0
        
        pass  # Handlers updated
        
    except Exception as e:
        get_error_manager().log_operation_result("Force Update Handlers", False, str(e))


def get_handler_status() -> Dict[str, any]:
    """Получает статус всех обработчиков."""
    return {
        'registered': event_manager._handlers_registered,
        'registered_count': len(event_manager._registered_handlers),
        'handlers': list(event_manager._registered_handlers),
        'blocked': event_manager._handler_blocked,
        'last_update': event_manager._last_update_time
    }


# Публичный API
__all__ = [
    'UnifiedEventManager',
    'event_manager',
    'register_event_handlers',
    'unregister_event_handlers',
    'is_handler_registered',
    'force_update_all_handlers',
    'get_handler_status'
]