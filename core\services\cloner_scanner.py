"""
Cloner Discovery System для ClonerPro
Упрощенная версия системы обнаружения клонеров из Advanced Cloners
"""

import bpy


class ClonerDiscovery:
    """Система обнаружения клонеров в сцене"""
    
    @staticmethod
    def scan_scene_cloners(context):
        """
        ЗАЩИЩЁННОЕ сканирование клонеров - ни один клонер не должен потеряться!
        
        Многоуровневая защита:
        1. Поиск по всем объектам сцены
        2. Поиск по всем коллекциям
        3. Защита от ошибок доступа
        4. Дедупликация результатов
        """
        cloners = []
        processed_modifiers = set()  # Защита от дублей
        
        # УРОВЕНЬ 1: Сканируем все объекты в сцене
        try:
            scene_objects = list(context.scene.objects)  # Копия для защиты от изменений
            
            for obj in scene_objects:
                try:
                    cloner_modifiers = ClonerDiscovery.get_cloner_modifiers(obj)
                    for modifier in cloner_modifiers:
                        # Уникальный идентификатор модификатора
                        modifier_id = f"{obj.name}::{modifier.name}"
                        
                        if modifier_id not in processed_modifiers:
                            cloner_info = ClonerDiscovery.analyze_cloner_modifier(obj, modifier)
                            if cloner_info:
                                cloners.append(cloner_info)
                                processed_modifiers.add(modifier_id)
                
                except Exception as e:
                    print(f"⚠️ [SCANNER] Error scanning object {getattr(obj, 'name', 'UNKNOWN')}: {e}")
                    continue
        
        except Exception as e:
            print(f"⚠️ [SCANNER] Error accessing scene objects: {e}")
        
        # УРОВЕНЬ 2: Дополнительный поиск по коллекциям (на случай скрытых объектов)
        try:
            def scan_collection_recursive(collection):
                for obj in collection.objects:
                    try:
                        cloner_modifiers = ClonerDiscovery.get_cloner_modifiers(obj)
                        for modifier in cloner_modifiers:
                            modifier_id = f"{obj.name}::{modifier.name}"
                            
                            if modifier_id not in processed_modifiers:
                                cloner_info = ClonerDiscovery.analyze_cloner_modifier(obj, modifier)
                                if cloner_info:
                                    cloners.append(cloner_info)
                                    processed_modifiers.add(modifier_id)
                    
                    except Exception as e:
                        continue
                
                # Рекурсивный поиск в дочерних коллекциях
                for child_collection in collection.children:
                    try:
                        scan_collection_recursive(child_collection)
                    except Exception as e:
                        continue
            
            # Сканируем основную коллекцию сцены
            if hasattr(context.scene, 'collection') and context.scene.collection:
                scan_collection_recursive(context.scene.collection)
        
        except Exception as e:
            print(f"⚠️ [SCANNER] Error in collection scan: {e}")
        
        # УРОВЕНЬ 3: Поиск по bpy.data.objects (последний резерв) с проверкой View Layer
        try:
            for obj in bpy.data.objects:
                try:
                    # КРИТИЧНО: Проверяем, что объект действительно доступен в текущем View Layer
                    if not ClonerDiscovery._is_object_in_current_view_layer(context, obj):
                        continue
                    
                    cloner_modifiers = ClonerDiscovery.get_cloner_modifiers(obj)
                    for modifier in cloner_modifiers:
                        modifier_id = f"{obj.name}::{modifier.name}"
                        
                        if modifier_id not in processed_modifiers:
                            cloner_info = ClonerDiscovery.analyze_cloner_modifier(obj, modifier)
                            if cloner_info:
                                cloners.append(cloner_info)
                                processed_modifiers.add(modifier_id)
                
                except Exception as e:
                    continue
        
        except Exception as e:
            print(f"⚠️ [SCANNER] Error in data.objects scan: {e}")
        
        # Ограниченное логирование - только при изменениях
        if hasattr(ClonerDiscovery, '_last_cloner_count'):
            if ClonerDiscovery._last_cloner_count != len(cloners):
                print(f"🎯 [SCANNER] Cloners changed: {ClonerDiscovery._last_cloner_count} → {len(cloners)}")
                ClonerDiscovery._last_cloner_count = len(cloners)
        else:
            ClonerDiscovery._last_cloner_count = len(cloners)
            if len(cloners) > 0:
                print(f"🎯 [SCANNER] Initial scan: {len(cloners)} cloners")
        return cloners
    
    @staticmethod
    def get_cloner_modifiers(obj):
        """Получить все модификаторы клонеров на объекте с защитой от ошибок"""
        cloner_modifiers = []
        
        try:
            # Защита от объектов без модификаторов
            if not hasattr(obj, 'modifiers') or not obj.modifiers:
                return cloner_modifiers
            
            # Создаём копию списка модификаторов для защиты от изменений во время итерации
            modifiers_copy = list(obj.modifiers)
            
            for modifier in modifiers_copy:
                try:
                    # Проверяем что модификатор ещё существует
                    if not modifier:
                        continue
                    
                    # Защищённая проверка типа и node_group
                    if (modifier.type == 'NODES' and 
                        hasattr(modifier, 'node_group') and 
                        modifier.node_group):
                        
                        if ClonerDiscovery.is_cloner_modifier(modifier):
                            cloner_modifiers.append(modifier)
                
                except (ReferenceError, AttributeError) as e:
                    # Модификатор был удалён или повреждён
                    continue
                except Exception as e:
                    continue
        
        except Exception as e:
            print(f"⚠️ [SCANNER] Error getting modifiers from {getattr(obj, 'name', 'UNKNOWN')}: {e}")
        
        return cloner_modifiers
    
    @staticmethod
    def is_cloner_modifier(modifier):
        """Защищённая проверка, является ли модификатор клонером"""
        try:
            # Базовая проверка node_group
            if not modifier.node_group:
                return False
            
            # Защищённое получение имени node group
            try:
                node_group_name = modifier.node_group.name
            except (ReferenceError, AttributeError):
                # Node group была удалена
                return False
            
            # ClonerPro клонеры - расширенный список паттернов
            cloner_node_patterns = [
                # Unified система (новые имена)
                "GridCloner3D_Advanced",    # Grid Object mode
                "GridClonerStacked",        # Grid Stacked mode  
                "GridClonerCollection",     # Grid Collection mode
                "LinearCloner3D_Advanced",  # Linear Object mode
                "LinearClonerStacked",      # Linear Stacked mode
                "LinearClonerCollection",   # Linear Collection mode
                "CircleCloner3D_Advanced",  # Circle Object mode
                "CircleClonerStacked",      # Circle Stacked mode
                "CircleClonerCollection",   # Circle Collection mode
                "SpiralCloner3D_Advanced",  # Spiral Object mode
                "SpiralClonerStacked",      # Spiral Stacked mode
                "SpiralClonerCollection",   # Spiral Collection mode
                
                
                # Mesh система
                "ObjectCloner3D_Advanced",  # ObjectCloner (базовое имя)
                "ObjectCloner",             # ObjectCloner из standalone
                "SplineCloner3D_Advanced",  # SplineCloner (новый)
                "CurvesCloner",             # Curves клонер (будущее)
                "VolumeCloner",             # Volume клонер (будущее)
                
                # Эффекторы
                "RandomEffector",           # Random Effector
                "NoiseEffector",            # Noise Effector
                "DelayEffector",            # Delay Effector
                "StepEffector",             # Step Effector
            ]
            
            # Проверка по паттернам имён
            for pattern in cloner_node_patterns:
                if pattern in node_group_name:
                    return True
            
            # Дополнительная проверка для ObjectCloner с уникальными именами
            if node_group_name.startswith("ObjectCloner3D_Advanced_"):
                return True
            
            # Дополнительная проверка для SplineCloner с уникальными именами
            if node_group_name.startswith("SplineCloner3D_Advanced_"):
                return True
            
            # МЕТА-СИСТЕМА: Проверяем метаданные ClonerPro (самый надёжный способ)
            # Используем новую систему метаданных из core.py
            try:
                cloner_type = modifier.get("cloner_type")
                if cloner_type and cloner_type in ["GRID", "LINEAR", "CIRCLE", "SPIRAL", "OBJECT", "SPLINE", "CURVES", "VOLUME"]:
                    return True
                
                # Проверяем системные флаги
                if modifier.get("is_unified_cloner") or modifier.get("is_mesh_cloner"):
                    return True
                
                # Проверяем режимные флаги  
                if (modifier.get("is_stacked_mode") or 
                    modifier.get("is_collection_mode") or 
                    modifier.get("is_object_mode")):
                    return True
                
                # Проверяем cloner_mode
                cloner_mode = modifier.get("cloner_mode")
                if cloner_mode and cloner_mode in ["OBJECT", "STACKED", "COLLECTION"]:
                    return True
                
                # Проверяем cloner_system
                cloner_system = modifier.get("cloner_system")
                if cloner_system and cloner_system in ["unified", "mesh"]:
                    return True
                
                # Legacy проверки (для совместимости)
                if modifier.get("is_stacked_cloner"):
                    return True
                
                source_type = modifier.get("source_type")
                if source_type and source_type in ["OBJECT", "COLLECTION"]:
                    return True
            
            except Exception as e:
                # Ошибка доступа к метаданным - пропускаем
                pass
            
            return False
        
        except Exception as e:
            print(f"⚠️ [SCANNER] Error in is_cloner_modifier: {e}")
            return False
    
    @staticmethod
    def analyze_cloner_modifier(obj, modifier):
        """Анализировать модификатор клонера и извлечь информацию"""
        if not modifier.node_group:
            return None
        
        node_group_name = modifier.node_group.name
        
        # Определяем тип клонера и режим
        cloner_type, cloner_mode = ClonerDiscovery.detect_cloner_type_and_mode(modifier)
        
        if not cloner_type:
            return None
        
        # Определяем имя оригинального объекта с учетом стековых клонеров
        original_object = modifier.get("original_object", "")
        if not original_object:
            # Для стековых клонеров или случаев когда original_object не задан
            if cloner_mode == "STACKED":
                original_object = obj.name  # Стековый клонер - сам объект является оригиналом
            else:
                original_object = "Object"  # Более нейтральное название вместо "Unknown"
        
        # Собираем информацию о клонере
        cloner_info = {
            'object': obj,
            'modifier': modifier,
            'cloner_type': cloner_type,
            'cloner_mode': cloner_mode,
            'node_group': modifier.node_group,
            'node_group_name': node_group_name,
            'display_name': f"{cloner_type} ({cloner_mode})",
            'original_object': original_object,
            'cloner_collection': modifier.get("cloner_collection", "None"),
            # КРИТИЧЕСКИ ВАЖНО: Добавляем ключи для UI кнопок
            'object_name': obj.name,
            'modifier_name': modifier.name,
            'visible': modifier.show_viewport
        }
        
        return cloner_info
    
    @staticmethod
    def detect_cloner_type_and_mode(modifier):
        """Определить тип клонера и режим работы"""
        if not modifier.node_group:
            return None, None
        
        node_group_name = modifier.node_group.name
        
        # Определяем тип клонера из метаданных (более надежно)
        cloner_type = modifier.get("cloner_type", None)
        cloner_mode = modifier.get("cloner_mode", None)
        
        # Если метаданные есть, используем их
        if cloner_type and cloner_mode:
            return cloner_type, cloner_mode
        
        # Fallback: определяем по имени node group
        if "GridCloner" in node_group_name:
            cloner_type = "GRID"
            
            # Определяем режим по имени группы
            if "Stacked" in node_group_name:
                cloner_mode = "STACKED"
            elif "Collection" in node_group_name:
                cloner_mode = "COLLECTION"
            else:
                cloner_mode = "OBJECT"
            
            return cloner_type, cloner_mode
            
        elif "LinearCloner" in node_group_name:
            cloner_type = "LINEAR"
            
            # Определяем режим по имени группы
            if "Stacked" in node_group_name:
                cloner_mode = "STACKED"
            elif "Collection" in node_group_name:
                cloner_mode = "COLLECTION"
            else:
                cloner_mode = "OBJECT"
            
            return cloner_type, cloner_mode
            
        elif "CircleCloner" in node_group_name:
            cloner_type = "CIRCLE"
            
            # Определяем режим по имени группы
            if "Stacked" in node_group_name:
                cloner_mode = "STACKED"
            elif "Collection" in node_group_name:
                cloner_mode = "COLLECTION"
            else:
                cloner_mode = "OBJECT"
            
            return cloner_type, cloner_mode
            
        elif ("ObjectCloner" in node_group_name or 
              node_group_name.startswith("ObjectCloner3D_Advanced_")):  # <- Поддержка уникальных имён
            cloner_type = "OBJECT"
            
            # ПРИОРИТЕТ 1: Метаданные (наиболее надежно)
            cloner_mode = modifier.get("cloner_mode")
            if cloner_mode in ["OBJECT", "COLLECTION"]:
                return cloner_type, cloner_mode
            
            # ПРИОРИТЕТ 2: Socket mapping система (новая)
            source_mode = None
            if modifier.node_group:
                # Ищем Source Mode socket
                for item in modifier.node_group.interface.items_tree:
                    if (item.item_type == 'SOCKET' and 
                        item.in_out == 'INPUT' and 
                        item.name == "Source Mode"):
                        source_mode = modifier.get(item.identifier, 0)
                        break
            
            # ПРИОРИТЕТ 3: Fallback к старой системе Input_2 (как в бекапе)
            if source_mode is None:
                source_mode = modifier.get("Input_2", 0)
            
            # Определяем режим по Source Mode
            if source_mode == 1:
                cloner_mode = "COLLECTION"
            else:
                cloner_mode = "OBJECT"  # По умолчанию Object режим
            
            return cloner_type, cloner_mode
            
        elif ("SplineCloner" in node_group_name or 
              node_group_name.startswith("SplineCloner3D_Advanced_")):  # <- Поддержка SplineCloner
            cloner_type = "SPLINE"
            
            # ПРИОРИТЕТ 1: Метаданные (наиболее надежно)
            cloner_mode = modifier.get("cloner_mode")
            if cloner_mode in ["OBJECT", "COLLECTION"]:
                return cloner_type, cloner_mode
            
            # ПРИОРИТЕТ 2: Socket mapping система (новая)
            source_mode = None
            if modifier.node_group:
                # Ищем Source Mode socket
                for item in modifier.node_group.interface.items_tree:
                    if (item.item_type == 'SOCKET' and 
                        item.in_out == 'INPUT' and 
                        item.name == "Source Mode"):
                        source_mode = modifier.get(item.identifier, 0)
                        break
            
            # ПРИОРИТЕТ 3: Fallback к старой системе Input_2
            if source_mode is None:
                source_mode = modifier.get("Input_2", 0)
            
            # Определяем режим по Source Mode
            if source_mode == 1:
                cloner_mode = "COLLECTION"
            else:
                cloner_mode = "OBJECT"  # По умолчанию Object режим
            
            return cloner_type, cloner_mode
        
        # Дополнительный fallback: определяем по source_type
        if not cloner_type:
            # Пытаемся определить тип клонера из имени группы
            if "Grid" in node_group_name:
                cloner_type = "GRID"
            elif "Linear" in node_group_name:
                cloner_type = "LINEAR"
            elif "Circle" in node_group_name:
                cloner_type = "CIRCLE"
            elif ("Object" in node_group_name or 
                  node_group_name.startswith("ObjectCloner3D_Advanced_")):
                cloner_type = "OBJECT"
            elif ("Spline" in node_group_name or 
                  node_group_name.startswith("SplineCloner3D_Advanced_")):
                cloner_type = "SPLINE"
            # TODO: добавить другие типы
        
        if not cloner_mode:
            # Определяем режим по метаданным и свойствам
            if modifier.get("is_stacked_cloner"):
                cloner_mode = "STACKED"
            elif modifier.get("source_type") == "COLLECTION":
                cloner_mode = "COLLECTION" 
            elif modifier.get("source_type") == "OBJECT":
                cloner_mode = "OBJECT"
            else:
                cloner_mode = "OBJECT"  # По умолчанию
        
        return cloner_type, cloner_mode
    
    @staticmethod
    def get_cloners_on_active_object(context):
        """Получить клонеры на активном объекте"""
        if not context.active_object:
            return []
        
        cloners = []
        cloner_modifiers = ClonerDiscovery.get_cloner_modifiers(context.active_object)
        
        for modifier in cloner_modifiers:
            cloner_info = ClonerDiscovery.analyze_cloner_modifier(context.active_object, modifier)
            if cloner_info:
                cloners.append(cloner_info)
        
        return cloners
    
    @staticmethod
    def count_cloners_in_scene(context):
        """Подсчитать количество клонеров в сцене"""
        return len(ClonerDiscovery.scan_scene_cloners(context))
    
    @staticmethod
    def _is_object_in_current_view_layer(context, obj):
        """
        Проверить, находится ли объект в текущем View Layer
        Это необходимо чтобы избежать ошибок с "orphaned" объектами
        """
        try:
            # Проверяем, есть ли объект в текущей сцене
            if obj.name not in context.scene.objects:
                return False
            
            # Проверяем, доступен ли объект в текущем view layer
            if obj.name not in context.view_layer.objects:
                return False
            
            # Дополнительная проверка: можем ли мы получить объект через view layer
            view_layer_obj = context.view_layer.objects.get(obj.name)
            if not view_layer_obj:
                return False
            
            return True
            
        except Exception as e:
            print(f"⚠️ [SCANNER] Error checking view layer access for {getattr(obj, 'name', 'UNKNOWN')}: {e}")
            return False

    @staticmethod
    def scan_scene_effectors(context):
        """
        Сканирование эффекторов в сцене
        Аналогично scan_scene_cloners, но для эффекторов
        """
        effectors = []
        processed_modifiers = set()  # Защита от дублей
        
        # Сканируем все объекты в сцене
        try:
            scene_objects = list(context.scene.objects)
            
            for obj in scene_objects:
                try:
                    effector_modifiers = ClonerDiscovery.get_effector_modifiers(obj)
                    for modifier in effector_modifiers:
                        # Уникальный идентификатор модификатора
                        modifier_id = f"{obj.name}::{modifier.name}"
                        
                        if modifier_id not in processed_modifiers:
                            effector_info = ClonerDiscovery.analyze_effector_modifier(obj, modifier)
                            if effector_info:
                                effectors.append(effector_info)
                                processed_modifiers.add(modifier_id)
                
                except Exception as e:
                    print(f"⚠️ [EFFECTOR SCANNER] Error scanning object {getattr(obj, 'name', 'UNKNOWN')}: {e}")
                    continue
        
        except Exception as e:
            print(f"⚠️ [EFFECTOR SCANNER] Error accessing scene objects: {e}")
        
        print(f"🔍 [EFFECTOR SCANNER] Found {len(effectors)} effectors in scene")
        return effectors

    @staticmethod
    def get_effector_modifiers(obj):
        """Получает все модификаторы эффекторов на объекте"""
        effector_modifiers = []
        
        for modifier in obj.modifiers:
            if ClonerDiscovery.is_effector_modifier(modifier):
                effector_modifiers.append(modifier)
        
        return effector_modifiers

    @staticmethod
    def is_effector_modifier(modifier):
        """Проверяет, является ли модификатор эффектором"""
        if modifier.type != 'NODES' or not modifier.node_group:
            return False
        
        # Проверяем по метаданным
        effector_type = modifier.get("effector_type")
        if effector_type:
            return True
        
        # Проверяем по имени node group
        node_group_name = modifier.node_group.name.lower()
        effector_keywords = ['effector', 'random', 'noise']
        return any(keyword in node_group_name for keyword in effector_keywords)

    @staticmethod
    def analyze_effector_modifier(obj, modifier):
        """Анализирует модификатор эффектора и возвращает информацию о нем"""
        try:
            effector_type = modifier.get("effector_type", "UNKNOWN")
            
            # Определяем позицию в стеке
            modifier_index = obj.modifiers.find(modifier.name)
            
            effector_info = {
                'type': 'EFFECTOR',
                'effector_type': effector_type,
                'object': obj,
                'object_name': obj.name,
                'modifier': modifier,
                'modifier_name': modifier.name,
                'display_name': f"{effector_type.title()} Effector",
                'index': modifier_index,
                'visible': modifier.show_viewport,
                'category': effector_type
            }
            
            return effector_info
            
        except Exception as e:
            print(f"⚠️ [EFFECTOR ANALYSIS] Error analyzing effector {modifier.name}: {e}")
            return None


def scan_scene_cloners(context):
    """Сканировать сцену на наличие клонеров"""
    return ClonerDiscovery.scan_scene_cloners(context)


def scan_scene_effectors(context):
    """Сканировать сцену на наличие эффекторов"""
    return ClonerDiscovery.scan_scene_effectors(context)


def get_active_object_effectors(context):
    """Получить эффекторы на активном объекте"""
    if not context.active_object:
        return []
    
    effectors = []
    effector_modifiers = ClonerDiscovery.get_effector_modifiers(context.active_object)
    
    for modifier in effector_modifiers:
        effector_info = ClonerDiscovery.analyze_effector_modifier(context.active_object, modifier)
        if effector_info:
            effectors.append(effector_info)
    
    return effectors


def force_refresh_cloner_browser():
    """
    Принудительное обновление браузера клонеров
    Вызывается при проблемах с отображением
    """
    try:
        import bpy
        
        print("🔄 [SCANNER] Forcing complete cloner browser refresh...")
        
        # ВАЖНО: Принудительно обновляем view layer после изменений модификаторов
        bpy.context.view_layer.update()
        
        # Даем Blender время обработать изменения в dependency graph
        if hasattr(bpy.context, 'evaluated_depsgraph_get'):
            try:
                depsgraph = bpy.context.evaluated_depsgraph_get()
                depsgraph.update()
            except:
                pass
        
        # Принудительно обновляем все области UI
        for window in bpy.context.window_manager.windows:
            for area in window.screen.areas:
                if area.type == 'VIEW_3D':
                    for region in area.regions:
                        if region.type == 'UI':
                            region.tag_redraw()
        
        print("✅ [SCANNER] Cloner browser refresh completed")
        return True
    
    except Exception as e:
        print(f"⚠️ [SCANNER] Error forcing refresh: {e}")
        return False


def emergency_cloner_recovery(context):
    """
    ЭКСТРЕННОЕ восстановление списка клонеров
    Используется когда обычное сканирование не работает
    """
    print("🚨 [SCANNER] EMERGENCY RECOVERY MODE")
    
    recovered_cloners = []
    
    try:
        # Поиск по всем объектам в bpy.data без исключений
        for obj_name, obj in bpy.data.objects.items():
            try:
                if not obj or not hasattr(obj, 'modifiers'):
                    continue
                
                for mod_name, modifier in enumerate(obj.modifiers):
                    try:
                        # Очень агрессивная проверка
                        if (hasattr(modifier, 'type') and 
                            modifier.type == 'NODES' and
                            hasattr(modifier, 'node_group') and
                            modifier.node_group):
                            
                            # Ищем любые признаки клонера
                            node_group_name = str(modifier.node_group.name)
                            
                            # Поиск по ключевым словам
                            cloner_keywords = [
                                'cloner', 'Cloner', 'CLONER',
                                'grid', 'Grid', 'GRID',
                                'linear', 'Linear', 'LINEAR', 
                                'circle', 'Circle', 'CIRCLE',
                                'spiral', 'Spiral', 'SPIRAL',
                                'object', 'Object', 'OBJECT',
                                'spline', 'Spline', 'SPLINE',
                                'ObjectCloner3D_Advanced',  # Добавляем базовое имя ObjectCloner
                                'SplineCloner3D_Advanced'   # Добавляем базовое имя SplineCloner
                            ]
                            
                            is_potential_cloner = False
                            for keyword in cloner_keywords:
                                if keyword in node_group_name:
                                    is_potential_cloner = True
                                    break
                            
                            # Также проверяем метаданные
                            if (modifier.get("cloner_type") or 
                                modifier.get("cloner_mode") or
                                modifier.get("is_stacked_cloner")):
                                is_potential_cloner = True
                            
                            if is_potential_cloner:
                                recovered_info = {
                                    'object': obj,
                                    'modifier': modifier,
                                    'cloner_type': modifier.get("cloner_type", "UNKNOWN"),
                                    'cloner_mode': modifier.get("cloner_mode", "UNKNOWN"),
                                    'node_group_name': node_group_name,
                                    'recovered': True
                                }
                                recovered_cloners.append(recovered_info)
                                print(f"🆘 [SCANNER] RECOVERED: {recovered_info['cloner_type']} on {obj_name}")
                    
                    except Exception as e:
                        continue  # Пропускаем проблемные модификаторы
            
            except Exception as e:
                continue  # Пропускаем проблемные объекты
    
    except Exception as e:
        print(f"🚨 [SCANNER] Emergency recovery failed: {e}")
    
    print(f"🆘 [SCANNER] Emergency recovery complete: {len(recovered_cloners)} cloners recovered")
    return recovered_cloners


def get_active_object_cloners(context):
    """Получить клонеры на активном объекте"""
    return ClonerDiscovery.get_cloners_on_active_object(context)


def scan_scene_effectors(context):
    """Сканировать сцену на наличие эффекторов"""
    effectors = []
    
    # Сканируем все объекты в сцене
    for obj in context.scene.objects:
        effector_modifiers = get_effector_modifiers(obj)
        for modifier in effector_modifiers:
            effector_info = analyze_effector_modifier(obj, modifier)
            if effector_info:
                effectors.append(effector_info)
    
    return effectors


def get_effector_modifiers(obj):
    """Получить все модификаторы эффекторов на объекте"""
    effector_modifiers = []
    
    for modifier in obj.modifiers:
        if modifier.type == 'NODES' and modifier.node_group:
            if is_effector_modifier(modifier):
                effector_modifiers.append(modifier)
    
    return effector_modifiers


def is_effector_modifier(modifier):
    """Проверить, является ли модификатор эффектором"""
    if not modifier.node_group:
        return False
    
    node_group_name = modifier.node_group.name
    
    # Паттерны для эффекторов
    effector_patterns = [
        "RandomEffector",
        "NoiseEffector", 
        "PlainEffector",
        "StepEffector",
        "DelayEffector",
        "TargetEffector"
    ]
    
    for pattern in effector_patterns:
        if pattern in node_group_name:
            return True
    
    # Проверяем метаданные
    if modifier.get("effector_type"):
        return True
    
    if modifier.get("is_effector"):
        return True
    
    return False


def analyze_effector_modifier(obj, modifier):
    """Анализировать модификатор эффектора и извлечь информацию"""
    if not modifier.node_group:
        return None
    
    node_group_name = modifier.node_group.name
    
    # Определяем тип эффектора
    effector_type = detect_effector_type(modifier)
    
    if not effector_type:
        return None
    
    # Собираем информацию об эффекторе
    effector_info = {
        'object': obj,
        'modifier': modifier,
        'effector_type': effector_type,
        'node_group': modifier.node_group,
        'node_group_name': node_group_name,
        'display_name': f"{effector_type} Effector",
        'object_name': obj.name,
        'modifier_name': modifier.name,
        'visible': modifier.show_viewport
    }
    
    return effector_info


def detect_effector_type(modifier):
    """Определить тип эффектора"""
    if not modifier.node_group:
        return None
    
    node_group_name = modifier.node_group.name
    
    # Проверяем метаданные
    effector_type = modifier.get("effector_type")
    if effector_type:
        return effector_type
    
    # Определяем по имени node group
    if "RandomEffector" in node_group_name:
        return "RANDOM"
    elif "NoiseEffector" in node_group_name:
        return "NOISE"
    elif "PlainEffector" in node_group_name:
        return "PLAIN"
    elif "StepEffector" in node_group_name:
        return "STEP"
    elif "DelayEffector" in node_group_name:
        return "DELAY"
    elif "TargetEffector" in node_group_name:
        return "TARGET"
    
    return None


def get_cloner_display_name(cloner_info):
    """Получить отображаемое имя клонера"""
    if not cloner_info:
        return "Unknown Cloner"
    
    cloner_type = cloner_info.get('cloner_type', 'Unknown')
    cloner_mode = cloner_info.get('cloner_mode', 'Unknown')
    object_name = cloner_info.get('object_name', 'Unknown')
    
    return f"{cloner_type} ({cloner_mode}) - {object_name}"


def get_effector_display_name(effector_info):
    """Получить отображаемое имя эффектора"""
    if not effector_info:
        return "Unknown Effector"
    
    effector_type = effector_info.get('effector_type', 'Unknown')
    object_name = effector_info.get('object_name', 'Unknown')
    
    return f"{effector_type} Effector - {object_name}"