"""
Cloner Interface Utilities
Common socket creation and interface utilities for all cloners
"""

import bpy


def apply_cloner_finalization(nodes, links, group_input, group_output, final_geometry_output, location_offset=(900, 0)):
    """
    Apply final geometry connection for cloner output.
    Cloners output instances only - realization handled by separate modifiers.
    
    Args:
        nodes: Node group nodes
        links: Node group links
        group_input: Group input node
        group_output: Group output node
        final_geometry_output: The final geometry output from cloner logic
        location_offset: Base location for finalization nodes
    
    Returns:
        None (connects directly to group_output)
    """
    x_offset, y_offset = location_offset
    
    # Join Geometry node to combine all cloner outputs
    join_geometry = nodes.new('GeometryNodeJoinGeometry')
    join_geometry.name = "Join Geometry"
    join_geometry.location = (x_offset, y_offset)
    links.new(final_geometry_output, join_geometry.inputs['Geometry'])
    
    # Direct instance output (no realization)
    links.new(join_geometry.outputs['Geometry'], group_output.inputs['Geometry'])
    
    from ..system.logging import log_info
    log_info("<PERSON><PERSON>r configured to output instances only", "CLONER_INTERFACE")


def add_common_transform_sockets(node_group):
    """Add common transform sockets (Instance Scale, Instance Rotation)"""
    scale_input = node_group.interface.new_socket(name="Instance Scale", in_out='INPUT', socket_type='NodeSocketVector')
    scale_input.default_value = (1.0, 1.0, 1.0)

    rotation_input = node_group.interface.new_socket(name="Instance Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    rotation_input.default_value = (0.0, 0.0, 0.0)
    
    return scale_input, rotation_input


def add_common_random_sockets(node_group):
    """Add common randomization sockets"""
    random_position_input = node_group.interface.new_socket(name="Random Position", in_out='INPUT', socket_type='NodeSocketVector')
    random_position_input.default_value = (0.0, 0.0, 0.0)

    random_rotation_input = node_group.interface.new_socket(name="Random Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    random_rotation_input.default_value = (0.0, 0.0, 0.0)

    random_scale_input = node_group.interface.new_socket(name="Random Scale", in_out='INPUT', socket_type='NodeSocketFloat')
    random_scale_input.default_value = 0.0
    random_scale_input.min_value = 0.0
    random_scale_input.max_value = 1.0

    seed_input = node_group.interface.new_socket(name="Random Seed", in_out='INPUT', socket_type='NodeSocketInt')
    seed_input.default_value = 0
    seed_input.min_value = 0
    seed_input.max_value = 10000
    
    return random_position_input, random_rotation_input, random_scale_input, seed_input


def add_common_global_sockets(node_group):
    """Add common global transform sockets"""
    global_pos = node_group.interface.new_socket(name="Global Position", in_out='INPUT', socket_type='NodeSocketVector')
    global_pos.default_value = (0.0, 0.0, 0.0)
    
    global_rot = node_group.interface.new_socket(name="Global Rotation", in_out='INPUT', socket_type='NodeSocketVector')
    global_rot.default_value = (0.0, 0.0, 0.0)
    
    return global_pos, global_rot


def create_basic_cloner_interface(node_group, cloner_type="Generic"):
    """
    Create basic cloner interface with all common sockets
    
    Args:
        node_group: The node group to add interface to
        cloner_type: Type of cloner for naming
    
    Returns:
        Dictionary with all created sockets for easy reference
    """
    sockets = {}
    
    # Input geometry (for stacked) or Collection (for collection mode)
    if "Stacked" in node_group.name:
        sockets['geometry_input'] = node_group.interface.new_socket(name="Geometry", in_out='INPUT', socket_type='NodeSocketGeometry')
    elif "Collection" in node_group.name:
        sockets['collection_input'] = node_group.interface.new_socket(name="Collection", in_out='INPUT', socket_type='NodeSocketCollection')
    else:
        # Object mode - uses Instance Source
        sockets['instance_source'] = node_group.interface.new_socket(name="Instance Source", in_out='INPUT', socket_type='NodeSocketGeometry')
    
    # Output
    sockets['geometry_output'] = node_group.interface.new_socket(name="Geometry", in_out='OUTPUT', socket_type='NodeSocketGeometry')
    
    # Common transforms - НЕ добавляем для LINEAR клонера (у него есть Start/End параметры)
    if cloner_type != "LINEAR":
        sockets['instance_scale'], sockets['instance_rotation'] = add_common_transform_sockets(node_group)
    
    # Common randomization
    sockets['random_position'], sockets['random_rotation'], sockets['random_scale'], sockets['random_seed'] = add_common_random_sockets(node_group)
    
    # Global transforms для всех режимов (Object, Collection, Stacked)
    sockets['global_position'], sockets['global_rotation'] = add_common_global_sockets(node_group)
    
    return sockets


# Legacy function name for backward compatibility
def apply_anti_recursion_architecture(nodes, links, group_input, group_output, final_geometry_output, use_anti_recursion=True, location_offset=(900, 0)):
    """
    DEPRECATED: Legacy function name for backward compatibility.
    Use apply_cloner_finalization() instead.
    """
    return apply_cloner_finalization(nodes, links, group_input, group_output, final_geometry_output, location_offset)