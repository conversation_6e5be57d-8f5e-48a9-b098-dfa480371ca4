"""
КРИТИЧЕСКАЯ система безопасных операций для ClonerPro
Защищает от крашей в rna_NodeTree_refine при обращении к удаленным NodeTree

ОБЯЗАТЕЛЬНА для предотвращения крашей при повторном создании эффекторов!

Дополнено функциями из advanced_cloners:
- Валидация компонентов (dependency_checker.py)
- Безопасность dependency graph (dependency_graph_safety.py) 
- Очистка узлов (node_utils.py)
- Утилиты модификаторов (modifier_utils.py)
"""

import bpy
import traceback
from typing import Optional, Union, Any, List, Dict


# ======================================================================
# SAFE ACCESS FUNCTIONS - Безопасный доступ к объектам Blender
# ======================================================================

def safe_get_object(object_name: str) -> Optional[bpy.types.Object]:
    """
    КРИТИЧЕСКАЯ ФУНКЦИЯ: Безопасно получает объект по имени.
    Защищает от ReferenceError при обращении к удаленным объектам.
    """
    try:
        if not object_name:
            return None
        
        obj = bpy.data.objects.get(object_name)
        if obj is None:
            return None
        
        # КРИТИЧНО: Проверяем валидность ссылки
        try:
            # Пытаемся обратиться к свойству объекта
            _ = obj.name
            return obj
        except ReferenceError:
            print(f"[SAFE ACCESS] Объект {object_name} удален")
            return None
        
    except Exception as e:
        print(f"[SAFE ACCESS] Ошибка доступа к объекту {object_name}: {e}")
        return None


def safe_get_modifier(obj: bpy.types.Object, modifier_name: str) -> Optional[bpy.types.Modifier]:
    """
    КРИТИЧЕСКАЯ ФУНКЦИЯ: Безопасно получает модификатор объекта.
    Защищает от ReferenceError при обращении к удаленным модификаторам.
    """
    try:
        if not obj or not modifier_name:
            return None
        
        # КРИТИЧНО: Проверяем валидность объекта
        try:
            _ = obj.name
        except ReferenceError:
            print(f"[SAFE ACCESS] Объект недоступен")
            return None
        
        # КРИТИЧНО: Проверяем наличие модификаторов
        if not hasattr(obj, 'modifiers'):
            return None
        
        modifier = obj.modifiers.get(modifier_name)
        if modifier is None:
            return None
        
        # КРИТИЧНО: Проверяем валидность ссылки на модификатор
        try:
            _ = modifier.name
            return modifier
        except ReferenceError:
            print(f"[SAFE ACCESS] Модификатор {modifier_name} удален")
            return None
        
    except Exception as e:
        print(f"[SAFE ACCESS] Ошибка доступа к модификатору {modifier_name}: {e}")
        return None


def safe_access_node_group(modifier: bpy.types.Modifier) -> Optional[bpy.types.NodeGroup]:
    """
    КРИТИЧЕСКАЯ ФУНКЦИЯ: Безопасно получает node_group модификатора.
    
    ЭТО КЛЮЧЕВАЯ ЗАЩИТА от краша в rna_NodeTree_refine!
    Защищает от обращения к удаленным NodeTree через недоступные модификаторы.
    """
    try:
        if not modifier:
            return None
        
        # КРИТИЧНО: Проверяем валидность модификатора
        try:
            _ = modifier.name
            modifier_type = modifier.type
        except ReferenceError:
            print(f"[SAFE ACCESS] Модификатор недоступен")
            return None
        
        # КРИТИЧНО: Проверяем тип модификатора
        if modifier_type != 'NODES':
            return None
        
        # КРИТИЧНО: Проверяем наличие node_group
        if not hasattr(modifier, 'node_group'):
            return None
        
        node_group = modifier.node_group
        if node_group is None:
            return None
        
        # КРИТИЧНО: Проверяем валидность ссылки на node_group
        try:
            _ = node_group.name
            return node_group
        except ReferenceError:
            print(f"[SAFE ACCESS] NodeGroup недоступна")
            return None
        
    except Exception as e:
        print(f"[SAFE ACCESS] Ошибка доступа к NodeGroup: {e}")
        return None


def safe_remove_node(node_group: bpy.types.NodeGroup, node: bpy.types.Node) -> bool:
    """
    КРИТИЧЕСКАЯ ФУНКЦИЯ: Безопасно удаляет узел из node_group.
    Защищает от ReferenceError при обращении к недоступным узлам.
    """
    try:
        if not node_group or not node:
            return False
        
        # КРИТИЧНО: Проверяем валидность node_group
        try:
            _ = node_group.name
        except ReferenceError:
            print(f"[SAFE REMOVE] NodeGroup недоступна")
            return False
        
        # КРИТИЧНО: Проверяем валидность узла
        try:
            _ = node.name
        except ReferenceError:
            print(f"[SAFE REMOVE] Узел уже удален")
            return True  # Считаем успешным, если узел уже удален
        
        # КРИТИЧНО: Безопасно отключаем все связи узла
        try:
            for socket in node.inputs:
                for link in socket.links[:]:
                    try:
                        node_group.links.remove(link)
                    except (ReferenceError, RuntimeError):
                        pass  # Связь уже удалена
            
            for socket in node.outputs:
                for link in socket.links[:]:
                    try:
                        node_group.links.remove(link)
                    except (ReferenceError, RuntimeError):
                        pass  # Связь уже удалена
        except (ReferenceError, AttributeError):
            pass  # Узел уже поврежден
        
        # КРИТИЧНО: Безопасно удаляем узел
        try:
            node_group.nodes.remove(node)
            print(f"[SAFE REMOVE] ✅ Узел безопасно удален")
            return True
        except (ReferenceError, RuntimeError, ValueError):
            print(f"[SAFE REMOVE] Узел уже удален или недоступен")
            return True  # Считаем успешным
        
    except Exception as e:
        print(f"[SAFE REMOVE] ❌ Ошибка безопасного удаления узла: {e}")
        return False


def safe_clear_node_group_references(node_group: bpy.types.NodeGroup) -> bool:
    """
    КРИТИЧЕСКАЯ ФУНКЦИЯ: Безопасно очищает все ссылки на node_group.
    
    ЭТО КРИТИЧНО для предотвращения краша в rna_NodeTree_refine!
    """
    try:
        if not node_group:
            return True
        
        print(f"[SAFE CLEAR] Очищаем ссылки на NodeGroup")
        
        # КРИТИЧНО: Проверяем валидность node_group
        try:
            node_group_name = node_group.name
        except ReferenceError:
            print(f"[SAFE CLEAR] NodeGroup уже недоступна")
            return True
        
        cleared_count = 0
        
        # КРИТИЧНО: Ищем и очищаем ВСЕ ссылки на эту node_group
        for obj in bpy.data.objects:
            try:
                if not hasattr(obj, 'modifiers'):
                    continue
                
                for mod in obj.modifiers:
                    try:
                        if (mod.type == 'NODES' and 
                            hasattr(mod, 'node_group') and 
                            mod.node_group == node_group):
                            
                            print(f"[SAFE CLEAR] Очищаем ссылку в {obj.name}.{mod.name}")
                            mod.node_group = None
                            cleared_count += 1
                            
                    except (ReferenceError, AttributeError):
                        # Модификатор уже поврежден
                        continue
                        
            except (ReferenceError, AttributeError):
                # Объект уже поврежден
                continue
        
        print(f"[SAFE CLEAR] ✅ Очищено {cleared_count} ссылок на NodeGroup")
        return True
        
    except Exception as e:
        print(f"[SAFE CLEAR] ❌ Ошибка очистки ссылок NodeGroup: {e}")
        return False


def emergency_cleanup_invalid_references():
    """
    КРИТИЧЕСКАЯ ФУНКЦИЯ: Экстренная очистка невалидных ссылок.
    
    Выполняется при критических ошибках для предотвращения дальнейших крашей.
    """
    print("[EMERGENCY CLEANUP] 🚨 Экстренная очистка невалидных ссылок")
    
    try:
        cleaned_count = 0
        
        # Проходим по всем объектам и очищаем поврежденные ссылки
        for obj in bpy.data.objects:
            try:
                if not hasattr(obj, 'modifiers'):
                    continue
                
                for mod in obj.modifiers:
                    try:
                        if mod.type == 'NODES':
                            # Проверяем валидность node_group
                            if hasattr(mod, 'node_group') and mod.node_group:
                                try:
                                    # Пытаемся обратиться к свойству
                                    _ = mod.node_group.name
                                except ReferenceError:
                                    # Ссылка повреждена, очищаем
                                    print(f"[EMERGENCY] Очищаем поврежденную ссылку в {obj.name}.{mod.name}")
                                    mod.node_group = None
                                    cleaned_count += 1
                    except (ReferenceError, AttributeError):
                        continue
                        
            except (ReferenceError, AttributeError):
                continue
        
        # Принудительное обновление dependency graph
        try:
            bpy.context.view_layer.update()
        except (RuntimeError, AttributeError) as e:
            from .logging import log_error
            log_error(f"Dependency graph update failed: {e}", "SAFE_OPERATIONS")
            # Try alternative update method
            try:
                if hasattr(bpy.context, 'scene'):
                    bpy.context.scene.update()
            except Exception as fallback_e:
                log_error(f"Fallback update also failed: {fallback_e}", "SAFE_OPERATIONS")
        
        # Дополнительная очистка через depsgraph
        try:
            if hasattr(bpy.context, 'evaluated_depsgraph_get'):
                depsgraph = bpy.context.evaluated_depsgraph_get()
                depsgraph.update()
        except:
            pass
        
        print(f"[EMERGENCY CLEANUP] ✅ Очищено {cleaned_count} поврежденных ссылок")
        
    except Exception as e:
        print(f"[EMERGENCY CLEANUP] ❌ Критическая ошибка экстренной очистки: {e}")
        traceback.print_exc()


def safe_invalidate_modifier_node_group(modifier: bpy.types.Modifier) -> bool:
    """
    КРИТИЧЕСКАЯ ФУНКЦИЯ: Безопасно invalidate node_group модификатора.
    
    ЭТО КРИТИЧНО для предотвращения краша в rna_NodeTree_refine!
    """
    try:
        if not modifier:
            return True
        
        print(f"[SAFE INVALIDATE] Invalidating modifier node_group")
        
        # КРИТИЧНО: Проверяем валидность модификатора
        try:
            modifier_name = modifier.name
        except ReferenceError:
            print(f"[SAFE INVALIDATE] Модификатор уже недоступен")
            return True
        
        # КРИТИЧНО: Проверяем наличие node_group
        if not hasattr(modifier, 'node_group') or not modifier.node_group:
            return True
        
        node_group = modifier.node_group
        
        # КРИТИЧНО: Проверяем валидность node_group
        try:
            node_group_name = node_group.name
        except ReferenceError:
            print(f"[SAFE INVALIDATE] NodeGroup уже недоступна")
            return True
        
        # КРИТИЧНО: Безопасно обнуляем ссылку ПЕРЕД invalidation
        modifier.node_group = None
        
        # КРИТИЧНО: Выполняем invalidation
        try:
            node_group.update_tag()
        except (ReferenceError, RuntimeError):
            print(f"[SAFE INVALIDATE] NodeGroup уже invalidated")
        
        # КРИТИЧНО: Принудительное обновление dependency graph
        try:
            bpy.context.view_layer.update()
        except:
            pass
        
        print(f"[SAFE INVALIDATE] ✅ NodeGroup safely invalidated")
        return True
        
    except Exception as e:
        print(f"[SAFE INVALIDATE] ❌ Ошибка безопасного invalidation: {e}")
        return False


def is_node_group_valid(node_group: Union[bpy.types.NodeGroup, None]) -> bool:
    """
    КРИТИЧЕСКАЯ ФУНКЦИЯ: Проверяет валидность node_group.
    
    Защищает от обращения к удаленным NodeTree.
    """
    try:
        if node_group is None:
            return False
        
        # Пытаемся обратиться к свойству
        _ = node_group.name
        return True
        
    except ReferenceError:
        return False
    except Exception:
        return False


def is_modifier_valid(modifier: Union[bpy.types.Modifier, None]) -> bool:
    """
    КРИТИЧЕСКАЯ ФУНКЦИЯ: Проверяет валидность модификатора.
    
    Защищает от обращения к удаленным модификаторам.
    """
    try:
        if modifier is None:
            return False
        
        # Пытаемся обратиться к свойствам
        _ = modifier.name
        _ = modifier.type
        return True
        
    except ReferenceError:
        return False
    except Exception:
        return False


def is_object_valid(obj: Union[bpy.types.Object, None]) -> bool:
    """
    КРИТИЧЕСКАЯ ФУНКЦИЯ: Проверяет валидность объекта.
    
    Защищает от обращения к удаленным объектам.
    """
    try:
        if obj is None:
            return False
        
        # Пытаемся обратиться к свойству
        _ = obj.name
        return True
        
    except ReferenceError:
        return False
    except Exception:
        return False


# ======================================================================
# VALIDATION DECORATORS - Декораторы для автоматической валидации
# ======================================================================

def validate_node_group_access(func):
    """
    Декоратор для автоматической валидации доступа к node_group.
    Предотвращает краши в rna_NodeTree_refine.
    """
    def wrapper(*args, **kwargs):
        try:
            # Проверяем аргументы на наличие node_group
            for arg in args:
                if hasattr(arg, 'node_group'):
                    if not is_node_group_valid(arg.node_group):
                        print(f"[VALIDATION] NodeGroup недоступна в {func.__name__}")
                        return None
                elif isinstance(arg, bpy.types.NodeGroup):
                    if not is_node_group_valid(arg):
                        print(f"[VALIDATION] NodeGroup недоступна в {func.__name__}")
                        return None
            
            # Проверяем kwargs
            for key, value in kwargs.items():
                if hasattr(value, 'node_group'):
                    if not is_node_group_valid(value.node_group):
                        print(f"[VALIDATION] NodeGroup недоступна в {func.__name__}")
                        return None
                elif isinstance(value, bpy.types.NodeGroup):
                    if not is_node_group_valid(value):
                        print(f"[VALIDATION] NodeGroup недоступна в {func.__name__}")
                        return None
            
            return func(*args, **kwargs)
            
        except ReferenceError as e:
            print(f"[VALIDATION] ReferenceError в {func.__name__}: {e}")
            return None
        except Exception as e:
            print(f"[VALIDATION] Ошибка в {func.__name__}: {e}")
            return None
    
    return wrapper


def validate_modifier_access(func):
    """
    Декоратор для автоматической валидации доступа к модификаторам.
    """
    def wrapper(*args, **kwargs):
        try:
            # Проверяем аргументы на наличие модификаторов
            for arg in args:
                if isinstance(arg, bpy.types.Modifier):
                    if not is_modifier_valid(arg):
                        print(f"[VALIDATION] Модификатор недоступен в {func.__name__}")
                        return None
            
            # Проверяем kwargs
            for key, value in kwargs.items():
                if isinstance(value, bpy.types.Modifier):
                    if not is_modifier_valid(value):
                        print(f"[VALIDATION] Модификатор недоступен в {func.__name__}")
                        return None
            
            return func(*args, **kwargs)
            
        except ReferenceError as e:
            print(f"[VALIDATION] ReferenceError в {func.__name__}: {e}")
            return None
        except Exception as e:
            print(f"[VALIDATION] Ошибка в {func.__name__}: {e}")
            return None
    
    return wrapper


# ======================================================================
# COMPONENT VALIDATION - Валидация компонентов (из advanced_cloners)
# ======================================================================

def is_cloner_modifier(mod: bpy.types.Modifier) -> bool:
    """
    Определяет, является ли модификатор клонером.
    
    Портировано из advanced_cloners/dependency_checker.py
    """
    try:
        if mod.type != 'NODES' or not mod.node_group:
            return False
        
        # Проверяем валидность ссылки
        try:
            _ = mod.name
            _ = mod.node_group.name
        except ReferenceError:
            return False
        
        # Проверяем метаданные
        component_type = mod.node_group.get("component_type")
        if component_type == 'CLONER':
            return True
        
        # Fallback на проверку по имени
        node_group_name = mod.node_group.name.lower()
        cloner_keywords = ['cloner', 'grid', 'linear', 'circle', 'spiral']
        return any(keyword in node_group_name for keyword in cloner_keywords)
        
    except Exception as e:
        print(f"[SAFE VALIDATION] Ошибка проверки клонера: {e}")
        return False


def is_effector_modifier(mod: bpy.types.Modifier) -> bool:
    """
    Определяет, является ли модификатор эффектором.
    
    Портировано из advanced_cloners/dependency_checker.py
    """
    try:
        if mod.type != 'NODES' or not mod.node_group:
            return False
        
        # Проверяем валидность ссылки
        try:
            _ = mod.name
            _ = mod.node_group.name
        except ReferenceError:
            return False
        
        # Проверяем метаданные
        component_type = mod.node_group.get("component_type")
        if component_type == 'EFFECTOR':
            return True
        
        # Fallback на проверку по имени
        node_group_name = mod.node_group.name.lower()
        effector_keywords = ['effector', 'random', 'noise']
        return any(keyword in node_group_name for keyword in effector_keywords)
        
    except Exception as e:
        print(f"[SAFE VALIDATION] Ошибка проверки эффектора: {e}")
        return False


def safe_find_cloner_modifiers(obj: bpy.types.Object) -> list:
    """
    Безопасно находит все модификаторы клонеров на объекте.
    
    Портировано из advanced_cloners/modifier_utils.py
    """
    cloner_modifiers = []
    
    try:
        if not obj or not hasattr(obj, 'modifiers'):
            return cloner_modifiers
        
        # Проверяем валидность объекта
        try:
            _ = obj.name
        except ReferenceError:
            print(f"[SAFE FIND] Объект недоступен")
            return cloner_modifiers
        
        for modifier in obj.modifiers:
            if is_cloner_modifier(modifier):
                cloner_modifiers.append(modifier)
        
        return cloner_modifiers
        
    except Exception as e:
        print(f"[SAFE FIND] Ошибка поиска клонеров: {e}")
        return cloner_modifiers


def safe_find_effector_modifiers(obj: bpy.types.Object) -> list:
    """
    Безопасно находит все модификаторы эффекторов на объекте.
    
    Портировано из advanced_cloners/modifier_utils.py
    """
    effector_modifiers = []
    
    try:
        if not obj or not hasattr(obj, 'modifiers'):
            return effector_modifiers
        
        # Проверяем валидность объекта
        try:
            _ = obj.name
        except ReferenceError:
            print(f"[SAFE FIND] Объект недоступен")
            return effector_modifiers
        
        for modifier in obj.modifiers:
            if is_effector_modifier(modifier):
                effector_modifiers.append(modifier)
        
        return effector_modifiers
        
    except Exception as e:
        print(f"[SAFE FIND] Ошибка поиска эффекторов: {e}")
        return effector_modifiers


# ======================================================================
# DEPENDENCY GRAPH SAFETY - Безопасность dependency graph
# ======================================================================

def safe_update_dependency_graph():
    """
    Безопасно обновляет dependency graph.
    
    Портировано из advanced_cloners/dependency_recovery.py
    """
    try:
        # Принудительное обновление dependency graph
        bpy.context.view_layer.update()
        
        # Дополнительная очистка через depsgraph
        if hasattr(bpy.context, 'evaluated_depsgraph_get'):
            depsgraph = bpy.context.evaluated_depsgraph_get()
            depsgraph.update()
        
        print("[SAFE UPDATE] ✅ Dependency graph безопасно обновлен")
        return True
        
    except Exception as e:
        print(f"[SAFE UPDATE] ❌ Ошибка обновления dependency graph: {e}")
        return False


def diagnose_component_relationships():
    """
    Диагностирует все связи клонер-эффектор в сцене.
    
    Упрощенная версия из advanced_cloners/dependency_graph_safety.py
    """
    print("[COMPONENT DIAGNOSIS] === ДИАГНОСТИКА СВЯЗЕЙ КОМПОНЕНТОВ ===")
    
    try:
        relationships_found = 0
        problems_detected = 0
        
        for obj in bpy.data.objects:
            try:
                if not hasattr(obj, 'modifiers'):
                    continue
                
                cloner_mods = safe_find_cloner_modifiers(obj)
                effector_mods = safe_find_effector_modifiers(obj)
                
                if cloner_mods and effector_mods:
                    relationships_found += len(cloner_mods) * len(effector_mods)
                    print(f"[DIAGNOSIS] Объект {obj.name}: {len(cloner_mods)} клонеров, {len(effector_mods)} эффекторов")
                
                # Проверяем валидность node_group у каждого модификатора
                for mod in cloner_mods + effector_mods:
                    if not is_modifier_valid(mod) or not is_node_group_valid(mod.node_group):
                        problems_detected += 1
                        print(f"[WARNING] Проблемный модификатор: {mod.name}")
                        
            except Exception as e:
                print(f"[DIAGNOSIS] Ошибка обработки объекта: {e}")
                continue
        
        print(f"[COMPONENT DIAGNOSIS] Найдено связей: {relationships_found}")
        print(f"[COMPONENT DIAGNOSIS] Обнаружено проблем: {problems_detected}")
        print("[COMPONENT DIAGNOSIS] === ДИАГНОСТИКА ЗАВЕРШЕНА ===")
        
        return {
            "relationships_found": relationships_found,
            "problems_detected": problems_detected
        }
        
    except Exception as e:
        print(f"[COMPONENT DIAGNOSIS] ❌ Ошибка диагностики: {e}")
        traceback.print_exc()
        return {"relationships_found": 0, "problems_detected": -1}


# ======================================================================
# NODE GROUP CLEANUP - Очистка групп узлов
# ======================================================================

def cleanup_missing_data_blocks():
    """
    Находит и удаляет все узлы GROUP с отсутствующими node_tree.
    
    Портировано из advanced_cloners/node_utils.py
    """
    print("[NODE CLEANUP] === ОЧИСТКА MISSING DATA BLOCKS ===")
    
    try:
        fixed_count = 0
        problem_nodes = []
        
        # Собираем информацию о проблемных узлах
        for ng in bpy.data.node_groups:
            try:
                _ = ng.name  # Проверяем валидность
                
                for node in ng.nodes:
                    try:
                        if node.type == 'GROUP' and not node.node_tree:
                            # Проверяем, есть ли у него связи
                            has_connections = any(socket.links for socket in node.inputs) or \
                                            any(socket.links for socket in node.outputs)
                            
                            if not has_connections:
                                problem_nodes.append((ng, node.name))
                                print(f"[NODE CLEANUP] Найден неиспользуемый узел: '{node.name}' в '{ng.name}'")
                                
                    except ReferenceError:
                        continue
                        
            except ReferenceError:
                continue
        
        # Безопасно удаляем проблемные узлы
        for ng, node_name in problem_nodes:
            try:
                node = ng.nodes.get(node_name)
                if node and node.type == 'GROUP' and not node.node_tree:
                    ng.nodes.remove(node)
                    fixed_count += 1
                    print(f"[NODE CLEANUP] ✅ Удален узел {node_name}")
                    
            except Exception as e:
                print(f"[NODE CLEANUP] ❌ Ошибка удаления {node_name}: {e}")
        
        print(f"[NODE CLEANUP] Очистка завершена. Удалено {fixed_count} узлов")
        return fixed_count
        
    except Exception as e:
        print(f"[NODE CLEANUP] ❌ Критическая ошибка очистки: {e}")
        traceback.print_exc()
        return 0


def cleanup_orphaned_node_groups():
    """
    Удаляет node groups без пользователей (orphaned).
    
    Портировано из advanced_cloners/node_utils.py
    """
    print("[ORPHAN CLEANUP] === ОЧИСТКА ORPHANED NODE GROUPS ===")
    
    try:
        removed_count = 0
        orphaned_groups = []
        
        # Находим группы без пользователей
        for ng in bpy.data.node_groups:
            try:
                if ng.users == 0:
                    orphaned_groups.append(ng.name)
                    print(f"[ORPHAN CLEANUP] Найдена orphaned группа: {ng.name}")
            except ReferenceError:
                continue
        
        # Удаляем orphaned группы
        for group_name in orphaned_groups:
            try:
                ng = bpy.data.node_groups.get(group_name)
                if ng and ng.users == 0:
                    bpy.data.node_groups.remove(ng)
                    removed_count += 1
                    print(f"[ORPHAN CLEANUP] ✅ Удалена группа {group_name}")
                    
            except Exception as e:
                print(f"[ORPHAN CLEANUP] ❌ Ошибка удаления {group_name}: {e}")
        
        print(f"[ORPHAN CLEANUP] ✅ Удалено {removed_count} orphaned node groups")
        return removed_count
        
    except Exception as e:
        print(f"[ORPHAN CLEANUP] ❌ Критическая ошибка: {e}")
        traceback.print_exc()
        return 0


def full_system_cleanup():
    """
    Выполняет полную очистку системы.
    
    Комбинация функций из advanced_cloners/node_utils.py
    """
    print("[FULL CLEANUP] 🧹 === ПОЛНАЯ ОЧИСТКА СИСТЕМЫ ===")
    
    try:
        results = {
            "missing_data_blocks_removed": 0,
            "orphaned_groups_removed": 0,
            "invalid_references_cleaned": 0,
            "dependency_graph_updated": False
        }
        
        # 1. Экстренная очистка невалидных ссылок
        print("\n1️⃣ Экстренная очистка невалидных ссылок:")
        emergency_cleanup_invalid_references()
        results["invalid_references_cleaned"] = 1
        
        # 2. Удаляем Missing Data Block узлы
        print("\n2️⃣ Удаление Missing Data Block узлов:")
        results["missing_data_blocks_removed"] = cleanup_missing_data_blocks()
        
        # 3. Удаляем orphaned группы
        print("\n3️⃣ Удаление orphaned node groups:")
        results["orphaned_groups_removed"] = cleanup_orphaned_node_groups()
        
        # 4. Обновляем dependency graph
        print("\n4️⃣ Обновление dependency graph:")
        results["dependency_graph_updated"] = safe_update_dependency_graph()
        
        # 5. Финальная диагностика
        print("\n5️⃣ Финальная диагностика:")
        final_diagnosis = diagnose_component_relationships()
        
        success = final_diagnosis["problems_detected"] == 0
        print(f"\n[FULL CLEANUP] {'🎉 Система полностью очищена!' if success else '⚠️ Остались проблемы'}")
        print("[FULL CLEANUP] 🏁 === ОЧИСТКА ЗАВЕРШЕНА ===")
        
        return results
        
    except Exception as e:
        print(f"[FULL CLEANUP] ❌ Критическая ошибка полной очистки: {e}")
        traceback.print_exc()
        return {"error": str(e)}


# ======================================================================
# ПУБЛИЧНЫЙ API
# ======================================================================

__all__ = [
    # Основные функции безопасного доступа
    'safe_get_object',
    'safe_get_modifier', 
    'safe_access_node_group',
    'safe_remove_node',
    'safe_clear_node_group_references',
    'safe_invalidate_modifier_node_group',
    
    # Функции валидации
    'is_node_group_valid',
    'is_modifier_valid',
    'is_object_valid',
    
    # Экстренные функции
    'emergency_cleanup_invalid_references',
    
    # Декораторы
    'validate_node_group_access',
    'validate_modifier_access',
    
    # Валидация компонентов (портировано из advanced_cloners)
    'is_cloner_modifier',
    'is_effector_modifier',
    'safe_find_cloner_modifiers',
    'safe_find_effector_modifiers',
    
    # Безопасность dependency graph
    'safe_update_dependency_graph',
    'diagnose_component_relationships',
    
    # Очистка системы
    'cleanup_missing_data_blocks',
    'cleanup_orphaned_node_groups',
    'full_system_cleanup'
]