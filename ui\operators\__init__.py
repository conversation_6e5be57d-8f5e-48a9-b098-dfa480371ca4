"""
UI Operators for ClonerPro
"""

from . import creation
from . import management
from . import browser
from . import cloner_ui
from . import effector_ui
from . import field_creation
from . import chain_management
from . import anti_recursion
from . import modifier_management
from . import section_management


def register():
    """Register all operators"""
    creation.register()
    management.register()
    browser.register()
    cloner_ui.register()
    effector_ui.register()
    field_creation.register()
    chain_management.register()
    anti_recursion.register()
    modifier_management.register()
    section_management.register()


def unregister():
    """Unregister all operators"""
    section_management.unregister()
    modifier_management.unregister()
    anti_recursion.unregister()
    chain_management.unregister()
    field_creation.unregister()
    effector_ui.unregister()
    cloner_ui.unregister()
    browser.unregister()
    management.unregister()
    creation.unregister()