"""
Fields module for ClonerPro

"""

# Class-based fields
from .sphere import SphereField, create_sphere_field
from .box import BoxField, create_box_field

__all__ = [
    # Class-based fields
    "SphereField",
    "create_sphere_field",
    "BoxField",
    "create_box_field"
]


def register():
    """Register field components"""
    print("✅ ClonerPro: Fields module registered")
    print("  - Class-based fields: SphereField")
    # Филды регистрируются через registry систему


def unregister():
    """Unregister field components"""
    print("✅ ClonerPro: Fields module unregistered")
    # Здесь можно добавить логику очистки если потребуется