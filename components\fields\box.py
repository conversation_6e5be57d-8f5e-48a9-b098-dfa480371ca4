"""
Box Field - Class-based implementation
Демонстрация унифицированной системы разработки полей
"""

import bpy
from ..base_field import BaseField


class BoxField(BaseField):
    """
    Box Field - создает прямоугольное поле для ограничения действия эффекторов
    Демонстрирует как легко добавить новое поле в унифицированную систему
    """
    
    bl_idname = "BOX"
    bl_label = "Box Field"
    
    def __init__(self):
        super().__init__()
    
    def register_properties(self, props_owner):
        """
        Регистрация всех свойств Box Field - маска для ограничения действия эффекторов
        """
        # Регистрируем базовые свойства
        super().register_properties(props_owner)

        # Box Settings - используем те же имена что и Sphere Field для совместимости
        props_owner.box_center = bpy.props.FloatVectorProperty(
            name="Center",
            description="Center of the box field mask",
            default=(0.0, 0.0, 0.0),
            size=3
        )
        props_owner.box_radius = bpy.props.FloatProperty(
            name="Radius",
            description="Size of the box field mask (called Radius for compatibility)",
            default=5.0,
            min=0.1,
            max=1000.0
        )
        props_owner.box_falloff = bpy.props.FloatProperty(
            name="Falloff",
            description="Falloff zone for smooth transition (0.0 - 1.0)",
            default=0.0,
            min=0.0,
            max=1.0
        )
    
    def get_specific_sockets(self):
        """
        Специфичные сокеты для Box Field - используем "Radius" для совместимости с системой синхронизации

        Returns:
            list: Список специфичных сокетов (name, socket_type, in_out, default, min, max)
        """
        return [
            ("Center", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),
            ("Radius", "NodeSocketFloat", "INPUT", 5.0, 0.1, 1000.0),  # Используем Radius для совместимости
            ("Falloff", "NodeSocketFloat", "INPUT", 0.0, 0.0, 1.0),
        ]
    
    def _create_field_logic(self, base_nodes):
        """
        Основная логика Box Field - маска для ограничения действия эффекторов
        Аналогично Sphere Field, но для прямоугольной области

        Args:
            base_nodes: Словарь с базовыми нодами

        Returns:
            NodeSocket: Выход силы поля (маски)
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        position = base_nodes['position']

        # 1. Смещаем позицию относительно центра бокса
        center_offset = nodes.new('ShaderNodeVectorMath')
        center_offset.operation = 'SUBTRACT'
        center_offset.location = (-400, 0)
        links.new(position.outputs[0], center_offset.inputs[0])
        links.new(group_input.outputs['Center'], center_offset.inputs[1])

        # 2. Разделяем координаты
        separate_xyz = nodes.new('ShaderNodeSeparateXYZ')
        separate_xyz.location = (-300, 0)
        links.new(center_offset.outputs['Vector'], separate_xyz.inputs['Vector'])

        # 3. Используем Radius как размер бокса (для совместимости с системой синхронизации)
        # Для Box Field: Radius определяет размер куба (половина стороны)
        half_size = group_input.outputs['Radius']  # Radius уже является половиной размера

        # 5. Получаем абсолютные значения координат
        abs_x = nodes.new('ShaderNodeMath')
        abs_x.operation = 'ABSOLUTE'
        abs_x.location = (-200, 100)
        links.new(separate_xyz.outputs['X'], abs_x.inputs[0])

        abs_y = nodes.new('ShaderNodeMath')
        abs_y.operation = 'ABSOLUTE'
        abs_y.location = (-200, 50)
        links.new(separate_xyz.outputs['Y'], abs_y.inputs[0])

        abs_z = nodes.new('ShaderNodeMath')
        abs_z.operation = 'ABSOLUTE'
        abs_z.location = (-200, 0)
        links.new(separate_xyz.outputs['Z'], abs_z.inputs[0])

        # 4. Вычисляем расстояние от границ для каждой оси
        # distance = radius - abs(position) (для кубической области)
        dist_x = nodes.new('ShaderNodeMath')
        dist_x.operation = 'SUBTRACT'
        dist_x.location = (-100, 100)
        links.new(group_input.outputs['Radius'], dist_x.inputs[0])
        links.new(abs_x.outputs[0], dist_x.inputs[1])

        dist_y = nodes.new('ShaderNodeMath')
        dist_y.operation = 'SUBTRACT'
        dist_y.location = (-100, 50)
        links.new(group_input.outputs['Radius'], dist_y.inputs[0])
        links.new(abs_y.outputs[0], dist_y.inputs[1])

        dist_z = nodes.new('ShaderNodeMath')
        dist_z.operation = 'SUBTRACT'
        dist_z.location = (-100, 0)
        links.new(group_input.outputs['Radius'], dist_z.inputs[0])
        links.new(abs_z.outputs[0], dist_z.inputs[1])

        # 5. Находим минимальное расстояние (определяет, насколько внутри точка)
        min_xy = nodes.new('ShaderNodeMath')
        min_xy.operation = 'MINIMUM'
        min_xy.location = (0, 75)
        links.new(dist_x.outputs[0], min_xy.inputs[0])
        links.new(dist_y.outputs[0], min_xy.inputs[1])

        min_xyz = nodes.new('ShaderNodeMath')
        min_xyz.operation = 'MINIMUM'
        min_xyz.location = (100, 50)
        links.new(min_xy.outputs[0], min_xyz.inputs[0])
        links.new(dist_z.outputs[0], min_xyz.inputs[1])

        # 6. Вычисляем falloff boundary для Box Field
        # falloff=0 -> резкая граница (From Min = 0)
        # falloff=0.5 -> плавный переход начинается с половины радиуса (From Min = -radius*0.5)
        # falloff=1.0 -> плавный переход от центра до края (From Min = -radius)
        math_falloff_boundary = nodes.new('ShaderNodeMath')
        math_falloff_boundary.operation = 'MULTIPLY'
        math_falloff_boundary.location = (200, -100)
        links.new(group_input.outputs['Radius'], math_falloff_boundary.inputs[0])
        links.new(group_input.outputs['Falloff'], math_falloff_boundary.inputs[1])

        # Инвертируем falloff boundary для Box Field (делаем отрицательным)
        math_falloff_negative = nodes.new('ShaderNodeMath')
        math_falloff_negative.operation = 'MULTIPLY'
        math_falloff_negative.inputs[1].default_value = -1.0
        math_falloff_negative.location = (300, -150)
        links.new(math_falloff_boundary.outputs[0], math_falloff_negative.inputs[0])

        # 7. Map Range: расстояние до границы -> сила поля (правильная логика для Box)
        map_range = nodes.new('ShaderNodeMapRange')
        map_range.clamp = True
        map_range.data_type = 'FLOAT'
        map_range.interpolation_type = 'LINEAR'
        map_range.location = (400, 0)

        # Подключаем минимальное расстояние как входное значение
        links.new(min_xyz.outputs[0], map_range.inputs['Value'])

        # Для Box Field:
        # min_distance > 0 = внутри, min_distance <= 0 = снаружи
        # From Min = falloff_boundary (отрицательное), From Max = radius (центр)
        links.new(math_falloff_negative.outputs[0], map_range.inputs['From Min'])  # Falloff boundary (отрицательное)
        links.new(group_input.outputs['Radius'], map_range.inputs['From Max'])  # В центре

        # To Min = 0.0 (нет силы в falloff зоне), To Max = 1.0 (полная сила в центре)
        map_range.inputs['To Min'].default_value = 0.0
        map_range.inputs['To Max'].default_value = 1.0

        # 8. Map Range: промежуточная сила -> финальная сила (inner/outer strength)
        map_range_final = nodes.new('ShaderNodeMapRange')
        map_range_final.clamp = True
        map_range_final.data_type = 'FLOAT'
        map_range_final.interpolation_type = 'LINEAR'
        map_range_final.location = (600, 0)

        # Подключаем промежуточную силу
        links.new(map_range.outputs['Result'], map_range_final.inputs['Value'])

        # From Min = 0.0, From Max = 1.0
        map_range_final.inputs['From Min'].default_value = 0.0
        map_range_final.inputs['From Max'].default_value = 1.0

        # To Min = Outer Strength, To Max = Inner Strength
        links.new(group_input.outputs['Outer Strength'], map_range_final.inputs['To Min'])
        links.new(group_input.outputs['Inner Strength'], map_range_final.inputs['To Max'])

        # 9. Enable multiply - точно как в Sphere Field
        enable_multiply = nodes.new('ShaderNodeMath')
        enable_multiply.operation = 'MULTIPLY'
        enable_multiply.location = (800, 0)

        # Подключаем: field_strength * Enable
        links.new(map_range_final.outputs['Result'], enable_multiply.inputs[0])
        links.new(group_input.outputs['Enable'], enable_multiply.inputs[1])

        return enable_multiply.outputs['Value']
    
    def get_parameter_groups(self):
        """Группировка параметров для UI - аналогично Sphere Field"""
        return {
            "Field Settings": [
                {
                    "name": "Enable",
                    "socket_name": "Enable",
                    "description": "Включить/выключить поле",
                    "type": "BOOLEAN",
                    "default": True,
                    "ui_type": "TOGGLE"
                },
                {
                    "name": "Strength",
                    "socket_name": "Inner Strength",
                    "description": "Сила поля внутри области",
                    "type": "FLOAT",
                    "default": 1.0,
                    "min": 0.0,
                    "max": 1.0,
                    "ui_type": "SLIDER"
                }
            ],
            "Spatial Parameters": [
                {
                    "name": "Center",
                    "socket_name": "Center",
                    "description": "Центр прямоугольной области",
                    "type": "VECTOR",
                    "default": (0.0, 0.0, 0.0),
                    "ui_type": "VECTOR"
                },
                {
                    "name": "Radius",
                    "socket_name": "Radius",
                    "description": "Размер кубической области (половина стороны)",
                    "type": "FLOAT",
                    "default": 5.0,
                    "min": 0.1,
                    "max": 1000.0,
                    "ui_type": "SLIDER"
                }
            ],
            "Falloff Controls": [
                {
                    "name": "Falloff",
                    "socket_name": "Falloff",
                    "description": "Зона затухания (0.0 - 1.0)",
                    "type": "FLOAT",
                    "default": 0.0,
                    "min": 0.0,
                    "max": 1.0,
                    "ui_type": "SLIDER"
                },
                {
                    "name": "Outer Strength",
                    "socket_name": "Outer Strength",
                    "description": "Сила поля снаружи области",
                    "type": "FLOAT",
                    "default": 0.0,
                    "min": 0.0,
                    "max": 1.0,
                    "ui_type": "SLIDER"
                }
            ]
        }
    
    def get_default_parameters(self):
        """Параметры по умолчанию для Box Field - маска для эффекторов"""
        base_defaults = super().get_default_parameters()
        box_defaults = {
            "center": (0.0, 0.0, 0.0),
            "radius": 5.0,  # Размер кубической области (половина стороны)
            "falloff": 0.0,
            "inner_strength": 1.0,  # Полная сила внутри
            "outer_strength": 0.0   # Нет силы снаружи
        }
        return {**base_defaults, **box_defaults}

    def draw_ui(self, layout, context, modifier):
        """
        Отрисовка UI для Box Field - точная копия SphereField UI с кнопками подключения
        """
        if not modifier:
            layout.label(text="No modifier found", icon='ERROR')
            return

        from ...ui.utils.ui_helpers import display_socket_prop

        # Получаем группы параметров
        parameter_groups = self.get_parameter_groups()

        # Отображаем каждую группу параметров
        for group_name, params in parameter_groups.items():
            if params:
                # Создаем бокс для группы
                box = layout.box()
                col = box.column(align=True)

                # Заголовок группы
                col.label(text=f"{group_name}:", icon='SETTINGS')
                col.separator()

                # Добавляем параметры
                for param in params:
                    param_name = param.get('name', param.get('socket_name', ''))
                    socket_name = param.get('socket_name', param_name)
                    display_socket_prop(col, modifier, socket_name, text=param_name)

        # Добавляем секцию управления подключениями к эффекторам ТОЛЬКО ОДИН РАЗ
        self.draw_field_connections(layout, modifier)

    def draw_field_connections(self, layout, modifier):
        """Отрисовка секции подключений филда к эффекторам - точная копия из SphereField"""
        if not modifier or not modifier.id_data:
            return

        obj = modifier.id_data

        # Заголовок секции
        box = layout.box()
        header = box.row()
        header.label(text="Effector Connections", icon='LINKED')

        col = box.column()

        # Найти все эффекторы на объекте
        effectors = []
        for mod in obj.modifiers:
            if mod.type == 'NODES' and mod.node_group:
                node_group_name = mod.node_group.name.lower()
                if ('effector' in node_group_name or 'random' in node_group_name or 'noise' in node_group_name):
                    # Проверить, подключен ли уже филд к этому эффектору
                    is_connected = False
                    if mod.node_group:
                        for node in mod.node_group.nodes:
                            if node.name == "Field" and node.type == 'GROUP' and node.node_tree:
                                # Дополнительно проверяем, что это именно field node group
                                if 'field' in node.node_tree.name.lower():
                                    is_connected = True
                                    break

                    effectors.append({
                        'name': mod.name,
                        'connected': is_connected
                    })

        if not effectors:
            col.label(text="No effectors found on this object", icon='INFO')
            return

        # Отображение эффекторов с кнопками подключения/отключения
        for effector in effectors:
            row = col.row()
            # Показываем статус для отладки
            status_text = f"{effector['name']} ({'Connected' if effector['connected'] else 'Not Connected'})"
            row.label(text=status_text)

            if effector['connected']:
                # Кнопка отключения
                disconnect_op = row.operator("clonerpro.disconnect_field_from_effector",
                                        text="Disconnect", icon='UNLINKED')
                disconnect_op.field_modifier_name = modifier.name
                disconnect_op.effector_modifier_name = effector['name']
            else:
                # Кнопка подключения
                connect_op = row.operator("clonerpro.connect_field_to_effector",
                                        text="Connect", icon='LINKED')
                connect_op.field_modifier_name = modifier.name
                connect_op.effector_modifier_name = effector['name']

    def create_field_node_group_for_effectors(self, name_suffix=""):
        """
        Создание node group Box поля для использования внутри эффекторов
        Возвращает float значение силы поля в каждой точке (НЕ модификатор)
        """
        # Создание node group для поля (не модификатора)
        group_name = f"BoxField{name_suffix}"
        node_group = bpy.data.node_groups.new(type='GeometryNodeTree', name=group_name)

        node_group.color_tag = 'NONE'
        node_group.description = "Box Field - возвращает силу поля для точки в пространстве"
        node_group.default_group_node_width = 140
        node_group.is_modifier = False  # Это НЕ модификатор

        # Интерфейс node group для поля - входы параметров и выход Field
        enable_input = node_group.interface.new_socket(name="Enable", in_out='INPUT', socket_type='NodeSocketBool')
        enable_input.default_value = True

        center_input = node_group.interface.new_socket(name="Center", in_out='INPUT', socket_type='NodeSocketVector')
        center_input.default_value = (0.0, 0.0, 0.0)

        radius_input = node_group.interface.new_socket(name="Radius", in_out='INPUT', socket_type='NodeSocketFloat')
        radius_input.default_value = 5.0
        radius_input.min_value = 0.1
        radius_input.max_value = 1000.0

        falloff_input = node_group.interface.new_socket(name="Falloff", in_out='INPUT', socket_type='NodeSocketFloat')
        falloff_input.default_value = 0.0
        falloff_input.min_value = 0.0
        falloff_input.max_value = 1.0

        inner_strength_input = node_group.interface.new_socket(name="Inner Strength", in_out='INPUT', socket_type='NodeSocketFloat')
        inner_strength_input.default_value = 1.0
        inner_strength_input.min_value = 0.0
        inner_strength_input.max_value = 1.0

        outer_strength_input = node_group.interface.new_socket(name="Outer Strength", in_out='INPUT', socket_type='NodeSocketFloat')
        outer_strength_input.default_value = 0.0
        outer_strength_input.min_value = 0.0
        outer_strength_input.max_value = 1.0

        # ВЫХОД: сила поля (float)
        field_output = node_group.interface.new_socket(name="Field", in_out='OUTPUT', socket_type='NodeSocketFloat')
        field_output.default_value = 0.0

        # Создание узлов (аналогично _create_field_logic, но без Store Attribute)
        nodes = node_group.nodes
        links = node_group.links

        # Группа input/output
        group_input = nodes.new('NodeGroupInput')
        group_input.location = (-600, 0)

        group_output = nodes.new('NodeGroupOutput')
        group_output.location = (600, 0)

        # Position для получения координат точек
        position = nodes.new('GeometryNodeInputPosition')
        position.location = (-400, -200)

        # Используем ту же логику что и в основном методе _create_field_logic
        # 1. Смещаем позицию относительно центра бокса
        center_offset = nodes.new('ShaderNodeVectorMath')
        center_offset.operation = 'SUBTRACT'
        center_offset.location = (-400, 0)
        links.new(position.outputs[0], center_offset.inputs[0])
        links.new(group_input.outputs['Center'], center_offset.inputs[1])

        # 2. Разделяем координаты
        separate_xyz = nodes.new('ShaderNodeSeparateXYZ')
        separate_xyz.location = (-300, 0)
        links.new(center_offset.outputs['Vector'], separate_xyz.inputs['Vector'])

        # 3. Получаем абсолютные значения координат
        abs_x = nodes.new('ShaderNodeMath')
        abs_x.operation = 'ABSOLUTE'
        abs_x.location = (-200, 100)
        links.new(separate_xyz.outputs['X'], abs_x.inputs[0])

        abs_y = nodes.new('ShaderNodeMath')
        abs_y.operation = 'ABSOLUTE'
        abs_y.location = (-200, 50)
        links.new(separate_xyz.outputs['Y'], abs_y.inputs[0])

        abs_z = nodes.new('ShaderNodeMath')
        abs_z.operation = 'ABSOLUTE'
        abs_z.location = (-200, 0)
        links.new(separate_xyz.outputs['Z'], abs_z.inputs[0])

        # 4. Вычисляем расстояние от границ для каждой оси
        # distance = radius - abs(position) (для кубической области)
        dist_x = nodes.new('ShaderNodeMath')
        dist_x.operation = 'SUBTRACT'
        dist_x.location = (-100, 100)
        links.new(group_input.outputs['Radius'], dist_x.inputs[0])
        links.new(abs_x.outputs[0], dist_x.inputs[1])

        dist_y = nodes.new('ShaderNodeMath')
        dist_y.operation = 'SUBTRACT'
        dist_y.location = (-100, 50)
        links.new(group_input.outputs['Radius'], dist_y.inputs[0])
        links.new(abs_y.outputs[0], dist_y.inputs[1])

        dist_z = nodes.new('ShaderNodeMath')
        dist_z.operation = 'SUBTRACT'
        dist_z.location = (-100, 0)
        links.new(group_input.outputs['Radius'], dist_z.inputs[0])
        links.new(abs_z.outputs[0], dist_z.inputs[1])

        # 5. Находим минимальное расстояние (определяет, насколько внутри точка)
        min_xy = nodes.new('ShaderNodeMath')
        min_xy.operation = 'MINIMUM'
        min_xy.location = (0, 75)
        links.new(dist_x.outputs[0], min_xy.inputs[0])
        links.new(dist_y.outputs[0], min_xy.inputs[1])

        min_xyz = nodes.new('ShaderNodeMath')
        min_xyz.operation = 'MINIMUM'
        min_xyz.location = (100, 50)
        links.new(min_xy.outputs[0], min_xyz.inputs[0])
        links.new(dist_z.outputs[0], min_xyz.inputs[1])

        # 6. Вычисляем falloff boundary для Box Field
        math_falloff_boundary = nodes.new('ShaderNodeMath')
        math_falloff_boundary.operation = 'MULTIPLY'
        math_falloff_boundary.location = (200, -100)
        links.new(group_input.outputs['Radius'], math_falloff_boundary.inputs[0])
        links.new(group_input.outputs['Falloff'], math_falloff_boundary.inputs[1])

        # Инвертируем falloff boundary для Box Field (делаем отрицательным)
        math_falloff_negative = nodes.new('ShaderNodeMath')
        math_falloff_negative.operation = 'MULTIPLY'
        math_falloff_negative.inputs[1].default_value = -1.0
        math_falloff_negative.location = (300, -150)
        links.new(math_falloff_boundary.outputs[0], math_falloff_negative.inputs[0])

        # 7. Map Range: расстояние до границы -> сила поля (правильная логика для Box)
        map_range = nodes.new('ShaderNodeMapRange')
        map_range.clamp = True
        map_range.data_type = 'FLOAT'
        map_range.interpolation_type = 'LINEAR'
        map_range.location = (400, 0)

        # Подключаем минимальное расстояние как входное значение
        links.new(min_xyz.outputs[0], map_range.inputs['Value'])

        # Для Box Field:
        # min_distance > 0 = внутри, min_distance <= 0 = снаружи
        # From Min = falloff_boundary (отрицательное), From Max = radius (центр)
        links.new(math_falloff_negative.outputs[0], map_range.inputs['From Min'])  # Falloff boundary (отрицательное)
        links.new(group_input.outputs['Radius'], map_range.inputs['From Max'])  # В центре

        # To Min = 0.0 (нет силы в falloff зоне), To Max = 1.0 (полная сила в центре)
        map_range.inputs['To Min'].default_value = 0.0
        map_range.inputs['To Max'].default_value = 1.0

        # 8. Map Range: промежуточная сила -> финальная сила (inner/outer strength)
        map_range_final = nodes.new('ShaderNodeMapRange')
        map_range_final.clamp = True
        map_range_final.data_type = 'FLOAT'
        map_range_final.interpolation_type = 'LINEAR'
        map_range_final.location = (600, 0)

        # Подключаем промежуточную силу
        links.new(map_range.outputs['Result'], map_range_final.inputs['Value'])

        # From Min = 0.0, From Max = 1.0
        map_range_final.inputs['From Min'].default_value = 0.0
        map_range_final.inputs['From Max'].default_value = 1.0

        # To Min = Outer Strength, To Max = Inner Strength
        links.new(group_input.outputs['Outer Strength'], map_range_final.inputs['To Min'])
        links.new(group_input.outputs['Inner Strength'], map_range_final.inputs['To Max'])

        # 9. Enable multiply - точно как в Sphere Field
        enable_multiply = nodes.new('ShaderNodeMath')
        enable_multiply.operation = 'MULTIPLY'
        enable_multiply.location = (800, 0)

        # Подключаем: field_strength * Enable
        links.new(map_range_final.outputs['Result'], enable_multiply.inputs[0])
        links.new(group_input.outputs['Enable'], enable_multiply.inputs[1])

        # Подключаем к выходу через enable multiply
        links.new(enable_multiply.outputs['Value'], group_output.inputs['Field'])

        return node_group


# Compatibility function for easy integration
def create_box_field(name_suffix=""):
    """Create Box Field using class-based approach"""
    field = BoxField()
    return field.create_node_group(name_suffix)


# Alias for backward compatibility
def create_box_field_modifier(name_suffix=""):
    """Create Box Field modifier - class-based compatibility function"""
    return create_box_field(name_suffix)


# Compatibility function for node group creation (for use inside effectors)
def create_box_field_node_group(name_suffix=""):
    """Create Box Field node group for use inside effectors - class-based compatibility function"""
    field = BoxField()
    return field.create_field_node_group_for_effectors(name_suffix)
