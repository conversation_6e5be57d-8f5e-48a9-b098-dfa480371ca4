"""
Base Wrapper Class для ClonerPro
Базовый класс для создания wrapper-обёрток эффекторов и филдов
"""

import bpy


class WrapperNodeGroup:
    """
    Базовый класс для создания wrapper-обёрток
    
    Упаковывает логику эффекторов/филдов в отдельную подгруппу,
    а в основной группе размещает только одну Group Node
    """
    
    def __init__(self, component_instance, wrapper_name_suffix="_Wrapper"):
        """
        Инициализация wrapper-обёртки

        Args:
            component_instance: Экземпляр эффектора или филда
            wrapper_name_suffix: Суффикс для имени wrapper группы
        """
        self.component = component_instance
        self.wrapper_suffix = wrapper_name_suffix
    
    def create_wrapper_node_group(self, name_suffix=""):
        """
        Создание wrapper node group с упакованной логикой

        Args:
            name_suffix: Суффикс для имени группы

        Returns:
            bpy.types.NodeGroup: Основная группа с одной Group Node внутри
        """
        # 1. Создаём подгруппу с логикой
        logic_group = self._create_logic_subgroup(name_suffix)
        if not logic_group:
            print(f"[ERROR] Failed to create logic subgroup for {self.component.bl_label}")
            return None

        # 2. Создаём wrapper группу с полным интерфейсом
        main_group = self._create_full_wrapper(logic_group, name_suffix)
        if not main_group:
            print(f"[ERROR] Failed to create wrapper group for {self.component.bl_label}")
            return None

        return main_group
    
    def _create_logic_subgroup(self, name_suffix=""):
        """
        Создание подгруппы с логикой компонента

        Args:
            name_suffix: Суффикс для имени

        Returns:
            bpy.types.NodeGroup: Подгруппа с логикой
        """
        # Используем специальные методы для создания логики (без wrapper)
        if hasattr(self.component, 'create_effector_logic_group'):
            # Для эффекторов - используем новый метод без wrapper
            logic_name_suffix = f"{name_suffix}_Logic"
            return self.component.create_effector_logic_group(logic_name_suffix)
        elif hasattr(self.component, 'create_field_logic_group'):
            # Для филдов - используем новый метод без wrapper
            logic_name_suffix = f"{name_suffix}_Logic"
            return self.component.create_field_logic_group(logic_name_suffix)
        elif hasattr(self.component, 'create_cloner_logic_group'):
            # Для клонеров - используем новый метод без wrapper
            logic_name_suffix = f"{name_suffix}_Logic"
            return self.component.create_cloner_logic_group(logic_name_suffix)
        elif hasattr(self.component, 'create_effector_node_group'):
            # Fallback для старых эффекторов
            logic_name_suffix = f"{name_suffix}_Logic"
            return self.component.create_effector_node_group(logic_name_suffix)
        elif hasattr(self.component, 'create_field_node_group'):
            # Fallback для старых филдов
            logic_name_suffix = f"{name_suffix}_Logic"
            return self.component.create_field_node_group(logic_name_suffix)
        else:
            print(f"[ERROR] Component {self.component.bl_label} has no create_*_node_group method")
            return None
    
    def _create_full_wrapper(self, logic_group, name_suffix=""):
        """
        Создание основной группы с ТОЛЬКО одной Group Node (без Group Input/Output)

        Args:
            logic_group: Подгруппа с логикой
            name_suffix: Суффикс для имени

        Returns:
            bpy.types.NodeGroup: Основная группа с одной нодой
        """
        # Создаём основную группу
        main_name = f"{self.component.bl_label.replace(' ', '')}{name_suffix}"
        main_group = bpy.data.node_groups.new(name=main_name, type='GeometryNodeTree')

        # Копируем настройки из logic группы
        main_group.color_tag = logic_group.color_tag
        main_group.description = logic_group.description
        main_group.default_group_node_width = logic_group.default_group_node_width
        main_group.is_modifier = logic_group.is_modifier

        # Создаем полный интерфейс для модификатора, но подключаем только нужные сокеты
        self._create_full_interface_minimal_connections(logic_group, main_group)

        # Создаём ноды в основной группе
        nodes = main_group.nodes
        links = main_group.links

        # Очищаем существующие ноды
        nodes.clear()

        # Создаём базовые Group Input и Group Output для геометрии
        group_input = nodes.new('NodeGroupInput')
        group_input.location = (-400, 0)

        group_output = nodes.new('NodeGroupOutput')
        group_output.location = (400, 0)

        # Создаём одну Group Node с логикой - ВСЕ ПАРАМЕТРЫ НА НЕЙ
        logic_node = nodes.new('GeometryNodeGroup')
        logic_node.node_tree = logic_group
        logic_node.location = (0, 0)
        logic_node.name = f"{self.component.bl_label}_Logic"
        logic_node.label = self.component.bl_label

        # Подключаем только геометрию, параметры передаются через интерфейс
        self._connect_geometry_and_setup_parameters(group_input, logic_node, group_output, main_group)

        return main_group


    def _create_full_interface_minimal_connections(self, logic_group, main_group):
        """
        Создание полного интерфейса для модификатора, но с минимальными подключениями

        Args:
            logic_group: Подгруппа с логикой
            main_group: Основная группа
        """
        # Копируем весь интерфейс для доступа в модификаторе
        self._copy_interface(logic_group, main_group)

    def _connect_geometry_and_setup_parameters(self, group_input, logic_node, group_output, main_group):
        """
        Подключение ВСЕХ выходов Group Input к соответствующим входам Group Node

        Args:
            group_input: Group Input нода
            logic_node: Group Node с логикой
            group_output: Group Output нода
            main_group: Основная группа
        """
        # Получаем links из node tree
        links = logic_node.id_data.links

        try:
            # Подключаем ВСЕ выходы Group Input к соответствующим входам Group Node
            print(f"🔗 [WRAPPER] Connecting Group Input to Logic Node...")
            connected_count = 0
            for output_socket in group_input.outputs:
                if output_socket.name in logic_node.inputs:
                    links.new(output_socket, logic_node.inputs[output_socket.name])
                    connected_count += 1
                    if "Collection Random" in output_socket.name:
                        print(f"🔗 [WRAPPER] Connected {output_socket.name} to Logic Node")
                else:
                    if "Collection Random" in output_socket.name:
                        print(f"❌ [WRAPPER] Socket {output_socket.name} NOT found in Logic Node inputs")

            print(f"🔗 [WRAPPER] Connected {connected_count} input sockets to Logic Node")

            # Подключаем ВСЕ выходы Group Node к соответствующим входам Group Output
            for input_socket in group_output.inputs:
                if input_socket.name in logic_node.outputs:
                    links.new(logic_node.outputs[input_socket.name], input_socket)

        except Exception as e:
            print(f"[WARNING] Could not connect sockets: {e}")

    def _copy_interface(self, source_group, target_group):
        """
        Копирование интерфейса из source группы в target группу
        
        Args:
            source_group: Исходная группа
            target_group: Целевая группа
        """
        # Копируем все сокеты интерфейса
        for socket in source_group.interface.items_tree:
            if socket.item_type == 'SOCKET':
                new_socket = target_group.interface.new_socket(
                    name=socket.name,
                    in_out=socket.in_out,
                    socket_type=socket.socket_type
                )
                
                # Копируем свойства сокета
                if hasattr(socket, 'default_value') and hasattr(new_socket, 'default_value'):
                    try:
                        new_socket.default_value = socket.default_value
                    except:
                        pass  # Некоторые типы сокетов не поддерживают default_value
                
                if hasattr(socket, 'min_value') and hasattr(new_socket, 'min_value'):
                    new_socket.min_value = socket.min_value
                
                if hasattr(socket, 'max_value') and hasattr(new_socket, 'max_value'):
                    new_socket.max_value = socket.max_value
                
                if hasattr(socket, 'attribute_domain') and hasattr(new_socket, 'attribute_domain'):
                    new_socket.attribute_domain = socket.attribute_domain
    



def create_wrapped_component(component_instance, name_suffix=""):
    """
    Удобная функция для создания wrapper-обёртки компонента

    Args:
        component_instance: Экземпляр эффектора или филда
        name_suffix: Суффикс для имени

    Returns:
        bpy.types.NodeGroup: Wrapper группа
    """
    wrapper = WrapperNodeGroup(component_instance)
    return wrapper.create_wrapper_node_group(name_suffix)
