"""
Section Management Operators для улучшенного UI ClonerPro
Операторы для управления коллапсируемыми секциями интерфейса
"""

import bpy
from bpy.types import Operator
from bpy.props import StringProperty


class CLONERPRO_OT_toggle_stack(Operator):
    """Переключить состояние Stack для клонеров"""
    bl_idname = "clonerpro.toggle_stack"
    bl_label = "Toggle Stack"
    bl_description = "Toggle stacking mode for cloners"
    bl_options = {'REGISTER'}
    
    stack_type: StringProperty(
        name="Stack Type",
        description="Type of stack to toggle (base or mesh)",
        options={'HIDDEN'}
    )
    
    def execute(self, context):
        if self.stack_type == "base":
            property_name = "use_stacked_modifiers"
        elif self.stack_type == "mesh":
            property_name = "use_mesh_stacked_modifiers"
        else:
            self.report({'ERROR'}, f"Unknown stack type: {self.stack_type}")
            return {'CANCELLED'}
        
        # Переключаем состояние
        if hasattr(context.scene, property_name):
            current_state = getattr(context.scene, property_name, False)
            setattr(context.scene, property_name, not current_state)
            
            stack_name = "Base Stack" if self.stack_type == "base" else "Mesh Stack"
            new_state = "ON" if not current_state else "OFF"
            self.report({'INFO'}, f"{stack_name}: {new_state}")
        else:
            self.report({'ERROR'}, f"Property {property_name} not found")
            return {'CANCELLED'}
        
        # Обновляем UI
        for area in context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
        
        return {'FINISHED'}





class CLONERPRO_OT_toggle_section_expanded(Operator):
    """Переключить состояние раскрытия секции"""
    bl_idname = "clonerpro.toggle_section_expanded"
    bl_label = "Toggle Section Expanded"
    bl_description = "Toggle section expand/collapse state"
    bl_options = {'REGISTER'}
    
    section_name: StringProperty()
    
    def execute(self, context):
        property_name = f"clonerpro_{self.section_name}_section_expanded"
        
        # Специальная обработка для некоторых секций
        if self.section_name in ['effector_creation', 'effector_settings', 'field_creation', 'field_settings']:
            property_name = f"clonerpro_{self.section_name}_expanded"
        
        # Специальная обработка для новых табовых секций
        if self.section_name in ['cloners_creation', 'effectors_creation', 'fields_creation']:
            property_name = f"clonerpro_{self.section_name}_expanded"
        
        # Проверяем, что свойство существует
        if hasattr(context.scene, property_name):
            current_state = getattr(context.scene, property_name, True)
            setattr(context.scene, property_name, not current_state)
        else:
            print(f"Warning: Property {property_name} not found")
            return {'CANCELLED'}
        
        # Обновляем UI
        for area in context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
        
        return {'FINISHED'}




class CLONERPRO_OT_collapse_all_sections(Operator):
    """Свернуть все секции"""
    bl_idname = "clonerpro.collapse_all_sections"
    bl_label = "Collapse All Sections"
    bl_description = "Collapse all collapsible sections for compact view"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        # Сворачиваем все секции кроме creation (которая всегда нужна)
        context.scene.clonerpro_browser_section_expanded = False
        context.scene.clonerpro_settings_section_expanded = False
        
        # Сворачиваем секции эффекторов
        context.scene.clonerpro_effector_creation_expanded = False
        context.scene.clonerpro_effector_settings_expanded = False
        
        # Сворачиваем секции филдов
        context.scene.clonerpro_field_creation_expanded = False
        context.scene.clonerpro_field_settings_expanded = False
        
        # Сворачиваем новые табовые секции
        context.scene.clonerpro_cloners_creation_expanded = False
        context.scene.clonerpro_effectors_creation_expanded = False
        context.scene.clonerpro_fields_creation_expanded = False
        
        # Обновляем UI
        for area in context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
        
        self.report({'INFO'}, "All sections collapsed")
        return {'FINISHED'}


class CLONERPRO_OT_expand_all_sections(Operator):
    """Развернуть все секции"""
    bl_idname = "clonerpro.expand_all_sections"
    bl_label = "Expand All Sections"
    bl_description = "Expand all collapsible sections for full view"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        # Разворачиваем все секции
        context.scene.clonerpro_browser_section_expanded = True
        context.scene.clonerpro_settings_section_expanded = True
        context.scene.clonerpro_creation_section_expanded = True
        
        # Разворачиваем секции эффекторов
        context.scene.clonerpro_effector_creation_expanded = True
        context.scene.clonerpro_effector_settings_expanded = True
        
        # Разворачиваем секции филдов
        context.scene.clonerpro_field_creation_expanded = True
        context.scene.clonerpro_field_settings_expanded = True
        
        # Разворачиваем новые табовые секции
        context.scene.clonerpro_cloners_creation_expanded = True
        context.scene.clonerpro_effectors_creation_expanded = True
        context.scene.clonerpro_fields_creation_expanded = True
        
        # Обновляем UI
        for area in context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
        
        self.report({'INFO'}, "All sections expanded")
        return {'FINISHED'}




def register():
    """Регистрация операторов управления секциями"""
    bpy.utils.register_class(CLONERPRO_OT_toggle_stack)
    bpy.utils.register_class(CLONERPRO_OT_toggle_section_expanded)
    bpy.utils.register_class(CLONERPRO_OT_collapse_all_sections)
    bpy.utils.register_class(CLONERPRO_OT_expand_all_sections)


def unregister():
    """Отмена регистрации операторов управления секциями"""
    bpy.utils.unregister_class(CLONERPRO_OT_expand_all_sections)
    bpy.utils.unregister_class(CLONERPRO_OT_collapse_all_sections)
    bpy.utils.unregister_class(CLONERPRO_OT_toggle_section_expanded)
    bpy.utils.unregister_class(CLONERPRO_OT_toggle_stack)
