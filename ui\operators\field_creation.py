"""
Операторы создания полей для ClonerPro
"""

import bpy
from bpy.types import Operator
# Новая система полей
from ...core.registry.field_registry import create_field_via_registry
# Совместимость для node group создания
from ...components.fields.sphere import create_sphere_field_node_group


class CLONERPRO_OT_create_field(Operator):
    """Оператор создания поля"""
    bl_idname = "clonerpro.create_field"
    bl_label = "Create Field"
    bl_description = "Create a new field"
    bl_options = {'REGISTER', 'UNDO'}
    
    # Тип поля - динамически из реестра
    def get_field_enum_items(self, context):
        """Динамическое получение списка полей из реестра"""
        from ...core.registry.field_registry import list_field_types, get_field_info

        items = []
        field_types = list_field_types()

        for field_type in field_types:
            field_info = get_field_info(field_type)
            display_name = field_info.get('display_name', field_type.title())
            description = field_info.get('description', f"{display_name} field")
            items.append((field_type, display_name, description))

        # Fallback если реестр пуст
        if not items:
            items.append(('SPHERE', "Sphere", "Spherical field (fallback)"))

        return items

    component_type: bpy.props.EnumProperty(
        name="Field Type",
        description="Type of field to create",
        items=get_field_enum_items,
        default=0  # Используем индекс вместо строки для динамических списков
    )
    
    def execute(self, context):
        """Универсальное создание поля через реестр"""
        try:
            # Используем новую registry систему для создания поля
            result = self._create_field_via_registry(context, self.component_type)

            if result:
                self.report({'INFO'}, f"Created {self.component_type} field")
                return {'FINISHED'}
            else:
                self.report({'WARNING'}, f"Failed to create {self.component_type} field")
                return {'CANCELLED'}

        except Exception as e:
            self.report({'ERROR'}, f"Failed to create field: {str(e)}")
            return {'CANCELLED'}

    def _create_field_via_registry(self, context, field_type):
        """Универсальное создание поля через реестр"""
        try:
            # Используем правильную функцию создания поля
            from ...core.managers.field_creation import create_field_for_object

            # Создаем поле для активного объекта
            modifier, message = create_field_for_object(context, field_type)

            if modifier:
                if message:
                    # Это предупреждение, а не ошибка
                    print(f"⚠️ [REGISTRY] Warning for {field_type} field: {message}")
                print(f"✅ [REGISTRY] Successfully created {field_type} field via registry")
                return True
            else:
                # Это настоящая ошибка
                error_msg = message or "Failed to create field - no modifier returned"
                print(f"❌ [REGISTRY] Error creating {field_type} field: {error_msg}")
                return None

        except Exception as e:
            print(f"❌ [REGISTRY] Error creating {field_type} field: {e}")
            import traceback
            traceback.print_exc()
            return None

    def create_sphere_field(self, context):
        """Создание отдельного модификатора сферического поля"""
        try:
            # Получаем активный объект
            obj = context.active_object
            if not obj:
                self.report({'ERROR'}, "No active object selected.")
                return {'CANCELLED'}
            
            # Создаем node group для сферического поля через новую систему
            sphere_field_group_name = "SphereFieldModifier"
            if sphere_field_group_name not in bpy.data.node_groups:
                sphere_field_group = create_field_via_registry("SPHERE", "Modifier")
                sphere_field_group.name = sphere_field_group_name  # Устанавливаем правильное имя
            else:
                sphere_field_group = bpy.data.node_groups[sphere_field_group_name]
            
            # Добавляем отдельный модификатор для поля
            field_modifier = obj.modifiers.new(name="SphereField", type='NODES')
            field_modifier.node_group = sphere_field_group
            
            # Устанавливаем значения по умолчанию
            defaults = {
                "Enable": True,
                "Center": (0.0, 0.0, 0.0),
                "Radius": 5.0,
                "Falloff": 0.0,
                "Inner Strength": 1.0,
                "Outer Strength": 0.0
            }
            
            for param_name, default_value in defaults.items():
                if param_name in field_modifier:
                    try:
                        field_modifier[param_name] = default_value
                    except Exception as e:
                        print(f"Warning: Could not set {param_name}: {e}")
            
            # Позиционируем field модификатор перед эффекторами
            self.position_field_modifier(obj, field_modifier)
            
            # НЕ подключаем автоматически - пользователь сам выберет эффекторы
            # connected_effectors = self.connect_field_to_effectors(obj, field_modifier)
            
            # Настраиваем сервис синхронизации (пока без подключений)
            # self.setup_field_parameter_synchronization(obj, field_modifier, [])
            
            # Отладочная информация
            print(f"Field modifier '{field_modifier.name}' created with node group '{field_modifier.node_group.name}'")
            print(f"Field modifier parameters: Enable={field_modifier.get('Enable', 'N/A')}, Radius={field_modifier.get('Radius', 'N/A')}")
            
            # Найдем доступные эффекторы для информации
            available_effectors = []
            for modifier in obj.modifiers:
                if modifier.type == 'NODES' and modifier.node_group:
                    node_group_name = modifier.node_group.name.lower()
                    if ('effector' in node_group_name or 'random' in node_group_name or 'noise' in node_group_name):
                        available_effectors.append(modifier.name)
            
            if available_effectors:
                self.report({'INFO'}, f"Created Sphere Field. Available effectors: {', '.join(available_effectors)}. Use Field UI to connect.")
            else:
                self.report({'INFO'}, f"Created Sphere Field. Add effectors and use Field UI to connect them.")
            
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Failed to create sphere field: {str(e)}")
            return {'CANCELLED'}
    
    def position_field_modifier(self, obj, field_modifier):
        """Позиционирование field модификатора перед эффекторами"""
        # Field должен быть после клонеров, но перед эффекторами
        
        # Находим первый эффектор
        first_effector_index = -1
        for i, mod in enumerate(obj.modifiers):
            if mod.type == 'NODES' and mod.node_group:
                node_group_name = mod.node_group.name.lower()
                if 'effector' in node_group_name:
                    first_effector_index = i
                    break
        
        # Если есть эффекторы, позиционируем поле перед ними
        if first_effector_index >= 0:
            current_index = len(obj.modifiers) - 1  # Новый модификатор в конце
            
            # Перемещаем вверх до позиции перед первым эффектором
            while current_index > first_effector_index:
                bpy.ops.object.modifier_move_up(modifier=field_modifier.name)
                current_index -= 1
    
    def connect_field_to_effectors(self, obj, field_modifier):
        """Автоматически подключает field node group к эффекторам на объекте"""
        connected_effectors = []

        print(f"Searching for effectors on object '{obj.name}'...")
        print(f"Total modifiers: {len(obj.modifiers)}")

        # Определяем тип поля
        field_type = self._detect_field_type_from_modifier(field_modifier)
        print(f"Detected field type: {field_type}")

        for i, modifier in enumerate(obj.modifiers):
            print(f"Modifier {i}: {modifier.name}, type: {modifier.type}")

            if modifier.type == 'NODES' and modifier.node_group:
                node_group_name = modifier.node_group.name
                print(f"  Node group: '{node_group_name}'")

                # Более широкий поиск эффекторов
                is_effector = ('effector' in node_group_name.lower() or
                              'random' in node_group_name.lower() or
                              'noise' in node_group_name.lower())

                if is_effector:
                    print(f"  Found effector: {modifier.name}")

                    # Создаем node group поля для использования в эффекторе (универсально)
                    field_node_group = self._create_field_node_group_for_effector(field_type)

                    if field_node_group:
                        # Добавляем node group поля внутрь эффектора
                        success = self.add_field_node_to_effector(modifier, field_node_group)
                        if success:
                            connected_effectors.append(modifier.name)
                            print(f"  ✓ Connected {field_type} field node group to {modifier.name}")
                        else:
                            print(f"  ✗ Failed to connect field to {modifier.name}")
                    else:
                        print(f"  ✗ Failed to create {field_type} field node group")
                else:
                    print(f"  Skipped: not an effector")

        print(f"Connected {len(connected_effectors)} effectors total")
        return connected_effectors

    def _detect_field_type_from_modifier(self, field_modifier):
        """Определить тип поля по модификатору"""
        if not field_modifier or not field_modifier.node_group:
            return "SPHERE"  # Fallback

        node_group_name = field_modifier.node_group.name.lower()

        # Проверяем по имени node group
        if 'sphere' in node_group_name:
            return "SPHERE"
        elif 'box' in node_group_name:
            return "BOX"
        elif 'ring' in node_group_name:
            return "RING"
        elif 'object' in node_group_name:
            return "OBJECT"

        # Проверяем по имени модификатора
        modifier_name = field_modifier.name.lower()
        if 'sphere' in modifier_name:
            return "SPHERE"
        elif 'box' in modifier_name:
            return "BOX"
        elif 'ring' in modifier_name:
            return "RING"
        elif 'object' in modifier_name:
            return "OBJECT"

        # Fallback
        return "SPHERE"

    def _create_field_node_group_for_effector(self, field_type):
        """Универсальное создание field node group для использования в эффекторах"""
        try:
            from ...core.registry.field_registry import create_field_via_registry, get_field_class

            # Создаем node group для использования внутри эффекторов
            field_node_group_name = f"{field_type}FieldNodeGroup"

            if field_node_group_name not in bpy.data.node_groups:
                # Получаем класс поля
                field_class = get_field_class(field_type)
                if field_class:
                    field_instance = field_class()
                    # Создаем node group специально для эффекторов
                    field_node_group = field_instance.create_field_node_group_for_effectors("NodeGroup")
                    if field_node_group:
                        field_node_group.name = field_node_group_name
                        print(f"✅ Created {field_type} field node group for effectors: {field_node_group_name}")
                        return field_node_group
                    else:
                        print(f"❌ Failed to create {field_type} field node group")
                        return None
                else:
                    print(f"❌ Unknown field type: {field_type}")
                    return None
            else:
                field_node_group = bpy.data.node_groups[field_node_group_name]
                print(f"✅ Using existing {field_type} field node group: {field_node_group_name}")
                return field_node_group

        except Exception as e:
            print(f"❌ Error creating {field_type} field node group: {e}")
            import traceback
            traceback.print_exc()
            return None

    def add_field_node_to_effector(self, effector_modifier, field_node_group):
        """Добавляет field node group внутрь эффектора и подключает к Field input"""
        try:
            if not effector_modifier.node_group:
                return False
                
            nodes = effector_modifier.node_group.nodes
            links = effector_modifier.node_group.links
            
            # Добавляем field node group
            field_node = nodes.new('GeometryNodeGroup')
            field_node.node_tree = field_node_group
            field_node.name = "Field"
            field_node.location = (-800, -800)
            
            # Ищем Group Input узел и Field input
            group_input = None
            field_input_socket = None
            
            for node in nodes:
                if node.type == 'GROUP_INPUT':
                    group_input = node
                    break
            
            if group_input:
                # Ищем Field input socket
                for socket in group_input.outputs:
                    if socket.name == "Field":
                        field_input_socket = socket
                        break
                        
                if field_input_socket:
                    # Ищем узел, который использует Field input
                    field_target_node = None
                    field_target_input = None
                    
                    for link in field_input_socket.links:
                        field_target_node = link.to_node
                        field_target_input = link.to_socket
                        break
                    
                    if field_target_node and field_target_input:
                        # Отключаем старую связь
                        links.remove(link)
                        
                        # Подключаем field node group к target узлу
                        links.new(field_node.outputs['Field'], field_target_input)
                        print(f"    Connected field output to {field_target_node.name}")
                        return True
                    else:
                        print(f"    No node uses Field input")
                        return False
                else:
                    print(f"    Field input socket not found")
                    return False
            else:
                print(f"    Group Input node not found")
                return False
                
        except Exception as e:
            print(f"    Error adding field node: {e}")
            return False
    
    def setup_field_parameter_synchronization(self, obj, field_modifier, connected_effectors):
        """Setup parameter synchronization between field modifier and embedded field node groups"""
        try:
            from ...core.services.field_synchronization import get_field_sync_service
            
            sync_service = get_field_sync_service()
            
            # Register connections for each effector
            for effector_name in connected_effectors:
                effector_modifier = obj.modifiers.get(effector_name)
                if effector_modifier:
                    # Register the connection
                    sync_service.register_field_connection(
                        f"{obj.name}.{field_modifier.name}",
                        obj,
                        effector_modifier,
                        "Field"  # Name of the field node inside the effector
                    )
            
            # ВРЕМЕННО ОТКЛЮЧЕНО: Create drivers to link parameters
            # sync_service.create_field_parameter_drivers(obj, field_modifier, "SPHERE")
            print(f"[DEBUG] Skipping driver creation - allowing direct node control")
            
            print(f"Setup parameter synchronization for field {field_modifier.name}")
            
        except Exception as e:
            print(f"Failed to setup field parameter synchronization: {e}")

class CLONERPRO_OT_force_field_update(Operator):
    """Оператор принудительного обновления поля"""
    bl_idname = "clonerpro.force_field_update"
    bl_label = "Force Field Update"
    bl_description = "Force update field geometry"
    bl_options = {'REGISTER', 'UNDO'}

    # Параметры объекта и модификатора
    object_name: bpy.props.StringProperty(
        name="Object Name",
        description="Name of the object with field modifier"
    )

    modifier_name: bpy.props.StringProperty(
        name="Modifier Name",
        description="Name of the field modifier"
    )

    def execute(self, context):
        """Принудительное обновление поля"""
        try:
            # Найти объект и модификатор
            obj = bpy.data.objects.get(self.object_name)
            if not obj:
                self.report({'ERROR'}, f"Object '{self.object_name}' not found")
                return {'CANCELLED'}

            modifier = obj.modifiers.get(self.modifier_name)
            if not modifier:
                self.report({'ERROR'}, f"Modifier '{self.modifier_name}' not found")
                return {'CANCELLED'}

            # Принудительная синхронизация параметров
            from ...core.services.field_synchronization import get_field_sync_service
            sync_service = get_field_sync_service()
            
            # Пересоздать драйверы для синхронизации
            sync_service.create_field_parameter_drivers(obj, modifier, "SPHERE")
            
            # Принудительное обновление
            from ...ui.utils.ui_helpers import force_field_update
            force_field_update(context, modifier)

            self.report({'INFO'}, f"Field '{self.modifier_name}' parameters synchronized and updated")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Failed to update field: {e}")
            return {'CANCELLED'}


class CLONERPRO_OT_connect_field_to_effector(Operator):
    """Оператор подключения филда к эффектору"""
    bl_idname = "clonerpro.connect_field_to_effector"
    bl_label = "Connect Field to Effector"
    bl_description = "Connect field to specific effector"
    bl_options = {'REGISTER', 'UNDO'}

    # Параметры
    field_modifier_name: bpy.props.StringProperty(
        name="Field Modifier",
        description="Name of the field modifier"
    )
    
    effector_modifier_name: bpy.props.StringProperty(
        name="Effector Modifier", 
        description="Name of the effector modifier"
    )

    def execute(self, context):
        """Подключить филд к эффектору"""
        try:
            obj = context.active_object
            if not obj:
                self.report({'ERROR'}, "No active object")
                return {'CANCELLED'}

            # Найти модификаторы
            field_modifier = obj.modifiers.get(self.field_modifier_name)
            effector_modifier = obj.modifiers.get(self.effector_modifier_name)
            
            if not field_modifier:
                self.report({'ERROR'}, f"Field modifier '{self.field_modifier_name}' not found")
                return {'CANCELLED'}
                
            if not effector_modifier:
                self.report({'ERROR'}, f"Effector modifier '{self.effector_modifier_name}' not found")
                return {'CANCELLED'}

            # Определить тип поля и создать соответствующий node group
            field_type = self._detect_field_type_from_modifier(field_modifier)
            field_node_group = self._create_field_node_group_for_effector(field_type)

            if not field_node_group:
                self.report({'ERROR'}, f"Failed to create {field_type} field node group")
                return {'CANCELLED'}
            
            # Подключить к конкретному эффектору
            success = self.add_field_node_to_effector(effector_modifier, field_node_group)
            
            if success:
                # Настроить синхронизацию
                self.setup_field_parameter_synchronization(obj, field_modifier, [self.effector_modifier_name])
                self.report({'INFO'}, f"Connected field '{self.field_modifier_name}' to effector '{self.effector_modifier_name}'")
            else:
                self.report({'ERROR'}, f"Failed to connect field to effector")
                
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Failed to connect field: {e}")
            import traceback
            traceback.print_exc()
            return {'CANCELLED'}
    
    def add_field_node_to_effector(self, effector_modifier, field_node_group):
        """Добавляет field node group внутрь эффектора и подключает к Field input"""
        try:
            if not effector_modifier.node_group:
                return False
                
            nodes = effector_modifier.node_group.nodes
            links = effector_modifier.node_group.links
            
            # Проверить, нет ли уже field node
            for node in nodes:
                if node.name == "Field" and node.type == 'GROUP':
                    print(f"Field node already exists in {effector_modifier.name}")
                    return True  # Уже подключен
            
            # Добавляем field node group
            field_node = nodes.new('GeometryNodeGroup')
            field_node.node_tree = field_node_group
            field_node.name = "Field"
            field_node.location = (-800, -800)
            
            # Ищем Group Input узел и Field input
            group_input = None
            field_input_socket = None
            
            for node in nodes:
                if node.type == 'GROUP_INPUT':
                    group_input = node
                    break
            
            if group_input:
                # Ищем Field input socket
                for socket in group_input.outputs:
                    if socket.name == "Field":
                        field_input_socket = socket
                        break
                        
                if field_input_socket:
                    # Ищем узел, который использует Field input
                    field_target_node = None
                    field_target_input = None
                    
                    for link in field_input_socket.links:
                        field_target_node = link.to_node
                        field_target_input = link.to_socket
                        break
                    
                    if field_target_node and field_target_input:
                        # Отключаем старую связь
                        links.remove(link)

                        # Подключаем field node group к target узлу
                        links.new(field_node.outputs['Field'], field_target_input)
                        print(f"Connected field output to {field_target_node.name}")
                        return True
                    else:
                        print(f"No node uses Field input")
                        return False
                else:
                    print(f"Field input socket not found")
                    return False
            else:
                print(f"Group Input node not found")
                return False
                
        except Exception as e:
            print(f"Error adding field node: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _detect_field_type_from_modifier(self, field_modifier):
        """Определить тип поля по модификатору"""
        if not field_modifier or not field_modifier.node_group:
            return "SPHERE"  # Fallback

        node_group_name = field_modifier.node_group.name.lower()

        # Проверяем по имени node group
        if 'sphere' in node_group_name:
            return "SPHERE"
        elif 'box' in node_group_name:
            return "BOX"
        elif 'ring' in node_group_name:
            return "RING"
        elif 'object' in node_group_name:
            return "OBJECT"

        # Проверяем по имени модификатора
        modifier_name = field_modifier.name.lower()
        if 'sphere' in modifier_name:
            return "SPHERE"
        elif 'box' in modifier_name:
            return "BOX"
        elif 'ring' in modifier_name:
            return "RING"
        elif 'object' in modifier_name:
            return "OBJECT"

        # Fallback
        return "SPHERE"

    def _create_field_node_group_for_effector(self, field_type):
        """Универсальное создание field node group для использования в эффекторах"""
        try:
            from ...core.registry.field_registry import get_field_class

            # Создаем node group для использования внутри эффекторов
            field_node_group_name = f"{field_type}FieldNodeGroup"

            if field_node_group_name not in bpy.data.node_groups:
                # Получаем класс поля
                field_class = get_field_class(field_type)
                if field_class:
                    field_instance = field_class()
                    # Создаем node group специально для эффекторов
                    field_node_group = field_instance.create_field_node_group_for_effectors("NodeGroup")
                    if field_node_group:
                        field_node_group.name = field_node_group_name
                        print(f"✅ Created {field_type} field node group for effectors: {field_node_group_name}")
                        return field_node_group
                    else:
                        print(f"❌ Failed to create {field_type} field node group")
                        return None
                else:
                    print(f"❌ Unknown field type: {field_type}")
                    return None
            else:
                field_node_group = bpy.data.node_groups[field_node_group_name]
                print(f"✅ Using existing {field_type} field node group: {field_node_group_name}")
                return field_node_group

        except Exception as e:
            print(f"❌ Error creating {field_type} field node group: {e}")
            import traceback
            traceback.print_exc()
            return None

    def setup_field_parameter_synchronization(self, obj, field_modifier, connected_effectors):
        """Setup parameter synchronization between field modifier and embedded field node groups"""
        try:
            from ...core.services.field_synchronization import get_field_sync_service
            
            sync_service = get_field_sync_service()
            
            # Register connections for each effector
            for effector_name in connected_effectors:
                effector_modifier = obj.modifiers.get(effector_name)
                if effector_modifier:
                    # Register the connection
                    sync_service.register_field_connection(
                        f"{obj.name}.{field_modifier.name}",
                        obj,
                        effector_modifier,
                        "Field"  # Name of the field node inside the effector
                    )
            
            # ВРЕМЕННО ОТКЛЮЧЕНО: Create drivers to link parameters
            # sync_service.create_field_parameter_drivers(obj, field_modifier, "SPHERE")
            
            print(f"Setup parameter synchronization for field {field_modifier.name}")
            
        except Exception as e:
            print(f"Failed to setup field parameter synchronization: {e}")
            import traceback
            traceback.print_exc()


class CLONERPRO_OT_disconnect_field_from_effector(Operator):
    """Оператор отключения филда от эффектора"""
    bl_idname = "clonerpro.disconnect_field_from_effector"
    bl_label = "Disconnect Field from Effector"
    bl_description = "Disconnect field from specific effector"
    bl_options = {'REGISTER', 'UNDO'}

    # Параметры
    field_modifier_name: bpy.props.StringProperty(
        name="Field Modifier",
        description="Name of the field modifier"
    )
    
    effector_modifier_name: bpy.props.StringProperty(
        name="Effector Modifier",
        description="Name of the effector modifier"
    )

    def execute(self, context):
        """Отключить филд от эффектора"""
        try:
            obj = context.active_object
            if not obj:
                self.report({'ERROR'}, "No active object")
                return {'CANCELLED'}

            # Найти эффектор
            effector_modifier = obj.modifiers.get(self.effector_modifier_name)
            if not effector_modifier or not effector_modifier.node_group:
                self.report({'ERROR'}, f"Effector modifier '{self.effector_modifier_name}' not found")
                return {'CANCELLED'}

            # Найти и удалить field node из эффектора
            field_node = None
            for node in effector_modifier.node_group.nodes:
                if node.name == "Field" and node.type == 'GROUP':
                    field_node = node
                    break
            
            if field_node:
                # Восстановить прямое подключение Field input если нужно
                group_input = None
                for node in effector_modifier.node_group.nodes:
                    if node.type == 'GROUP_INPUT':
                        group_input = node
                        break
                
                if group_input:
                    # Найти что было подключено к field node output
                    field_targets = []
                    for output in field_node.outputs:
                        for link in output.links:
                            field_targets.append((link.to_node, link.to_socket))
                    
                    # Удалить field node
                    effector_modifier.node_group.nodes.remove(field_node)
                    
                    # Восстановить подключения к Field input
                    for field_input in group_input.outputs:
                        if field_input.name == "Field":
                            for target_node, target_socket in field_targets:
                                effector_modifier.node_group.links.new(field_input, target_socket)
                            break
                
                # Очистить из сервиса синхронизации
                from ...core.services.field_synchronization import get_field_sync_service
                sync_service = get_field_sync_service()
                field_modifier = obj.modifiers.get(self.field_modifier_name)
                if field_modifier:
                    sync_service.cleanup_field_connections(obj, field_modifier)
                
                self.report({'INFO'}, f"Disconnected field '{self.field_modifier_name}' from effector '{self.effector_modifier_name}'")
            else:
                self.report({'WARNING'}, f"No field connection found in effector '{self.effector_modifier_name}'")
                
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Failed to disconnect field: {e}")
            return {'CANCELLED'}


def register():
    """Регистрация операторов создания полей"""
    bpy.utils.register_class(CLONERPRO_OT_create_field)
    bpy.utils.register_class(CLONERPRO_OT_force_field_update)
    bpy.utils.register_class(CLONERPRO_OT_connect_field_to_effector)
    bpy.utils.register_class(CLONERPRO_OT_disconnect_field_from_effector)


def unregister():
    """Отмена регистрации операторов создания полей"""
    bpy.utils.unregister_class(CLONERPRO_OT_disconnect_field_from_effector)
    bpy.utils.unregister_class(CLONERPRO_OT_connect_field_to_effector)
    bpy.utils.unregister_class(CLONERPRO_OT_force_field_update)
    bpy.utils.unregister_class(CLONERPRO_OT_create_field)