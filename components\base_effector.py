"""
Base Effector Class для ClonerPro
Базовый класс для всех эффекторов с общей функциональностью
"""

import bpy
from .base import BaseComponent


class BaseEffector(BaseComponent):
    """
    Базовый класс для всех эффекторов ClonerPro

    Предоставляет общую функциональность:
    - Создание базовых сокетов эффектора
    - Применение трансформаций (позиция, поворот, масштаб)
    - Работа с филдами для пространственного контроля
    - Система включения/выключения эффектора
    """

    component_type = "EFFECTOR"

    def __init__(self):
        super().__init__()

    def get_base_effector_sockets(self):
        """
        Базовые сокеты, общие для всех эффекторов

        Returns:
            list: Список базовых сокетов (name, socket_type, in_out, default, min, max)
        """
        return [
            # Геометрия
            ("Geometry", "NodeSocketGeometry", "INPUT", None),
            ("Geometry", "NodeSocketGeometry", "OUTPUT", None),

            # Основные настройки эффектора
            ("Enable", "NodeSocketBool", "INPUT", True),
            ("Strength", "NodeSocketFloat", "INPUT", 1.0, 0.0, 2.0),

            # Филд для пространственного контроля
            ("Field", "NodeSocketFloat", "INPUT", 1.0, 0.0, 1.0),

            # Трансформации
            ("Position", "NodeSocketVector", "INPUT", (1.0, 1.0, 1.0)),
            ("Rotation", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),
            ("Scale", "NodeSocketVector", "INPUT", (0.1, 0.1, 0.1)),

            # Рандомизация
            ("Seed", "NodeSocketInt", "INPUT", 0, 0),
        ]

    def register_base_properties(self, props_owner):
        """
        Регистрация базовых свойств, общих для всех эффекторов

        Args:
            props_owner: Объект для регистрации свойств
        """
        # Основные настройки эффектора
        props_owner.effector_enable = bpy.props.BoolProperty(
            name="Enable",
            description="Enable/disable the effector",
            default=True
        )
        props_owner.effector_strength = bpy.props.FloatProperty(
            name="Strength",
            description="Overall strength of the effect",
            default=1.0,
            min=0.0,
            max=2.0
        )

        # Филд
        props_owner.effector_field = bpy.props.FloatProperty(
            name="Field",
            description="Field strength for spatial control",
            default=1.0,
            min=0.0,
            max=1.0
        )

        # Трансформации
        props_owner.effector_position = bpy.props.FloatVectorProperty(
            name="Position",
            description="Position transformation range",
            default=(1.0, 1.0, 1.0),
            size=3
        )
        props_owner.effector_rotation = bpy.props.FloatVectorProperty(
            name="Rotation",
            description="Rotation transformation range",
            default=(0.0, 0.0, 0.0),
            size=3
        )
        props_owner.effector_scale = bpy.props.FloatVectorProperty(
            name="Scale",
            description="Scale transformation range",
            default=(0.1, 0.1, 0.1),
            size=3
        )

        # Рандомизация
        props_owner.effector_seed = bpy.props.IntProperty(
            name="Seed",
            description="Seed for random number generation",
            default=0,
            min=0
        )

    def create_effector_interface(self, node_group, specific_sockets=None):
        """
        Создание интерфейса эффектора (базовые + специфичные сокеты)

        Args:
            node_group: Node group для создания интерфейса
            specific_sockets: Список специфичных сокетов для данного эффектора

        Returns:
            dict: Маппинг имен сокетов к их объектам
        """
        socket_mapping = {}

        # Создаем базовые сокеты
        base_sockets = self.get_base_effector_sockets()
        for socket_info in base_sockets:
            name, socket_type, in_out = socket_info[:3]
            default = socket_info[3] if len(socket_info) > 3 else None
            min_val = socket_info[4] if len(socket_info) > 4 else None
            max_val = socket_info[5] if len(socket_info) > 5 else None

            socket = node_group.interface.new_socket(
                name=name,
                in_out=in_out,
                socket_type=socket_type
            )

            # Устанавливаем значения по умолчанию и ограничения
            if default is not None and hasattr(socket, 'default_value'):
                socket.default_value = default
            if min_val is not None and hasattr(socket, 'min_value'):
                socket.min_value = min_val
            if max_val is not None and hasattr(socket, 'max_value'):
                socket.max_value = max_val

            # Специальные настройки для филда
            if name == "Field" and hasattr(socket, 'attribute_domain'):
                socket.attribute_domain = 'POINT'

            socket_mapping[name] = socket

        # Создаем специфичные сокеты
        if specific_sockets:
            for socket_info in specific_sockets:
                name, socket_type, in_out = socket_info[:3]
                default = socket_info[3] if len(socket_info) > 3 else None
                min_val = socket_info[4] if len(socket_info) > 4 else None
                max_val = socket_info[5] if len(socket_info) > 5 else None

                socket = node_group.interface.new_socket(
                    name=name,
                    in_out=in_out,
                    socket_type=socket_type
                )

                if default is not None and hasattr(socket, 'default_value'):
                    socket.default_value = default
                if min_val is not None and hasattr(socket, 'min_value'):
                    socket.min_value = min_val
                if max_val is not None and hasattr(socket, 'max_value'):
                    socket.max_value = max_val

                socket_mapping[name] = socket

        return socket_mapping

    def create_base_nodes(self, node_group):
        """
        Создание базовых нодов, общих для всех эффекторов

        Args:
            node_group: Node group

        Returns:
            dict: Словарь с созданными нодами
        """
        nodes = node_group.nodes
        links = node_group.links

        # Очищаем существующие ноды
        nodes.clear()

        # Группа input/output
        group_input = nodes.new('NodeGroupInput')
        group_input.location = (-800, 0)

        group_output = nodes.new('NodeGroupOutput')
        group_output.location = (800, 0)

        # Switch для включения/выключения эффектора
        switch = nodes.new('GeometryNodeSwitch')
        switch.input_type = 'GEOMETRY'
        switch.location = (600, 0)
        links.new(group_input.outputs['Enable'], switch.inputs[0])  # Switch
        links.new(group_input.outputs['Geometry'], switch.inputs[1])  # False (bypass)

        # Index для рандомизации per-instance
        index = nodes.new('GeometryNodeInputIndex')
        index.location = (-600, -200)

        base_nodes = {
            'group_input': group_input,
            'group_output': group_output,
            'switch': switch,
            'index': index,
            'nodes': nodes,
            'links': links
        }

        return base_nodes

    def apply_field_multiplication(self, base_nodes, transformation_output, field_socket_name="Field"):
        """
        Применение филда к трансформации (умножение на значение филда)

        Args:
            base_nodes: Словарь с базовыми нодами
            transformation_output: Выход трансформации для умножения на филд
            field_socket_name: Имя сокета филда

        Returns:
            NodeSocket: Результат умножения на филд
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']

        # Создаем ноду для умножения на филд
        field_multiply = nodes.new('ShaderNodeVectorMath')
        field_multiply.operation = 'MULTIPLY'
        field_multiply.location = (200, -100)

        # Подключаем трансформацию и филд
        links.new(transformation_output, field_multiply.inputs[0])
        links.new(group_input.outputs[field_socket_name], field_multiply.inputs[1])

        return field_multiply.outputs['Vector']

    def create_effector_node_group(self, name_suffix=""):
        """
        Создание node group эффектора с полной логикой

        Args:
            name_suffix: Суффикс для имени группы

        Returns:
            bpy.types.NodeGroup: Созданная группа нодов
        """
        # НОВАЯ ЛОГИКА: Создаём wrapper-обёртку вместо прямого размещения нодов
        from ..core.wrappers import WrapperNodeGroup

        wrapper = WrapperNodeGroup(self)
        return wrapper.create_wrapper_node_group(name_suffix)

    def create_effector_logic_group(self, name_suffix=""):
        """
        Создание node group с логикой эффектора (без wrapper-обёртки)
        Используется внутри wrapper-системы

        Args:
            name_suffix: Суффикс для имени группы

        Returns:
            bpy.types.NodeGroup: Созданная группа нодов с логикой
        """
        # Создаем новую группу узлов
        group_name = f"{self.bl_label.replace(' ', '')}{name_suffix}"
        node_group = bpy.data.node_groups.new(name=group_name, type='GeometryNodeTree')

        # Создаем интерфейс (базовые + специфичные сокеты)
        specific_sockets = self.get_specific_sockets()
        socket_mapping = self.create_effector_interface(node_group, specific_sockets)

        # Создаем базовые ноды
        base_nodes = self.create_base_nodes(node_group)

        # Реализуем логику эффектора (переопределяется в дочерних классах)
        final_geometry = self._create_effector_logic(base_nodes)

        # Подключаем к switch и выходу
        base_nodes['links'].new(final_geometry, base_nodes['switch'].inputs[2])  # True (with effect)
        base_nodes['links'].new(base_nodes['switch'].outputs['Output'], base_nodes['group_output'].inputs['Geometry'])

        return node_group

    def get_specific_sockets(self):
        """
        Специфичные сокеты для конкретного эффектора
        Переопределяется в дочерних классах

        Returns:
            list: Список специфичных сокетов
        """
        return []

    def _create_effector_logic(self, base_nodes):
        """
        Основная логика эффектора - переопределяется в дочерних классах

        Args:
            base_nodes: Словарь с базовыми нодами

        Returns:
            NodeSocket: Финальный выход геометрии с примененным эффектом
        """
        # Базовая реализация - просто возвращаем входную геометрию
        return base_nodes['group_input'].outputs['Geometry']

    def get_default_parameters(self):
        """Параметры по умолчанию для базового эффектора"""
        return {
            "enable": True,
            "strength": 1.0,
            "field": 1.0,
            "position": (1.0, 1.0, 1.0),
            "rotation": (0.0, 0.0, 0.0),
            "scale": (0.1, 0.1, 0.1),
            "seed": 0
        }

    def get_effector_parameter_groups(self):
        """
        Базовая группировка параметров для UI эффекторов

        Returns:
            dict: Группы параметров
        """
        return {
            "Effector Settings": ["Enable", "Strength", "Field"],
            "Transform": ["Position", "Rotation", "Scale"],
            "Random Settings": ["Seed"]
        }

    def register_properties(self, props_owner):
        """
        Регистрация всех свойств эффектора
        Базовая реализация - только базовые свойства
        """
        self.register_base_properties(props_owner)

    def create_node_group(self, name_suffix=""):
        """
        Публичный метод для создания node group
        Алиас для create_effector_node_group для совместимости
        """
        return self.create_effector_node_group(name_suffix)

    def draw_ui(self, layout, context, modifier):
        """
        Отрисовка UI эффектора
        Базовая реализация - переопределяется в дочерних классах
        """
        layout.label(text=f"{self.bl_label} UI not implemented")

    def validate_parameters(self, parameters):
        """
        Валидация параметров эффектора

        Args:
            parameters: Словарь параметров для проверки

        Returns:
            tuple: (is_valid: bool, error_message: str)
        """
        # Базовая валидация
        required_params = ["enable", "strength"]
        for param in required_params:
            if param not in parameters:
                return False, f"Missing required parameter: {param}"

        # Проверка диапазонов
        if "strength" in parameters:
            strength = parameters["strength"]
            if not (0.0 <= strength <= 2.0):
                return False, "Strength must be between 0.0 and 2.0"

        return True, ""