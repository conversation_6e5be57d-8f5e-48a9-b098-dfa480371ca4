"""
Effector UI Generator для ClonerPro
DEPRECATED: Эффекторы переведены на классовую архитектуру
"""

import bpy
from ...ui.utils.ui_state import is_effector_expanded


def draw_effector_ui_simple(context, layout, obj, modifier):
    """
    Простая отрисовка UI эффектора - ПРЯМАЯ РЕАЛИЗАЦИЯ
    Копия логики из advanced_cloners/ui/panels/effector_interface/effector_display.py БЕЗ сложности
    """
    try:
        # Определяем тип эффектора
        effector_type = detect_effector_type_simple(modifier)
        
        if effector_type == "UNKNOWN":
            return
        
        box = layout.box()
        
        # 1. Заголовок эффектора
        draw_effector_header_simple(context, box, obj, modifier, effector_type)
        
        # 2. Проверяем, развернут ли эффектор
        expanded = is_effector_expanded(context, obj.name, modifier.name)
        if not expanded:
            return
        
        # 3. Информация о статусе эффектора убрана - не нужна
        
        # 5. Параметры эффектора
        draw_effector_parameters(box, modifier)
        
    except Exception as e:
        print(f"Error drawing effector UI: {e}")


def detect_effector_type_simple(modifier):
    """
    Простое определение типа эффектора
    """
    if not modifier.node_group:
        return "UNKNOWN"
    
    node_group_name = modifier.node_group.name.lower()
    
    if 'random' in node_group_name and 'effector' in node_group_name:
        return 'RANDOM'
    elif 'noise' in node_group_name and 'effector' in node_group_name:
        return 'NOISE'
    elif 'step' in node_group_name and 'effector' in node_group_name:
        return 'STEP'
    elif 'plain' in node_group_name and 'effector' in node_group_name:
        return 'PLAIN'
    
    # Проверяем метаданные
    effector_type = modifier.get("effector_type")
    if effector_type:
        return effector_type
    
    return "UNKNOWN"


def draw_effector_header_simple(context, layout, obj, modifier, effector_type):
    """
    Простой заголовок эффектора
    """
    row = layout.row(align=True)
    
    # Треугольник expand/collapse
    expanded = is_effector_expanded(context, obj.name, modifier.name)
    icon = 'TRIA_DOWN' if expanded else 'TRIA_RIGHT'
    
    toggle_op = row.operator("clonerpro.toggle_effector_expanded", text="", icon=icon, emboss=False)
    toggle_op.object_name = obj.name
    toggle_op.modifier_name = modifier.name
    
    # Кастомная фиолетовая иконка для эффектора
    from ...ui.icons import get_icon_id
    effector_icon = get_icon_id("effector_purple")

    row.label(text=f"{effector_type.title()}", icon_value=effector_icon)
    
    # Кнопки управления
    controls = row.row(align=True)
    controls.scale_x = 0.8
    
    # Перемещение вверх/вниз
    move_up_op = controls.operator("clonerpro.move_effector_up", text="", icon="TRIA_UP", emboss=False)
    move_up_op.object_name = obj.name
    move_up_op.modifier_name = modifier.name
    
    move_down_op = controls.operator("clonerpro.move_effector_down", text="", icon="TRIA_DOWN", emboss=False)
    move_down_op.object_name = obj.name
    move_down_op.modifier_name = modifier.name
    
    # Кнопка Move to Effector Group убрана по запросу
    
    # Видимость
    vis_icon = "HIDE_OFF" if modifier.show_viewport else "HIDE_ON"
    vis_op = controls.operator("clonerpro.toggle_effector_visibility", text="", icon=vis_icon, emboss=False)
    vis_op.object_name = obj.name
    vis_op.modifier_name = modifier.name
    
    # Удаление
    del_op = controls.operator("clonerpro.delete_effector", text="", icon="X", emboss=False)
    del_op.object_name = obj.name
    del_op.modifier_name = modifier.name





def is_cloner_modifier_simple(modifier):
    """Простая проверка, является ли модификатор клонером"""
    if not modifier.node_group:
        return False
        
    node_group_name = modifier.node_group.name.lower()
    cloner_keywords = ['cloner', 'grid', 'linear', 'circle', 'spiral', 'object', 'spline']
    
    return any(keyword in node_group_name for keyword in cloner_keywords)


def draw_effector_parameters(layout, modifier):
    """
    Отображает параметры эффектора через универсальную систему UI
    """
    from .universal_ui import draw_effector_ui_universal

    # Определяем тип эффектора
    effector_type = detect_effector_type_from_modifier(modifier)

    if effector_type == "UNKNOWN":
        layout.label(text="Unknown effector type", icon='ERROR')
        return

    # Используем универсальную систему отрисовки
    draw_effector_ui_universal(layout, modifier, effector_type)


def detect_effector_type_from_modifier(modifier):
    """Определяет тип эффектора по модификатору"""
    if not modifier.node_group:
        return "UNKNOWN"
    
    node_group_name = modifier.node_group.name.lower()
    
    if "random" in node_group_name:
        return "RANDOM"
    elif "noise" in node_group_name:
        return "NOISE"
    elif "step" in node_group_name:
        return "STEP"
    
    # Проверяем метаданные
    effector_type = modifier.get("effector_type")
    if effector_type:
        return effector_type.upper()
    
    return "UNKNOWN"


def get_group_icon(group_name):
    """Получить иконку для группы параметров"""
    icon_map = {
        "Effector Settings": "SETTINGS",
        "Transform Parameters": "OBJECT_DATA",
        "Noise Parameters": "FORCE_TURBULENCE", 
        "Random Parameters": "FORCE_TURBULENCE",
        "Animation": "TIME",
        "Falloff": "IPO_EASE_IN_OUT"
    }
    return icon_map.get(group_name, "SETTINGS")