"""
Effector Creation Manager для ClonerPro
Прямая логика создания модификаторов эффекторов БЕЗ фабрик и абстракций
"""

import bpy
# Система эффекторов через реестр
from ..registry.effector_registry import create_effector_via_registry

# Все эффекторы используют классовую архитектуру

from ..system.error_handling import safe_execute_operation, component_creation_safe
from ..system.validation import validate_operation_context, validate_component
from ..system.dependency_safety import safe_execute_effector_operation

# =============================================================================
# RANDOM EFFECTOR CREATION
# =============================================================================


@component_creation_safe
def create_random_effector_modifier(context, obj, effector_type="RANDOM"):
    """
    🛡️ БЕЗОПАСНОЕ создание Random Effector модификатора на объекте
    Прямая реализация с интегрированными системами безопасности
    """
    # 🛡️ КРИТИЧЕСКАЯ ВАЛИДАЦИЯ КОНТЕКСТА
    validate_operation_context()
    validate_component('object', obj)
    
    # 🛡️ БЕЗОПАСНОЕ ВЫПОЛНЕНИЕ ОПЕРАЦИИ С ЭФФЕКТОРОМ
    success, result = safe_execute_effector_operation(_create_random_effector_modifier_internal, context, obj, effector_type)
    
    if not success:
        print(f"[ERROR] Failed to create random effector: {result}")
        return None, result
    
    return result


def _create_random_effector_modifier_internal(context, obj, effector_type="RANDOM"):
    """
    Внутренняя функция создания Random Effector модификатора
    Выполняется внутри безопасного контекста
    """
    try:
        # 1. Создаем node group для Random Effector через новую систему
        node_group = create_effector_via_registry("RANDOM")

        if not node_group:
            return None, "Could not create Random Effector node group"
        
        # Removed debug log
        
        # 2. Создаем уникальное имя модификатора
        base_mod_name = "Random Effector"
        mod_name = base_mod_name
        counter = 1
        while mod_name in obj.modifiers:
            mod_name = f"{base_mod_name}.{counter:03d}"
            counter += 1
        
        # Removed debug log
        
        # КРИТИЧЕСКИ ВАЖНО: Принудительно очищаем ВСЕ handlers для этого имени
        # Это предотвращает краши при пересоздании эффектора с тем же именем
        _force_cleanup_effector_handlers(mod_name)
        
        # 3. Создаем модификатор
        modifier = obj.modifiers.new(name=mod_name, type='NODES')
        modifier.node_group = node_group
        
        # 4. Добавляем метаданные
        modifier["architecture_type"] = "standard"
        modifier["effector_type"] = effector_type
        
        # 5. Устанавливаем параметры по умолчанию для видимого эффекта
        for socket in node_group.interface.items_tree:
            if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                try:
                    if socket.name == "Enable":
                        modifier[socket.identifier] = True  # Включаем эффектор
                    elif socket.name == "Position":
                        # Устанавливаем заметные значения для позиции
                        modifier[socket.identifier] = (1.0, 1.0, 1.0)
                    elif socket.name == "Rotation":
                        # Устанавливаем заметные значения для поворота (в радианах)
                        modifier[socket.identifier] = (0.2, 0.2, 0.2)
                    elif socket.name == "Scale":
                        # Устанавливаем заметные значения для масштаба
                        modifier[socket.identifier] = (0.2, 0.2, 0.2)
                    elif socket.name == "Strength":
                        modifier[socket.identifier] = 1.0
                    elif socket.name == "Uniform Scale":
                        modifier[socket.identifier] = True
                    elif socket.name == "Seed":
                        modifier[socket.identifier] = 42  # Используем ненулевой seed
                except Exception as e:
                    print(f"Warning: Could not set parameter {socket.name}: {e}")
        
        # 6. Включаем модификатор в viewport - эффектор активен по умолчанию
        modifier.show_viewport = True
        
        # 7. Размещаем эффектор над модификаторами Realize Instances
        _position_effector_above_realize_instances(obj, modifier)
        
        # 8. Устанавливаем параметры из конфигурации
        # ВРЕМЕННО ОТКЛЮЧЕНО: set_effector_default_parameters(modifier, effector_type)
        
        print(f"✅ Created {effector_type} effector modifier: {mod_name}")
        return modifier, None
        
    except Exception as e:
        error_msg = f"Error creating Random Effector: {e}"
        print(f"❌ {error_msg}")
        return None, error_msg


# =============================================================================
# NOISE EFFECTOR CREATION
# =============================================================================

@component_creation_safe
def create_noise_effector_modifier(context, obj, effector_type="NOISE"):
    """
    🛡️ БЕЗОПАСНОЕ создание Noise Effector модификатора на объекте
    Прямая реализация с интегрированными системами безопасности
    """
    # 🛡️ КРИТИЧЕСКАЯ ВАЛИДАЦИЯ КОНТЕКСТА
    validate_operation_context()
    validate_component('object', obj)
    
    # 🛡️ БЕЗОПАСНОЕ ВЫПОЛНЕНИЕ ОПЕРАЦИИ С ЭФФЕКТОРОМ
    success, result = safe_execute_effector_operation(_create_noise_effector_modifier_internal, context, obj, effector_type)
    
    if not success:
        print(f"[ERROR] Failed to create noise effector: {result}")
        return None, result
    
    return result


def _create_noise_effector_modifier_internal(context, obj, effector_type="NOISE"):
    """
    Внутренняя функция создания Noise Effector модификатора
    Выполняется внутри безопасного контекста
    """
    try:
        # 1. Создаем node group для Noise Effector через новую систему
        node_group = create_effector_via_registry("NOISE")

        if not node_group:
            return None, "Could not create Noise Effector node group"
        
        # Removed debug log
        
        # 2. Создаем уникальное имя модификатора
        base_mod_name = "Noise Effector"
        mod_name = base_mod_name
        counter = 1
        while mod_name in obj.modifiers:
            mod_name = f"{base_mod_name}.{counter:03d}"
            counter += 1
        
        # Removed debug log
        
        # КРИТИЧЕСКИ ВАЖНО: Принудительно очищаем ВСЕ handlers для этого имени
        # Это предотвращает краши при пересоздании эффектора с тем же именем
        _force_cleanup_effector_handlers(mod_name)
        
        # 3. Создаем модификатор
        modifier = obj.modifiers.new(name=mod_name, type='NODES')
        modifier.node_group = node_group
        
        # 4. Добавляем метаданные
        modifier["architecture_type"] = "standard"
        modifier["effector_type"] = effector_type
        
        # 5. Устанавливаем параметры по умолчанию для видимого эффекта
        for socket in node_group.interface.items_tree:
            if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                try:
                    if socket.name == "Enable":
                        modifier[socket.identifier] = True  # Включаем эффектор
                    elif socket.name == "Position":
                        # Устанавливаем заметные значения для позиции
                        modifier[socket.identifier] = (2.0, 2.0, 2.0)
                    elif socket.name == "Rotation":
                        # Устанавливаем заметные значения для поворота
                        modifier[socket.identifier] = (0.3, 0.3, 0.3)
                    elif socket.name == "Scale":
                        # Устанавливаем заметные значения для масштаба
                        modifier[socket.identifier] = (0.3, 0.3, 0.3)
                    elif socket.name == "Strength":
                        modifier[socket.identifier] = 1.0
                    elif socket.name == "Uniform Scale":
                        modifier[socket.identifier] = True
                    elif socket.name == "Symmetric Translation":
                        modifier[socket.identifier] = False
                    elif socket.name == "Symmetric Rotation":
                        modifier[socket.identifier] = False
                    elif socket.name == "Noise Scale":
                        modifier[socket.identifier] = 5.0  # Увеличиваем для видимого эффекта
                    elif socket.name == "Noise Detail":
                        modifier[socket.identifier] = 2.0
                    elif socket.name == "Noise Roughness":
                        modifier[socket.identifier] = 0.5
                    elif socket.name == "Noise Lacunarity":
                        modifier[socket.identifier] = 2.0
                    elif socket.name == "Noise Distortion":
                        modifier[socket.identifier] = 0.0
                    elif socket.name == "Noise Position":
                        modifier[socket.identifier] = (0.0, 0.0, 0.0)
                    elif socket.name == "Noise XYZ Scale":
                        modifier[socket.identifier] = (1.0, 1.0, 1.0)
                    elif socket.name == "Speed":
                        modifier[socket.identifier] = 0.0
                    elif socket.name == "Seed":
                        modifier[socket.identifier] = 0
                except Exception as e:
                    print(f"Warning: Could not set parameter {socket.name}: {e}")
        
        # 6. Включаем модификатор в viewport - эффектор активен по умолчанию
        modifier.show_viewport = True
        
        # 7. Размещаем эффектор над модификаторами Realize Instances
        _position_effector_above_realize_instances(obj, modifier)
        
        # 8. Устанавливаем параметры из конфигурации
        # ВРЕМЕННО ОТКЛЮЧЕНО: set_effector_default_parameters(modifier, effector_type)
        
        print(f"✅ Created {effector_type} effector modifier: {mod_name}")
        return modifier, None
        
    except Exception as e:
        error_msg = f"Error creating Noise Effector: {e}"
        print(f"❌ {error_msg}")
        return None, error_msg


# =============================================================================
# STEP EFFECTOR CREATION
# =============================================================================

@component_creation_safe
def create_step_effector_modifier(context, obj, effector_type="STEP"):
    """
    🛡️ БЕЗОПАСНОЕ создание Step Effector модификатора на объекте
    Прямая реализация с интегрированными системами безопасности
    """
    # 🛡️ КРИТИЧЕСКАЯ ВАЛИДАЦИЯ КОНТЕКСТА
    validate_operation_context()
    validate_component('object', obj)
    
    # 🛡️ БЕЗОПАСНОЕ ВЫПОЛНЕНИЕ ОПЕРАЦИИ С ЭФФЕКТОРОМ
    success, result = safe_execute_effector_operation(_create_step_effector_modifier_internal, context, obj, effector_type)
    
    if not success:
        print(f"[ERROR] Failed to create step effector: {result}")
        return None, result
    
    return result


def _create_step_effector_modifier_internal(context, obj, effector_type="STEP"):
    """
    Внутренняя функция создания Step Effector модификатора
    Выполняется внутри безопасного контекста
    """
    try:
        # 1. Создаем node group для Step Effector через новую систему
        node_group = create_effector_via_registry("STEP")

        if not node_group:
            return None, "Could not create Step Effector node group"
        
        # 2. Создаем уникальное имя модификатора
        base_mod_name = "Step Effector"
        mod_name = base_mod_name
        counter = 1
        while mod_name in obj.modifiers:
            mod_name = f"{base_mod_name}.{counter:03d}"
            counter += 1
        
        # КРИТИЧЕСКИ ВАЖНО: Принудительно очищаем ВСЕ handlers для этого имени
        # Это предотвращает краши при пересоздании эффектора с тем же именем
        _force_cleanup_effector_handlers(mod_name)
        
        # 3. Создаем модификатор
        modifier = obj.modifiers.new(name=mod_name, type='NODES')
        modifier.node_group = node_group
        
        # 4. Добавляем метаданные
        modifier["architecture_type"] = "standard"
        modifier["effector_type"] = effector_type
        
        # 5. Устанавливаем параметры по умолчанию для видимого эффекта
        for socket in node_group.interface.items_tree:
            if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                try:
                    if socket.name == "Enable":
                        modifier[socket.identifier] = True  # Включаем эффектор
                    elif socket.name == "Position":
                        # Устанавливаем заметные значения для позиции
                        modifier[socket.identifier] = (1.0, 1.0, 1.0)
                    elif socket.name == "Rotation":
                        # Устанавливаем заметные значения для поворота (в радианах)
                        modifier[socket.identifier] = (0.2, 0.2, 0.2)
                    elif socket.name == "Scale":
                        # Устанавливаем заметные значения для масштаба
                        modifier[socket.identifier] = (0.2, 0.2, 0.2)
                    elif socket.name == "Strength":
                        modifier[socket.identifier] = 1.0
                    elif socket.name == "Uniform Scale":
                        modifier[socket.identifier] = False  # Step Effector default
                    elif socket.name == "Seed":
                        modifier[socket.identifier] = 42  # Используем ненулевой seed
                except Exception as e:
                    print(f"Warning: Could not set parameter {socket.name}: {e}")
        
        # 6. Включаем модификатор в viewport - эффектор активен по умолчанию
        modifier.show_viewport = True
        
        # 7. Позиционируем эффектор над Realize Instances
        _position_effector_above_realize_instances(obj, modifier)
        
        # 8. Устанавливаем параметры из конфигурации
        # ВРЕМЕННО ОТКЛЮЧЕНО: set_effector_default_parameters(modifier, effector_type)
        
        print(f"✅ Created {effector_type} effector modifier: {mod_name}")
        return modifier, None
        
    except Exception as e:
        error_msg = f"Error creating Step Effector: {e}"
        print(f"❌ {error_msg}")
        return None, error_msg


# =============================================================================
# EFFECTOR CLEANUP AND SAFETY
# =============================================================================

def _force_cleanup_effector_handlers(effector_name):
    """
    КРИТИЧЕСКИ ВАЖНАЯ функция: Принудительная очистка ВСЕХ handlers для эффектора.
    
    Предотвращает краши rna_NodeTree_refine при пересоздании эффектора с тем же именем.
    """
    try:
        print(f"[FORCE CLEANUP] Очищаем ВСЕ handlers для {effector_name}")
        
        # 1. Очищаем depsgraph_update_post handlers
        handlers_removed = 0
        handlers_to_remove = []
        
        for handler in bpy.app.handlers.depsgraph_update_post:
            try:
                # Проверяем по имени эффектора
                if (hasattr(handler, 'effector_name') and 
                    handler.effector_name == effector_name):
                    handlers_to_remove.append(handler)
                # Проверяем по содержанию в __name__ или __repr__
                elif (hasattr(handler, '__name__') and 
                      effector_name in str(handler.__name__)):
                    handlers_to_remove.append(handler)
                # Проверяем в строковом представлении handler
                elif effector_name in str(handler):
                    handlers_to_remove.append(handler)
            except (AttributeError, ReferenceError, TypeError):
                # Handler поврежден или содержит невалидные ссылки - удаляем
                handlers_to_remove.append(handler)
        
        for handler in handlers_to_remove:
            try:
                bpy.app.handlers.depsgraph_update_post.remove(handler)
                handlers_removed += 1
                print(f"[FORCE CLEANUP] Удален depsgraph handler #{handlers_removed}")
            except (ValueError, ReferenceError):
                pass
        
        # 2. Очищаем frame_change_post handlers
        handlers_to_remove = []
        
        for handler in bpy.app.handlers.frame_change_post:
            try:
                if (hasattr(handler, 'effector_name') and 
                    handler.effector_name == effector_name):
                    handlers_to_remove.append(handler)
                elif effector_name in str(handler):
                    handlers_to_remove.append(handler)
            except (AttributeError, ReferenceError, TypeError):
                handlers_to_remove.append(handler)
        
        for handler in handlers_to_remove:
            try:
                bpy.app.handlers.frame_change_post.remove(handler)
                handlers_removed += 1
                print(f"[FORCE CLEANUP] Удален frame_change handler #{handlers_removed}")
            except (ValueError, ReferenceError):
                pass
        
        # 3. КРИТИЧНО: Дополнительно очищаем все поврежденные handlers
        _cleanup_corrupted_handlers()
        
        print(f"[FORCE CLEANUP] ✅ Удалено {handlers_removed} handlers для {effector_name}")
        
    except Exception as e:
        print(f"[FORCE CLEANUP] Ошибка принудительной очистки handlers: {e}")
        # При ошибке выполняем экстренную очистку
        try:
            from ..system.safe_operations import emergency_cleanup_invalid_references
            emergency_cleanup_invalid_references()
        except:
            pass


def _cleanup_corrupted_handlers():
    """Дополнительная очистка поврежденных handlers"""
    try:
        # Очищаем depsgraph handlers с ReferenceError
        handlers_to_remove = []
        for handler in bpy.app.handlers.depsgraph_update_post:
            try:
                # Пытаемся получить доступ к handler - если он поврежден, будет ошибка
                _ = str(handler)
                if hasattr(handler, 'effector_name'):
                    _ = handler.effector_name  # Может вызвать ReferenceError
            except (ReferenceError, AttributeError):
                handlers_to_remove.append(handler)
        
        for handler in handlers_to_remove:
            try:
                bpy.app.handlers.depsgraph_update_post.remove(handler)
                print(f"[FORCE CLEANUP] Удален поврежденный depsgraph handler")
            except:
                pass
        
        # Очищаем frame_change handlers с ReferenceError
        handlers_to_remove = []
        for handler in bpy.app.handlers.frame_change_post:
            try:
                _ = str(handler)
                if hasattr(handler, 'effector_name'):
                    _ = handler.effector_name
            except (ReferenceError, AttributeError):
                handlers_to_remove.append(handler)
        
        for handler in handlers_to_remove:
            try:
                bpy.app.handlers.frame_change_post.remove(handler)
                print(f"[FORCE CLEANUP] Удален поврежденный frame_change handler")
            except:
                pass
                
    except Exception as e:
        print(f"[FORCE CLEANUP] Ошибка очистки поврежденных handlers: {e}")


# =============================================================================
# EFFECTOR CONFIGURATION AND DEFAULTS
# =============================================================================

def set_effector_default_parameters(modifier, effector_type):
    """
    Установить параметры эффектора по умолчанию из конфигурации
    """
    try:
        if effector_type == "RANDOM":
            # Random Effector теперь использует классовую архитектуру
            # Параметры получаем через класс
            from ..registry.effector_registry import get_effector_class
            effector_class = get_effector_class("RANDOM")
            if effector_class:
                effector_instance = effector_class()
                # Преобразуем группы параметров в совместимый формат
                param_groups = effector_instance.get_parameter_groups()
                params = {}
                for group_name, param_names in param_groups.items():
                    params[group_name] = []
                    for param_name in param_names:
                        params[group_name].append({
                            "socket_name": param_name,
                            "default": effector_instance.get_default_parameters().get(param_name.lower().replace(" ", "_"), 1.0)
                        })
            else:
                print("Warning: Could not get Random Effector class")
                return
        elif effector_type == "NOISE":
            # Noise Effector теперь использует классовую архитектуру
            # Параметры получаем через класс
            from ..registry.effector_registry import get_effector_class
            effector_class = get_effector_class("NOISE")
            if effector_class:
                effector_instance = effector_class()
                # Преобразуем группы параметров в совместимый формат
                param_groups = effector_instance.get_parameter_groups()
                params = {}
                for group_name, param_names in param_groups.items():
                    params[group_name] = []
                    for param_name in param_names:
                        params[group_name].append({
                            "socket_name": param_name,
                            "default": effector_instance.get_default_parameters().get(param_name.lower().replace(" ", "_"), 1.0)
                        })
            else:
                print("Warning: Could not get Noise Effector class")
                return
        elif effector_type == "STEP":
            # Step Effector использует классовую архитектуру
            # Параметры получаем через класс
            from ..registry.effector_registry import get_effector_class
            effector_class = get_effector_class("STEP")
            if effector_class:
                effector_instance = effector_class()
                # Преобразуем группы параметров в совместимый формат
                param_groups = effector_instance.get_parameter_groups()
                params = {}
                for group_name, param_names in param_groups.items():
                    params[group_name] = []
                    for param_name in param_names:
                        params[group_name].append({
                            "socket_name": param_name,
                            "default": effector_instance.get_default_parameters().get(param_name.lower().replace(" ", "_"), 1.0)
                        })
            else:
                print("Warning: Could not get Step Effector class")
                return
        else:
            print(f"Warning: Unknown effector type {effector_type}")
            return
            
        # Устанавливаем значения по умолчанию из конфигурации
        for group_name, parameters in params.items():
            for param in parameters:
                socket_name = param["socket_name"]
                default_value = param["default"]
                
                # Находим сокет в node group
                if modifier.node_group:
                    for socket in modifier.node_group.interface.items_tree:
                        if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT' and socket.name == socket_name:
                            try:
                                modifier[socket.identifier] = default_value
                                print(f"Set {socket_name} = {default_value}")
                            except Exception as e:
                                print(f"Warning: Could not set {socket_name}: {e}")
                            break
        
        print(f"✅ Установлены параметры по умолчанию для {effector_type} эффектора")
            
    except Exception as e:
        print(f"Warning: Could not set default parameters: {e}")


def check_object_has_cloners(obj):
    """
    Проверить, есть ли клонеры на объекте через UUID систему
    """
    has_cloner = False

    for mod in obj.modifiers:
        if mod.type == 'NODES' and mod.node_group:
            # ИСПРАВЛЕНО: Используем UUID систему для проверки клонеров

            # 1. Проверяем UUID метаданные (самый надежный способ)
            if mod.get("cloner_uuid") or mod.get("cloner_type"):
                has_cloner = True
                break

            # 2. Проверяем метаданные node_group
            if (mod.node_group.get("component_type") == 'CLONER' or
                mod.node_group.get("cloner_type")):
                has_cloner = True
                break

            # 3. Альтернативная проверка: используем функцию из dependency_safety
            try:
                from ..system.dependency_safety import is_cloner_modifier
                if is_cloner_modifier(mod):
                    has_cloner = True
                    break
            except ImportError:
                pass

    return has_cloner


# =============================================================================
# MAIN EFFECTOR CREATION FUNCTIONS
# =============================================================================

def create_effector_for_object(context, effector_type="RANDOM"):
    """
    Создать эффектор для активного объекта - главная функция
    """
    obj = context.active_object

    if not obj:
        return None, "No active object selected"

    # Проверяем наличие клонеров
    has_cloner = check_object_has_cloners(obj)
    warning_msg = None

    if not has_cloner:
        warning_msg = "This object has no cloners. Effectors only work with cloners."

    # Создаем эффектор через унифицированную систему реестра
    modifier, error = create_effector_modifier_via_registry(context, obj, effector_type)
    
    if error:
        return None, error
    
    if not has_cloner and modifier:
        # Предупреждение, но не ошибка
        return modifier, warning_msg
    
    return modifier, None


def create_effector_modifier_via_registry(context, obj, effector_type):
    """
    Универсальное создание модификатора эффектора через реестр

    Args:
        context: Blender context
        obj: Объект для добавления модификатора
        effector_type: Тип эффектора из реестра

    Returns:
        Tuple[modifier, error]: Модификатор и ошибка (если есть)
    """
    try:
        from ..registry.effector_registry import create_effector_via_registry, get_effector_info

        # Получаем информацию об эффекторе
        effector_info = get_effector_info(effector_type)
        if not effector_info:
            return None, f"Unknown effector type: {effector_type}"

        # Создаем node group через реестр
        effector_group = create_effector_via_registry(effector_type)
        if not effector_group:
            return None, f"Failed to create {effector_type} effector node group"

        # Добавляем модификатор
        display_name = effector_info.get('display_name', effector_type.title())
        modifier_name = f"{display_name.replace(' ', '')}"

        effector_modifier = obj.modifiers.new(name=modifier_name, type='NODES')
        effector_modifier.node_group = effector_group

        # Устанавливаем значения по умолчанию из класса эффектора
        _set_effector_default_values(effector_modifier, effector_type)

        # Позиционируем модификатор правильно (критически важно!)
        _position_effector_above_realize_instances(obj, effector_modifier)

        print(f"✅ [EFFECTOR_CREATION] Created {effector_type} effector modifier: {modifier_name}")
        return effector_modifier, None

    except Exception as e:
        print(f"❌ [EFFECTOR_CREATION] Error creating {effector_type} effector: {e}")
        import traceback
        traceback.print_exc()
        return None, str(e)


# =============================================================================
# EFFECTOR UTILITIES AND POSITIONING
# =============================================================================

def _set_effector_default_values(modifier, effector_type):
    """Установка значений по умолчанию для эффектора"""
    try:
        from ..registry.effector_registry import get_effector_class

        effector_class = get_effector_class(effector_type)
        if effector_class:
            effector_instance = effector_class()
            defaults = effector_instance.get_default_parameters()

            for param_name, default_value in defaults.items():
                # Преобразуем имена параметров в имена сокетов
                socket_name = param_name.replace("_", " ").title()
                if socket_name in modifier:
                    try:
                        modifier[socket_name] = default_value
                    except Exception as e:
                        print(f"Warning: Could not set {socket_name}: {e}")

    except Exception as e:
        print(f"Warning: Could not set default values for {effector_type}: {e}")


def _position_effector_above_realize_instances(obj, effector_modifier):
    """
    Размещает эффектор над модификаторами Realize Instances
    Если несколько Realize Instances - над самым нижним (последним в стеке)
    
    Args:
        obj: Объект с модификаторами
        effector_modifier: Модификатор эффектора для позиционирования
    """
    try:
        # Найдем все модификаторы Realize Instances
        realize_instances_indices = []
        
        for i, modifier in enumerate(obj.modifiers):
            if (modifier.type == 'NODES' and 
                modifier.get("modifier_type") == "REALIZE_INSTANCES"):
                realize_instances_indices.append(i)
        
        if not realize_instances_indices:
            print(f"[POSITIONING] No Realize Instances modifiers found on {obj.name}")
            return
        
        # Находим самый нижний (последний) Realize Instances модификатор
        lowest_realize_index = max(realize_instances_indices)
        
        # Находим индекс нашего эффектора
        effector_index = obj.modifiers.find(effector_modifier.name)
        
        if effector_index == -1:
            print(f"[POSITIONING] Effector modifier not found in stack")
            return
        
        print(f"[POSITIONING] Moving effector from position {effector_index} to above Realize Instances at {lowest_realize_index}")
        
        # Перемещаем эффектор над самым нижним Realize Instances
        # Нужно перемещать до тех пор, пока эффектор не окажется перед Realize Instances
        bpy.context.view_layer.objects.active = obj
        
        moves_count = 0
        max_moves = len(obj.modifiers)  # Защита от бесконечного цикла
        
        while effector_index > lowest_realize_index and moves_count < max_moves:
            try:
                bpy.ops.object.modifier_move_up(modifier=effector_modifier.name)
                effector_index = obj.modifiers.find(effector_modifier.name)
                moves_count += 1
                print(f"[POSITIONING] Moved effector up, new position: {effector_index}")
            except Exception as e:
                print(f"[POSITIONING] Error moving modifier up: {e}")
                break
        
        final_effector_index = obj.modifiers.find(effector_modifier.name)
        print(f"✅ [POSITIONING] Effector positioned at index {final_effector_index}, above Realize Instances at {lowest_realize_index}")
        
    except Exception as e:
        print(f"❌ [POSITIONING] Error positioning effector: {e}")
        import traceback
        traceback.print_exc()