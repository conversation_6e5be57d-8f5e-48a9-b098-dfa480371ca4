"""
Realize Instances Modifier Creation для ClonerPro
Создание отдельного модификатора для анти-рекурсии
"""

import bpy


def create_realize_instances_modifier(obj):
    """
    Создает отдельный модификатор Realize Instances на объекте
    
    Args:
        obj: Объект для добавления модификатора
    
    Returns:
        Созданный модификатор или None при ошибке
    """
    try:
        # Проверяем, что объект существует
        if not obj:
            print("[ERROR] No object provided for Realize Instances modifier")
            return None
        
        # Создаем уникальное имя модификатора
        base_name = "Realize Instances (Anti-Recursion)"
        modifier_name = base_name
        counter = 1
        
        while modifier_name in obj.modifiers:
            modifier_name = f"{base_name}.{counter:03d}"
            counter += 1
        
        # Создаем node group для Realize Instances
        node_group = create_realize_instances_node_group()
        if not node_group:
            print("[ERROR] Failed to create Realize Instances node group")
            return None
        
        # Создаем модификатор
        modifier = obj.modifiers.new(name=modifier_name, type='NODES')
        modifier.node_group = node_group
        
        # Добавляем метаданные
        modifier["architecture_type"] = "anti_recursion"
        modifier["modifier_type"] = "REALIZE_INSTANCES"
        
        print(f"✅ Created Realize Instances modifier: {modifier_name}")
        return modifier
        
    except Exception as e:
        print(f"❌ Error creating Realize Instances modifier: {e}")
        return None


def create_realize_instances_node_group():
    """
    Создает node group содержащую только Realize Instances
    
    Returns:
        Node group или None при ошибке
    """
    try:
        # Создаем уникальное имя node group
        base_name = "Realize Instances Anti-Recursion"
        node_group_name = base_name
        counter = 1
        
        while node_group_name in bpy.data.node_groups:
            node_group_name = f"{base_name}.{counter:03d}"
            counter += 1
        
        # Создаем node group
        node_group = bpy.data.node_groups.new(node_group_name, 'GeometryNodeTree')
        
        # Создаем интерфейс
        geometry_input = node_group.interface.new_socket(
            name="Geometry", 
            in_out='INPUT', 
            socket_type='NodeSocketGeometry'
        )
        
        geometry_output = node_group.interface.new_socket(
            name="Geometry", 
            in_out='OUTPUT', 
            socket_type='NodeSocketGeometry'
        )
        
        # Создаем узлы
        nodes = node_group.nodes
        links = node_group.links
        
        # Group Input
        group_input = nodes.new('NodeGroupInput')
        group_input.location = (0, 0)
        
        # Realize Instances
        realize_instances = nodes.new('GeometryNodeRealizeInstances')
        realize_instances.name = "Realize Instances"
        realize_instances.location = (200, 0)
        
        # Group Output
        group_output = nodes.new('NodeGroupOutput')
        group_output.location = (400, 0)
        
        # Связываем узлы
        links.new(group_input.outputs['Geometry'], realize_instances.inputs['Geometry'])
        links.new(realize_instances.outputs['Geometry'], group_output.inputs['Geometry'])
        
        # Добавляем метаданные
        node_group["component_type"] = "ANTI_RECURSION"
        node_group["modifier_type"] = "REALIZE_INSTANCES"
        
        print(f"✅ Created Realize Instances node group: {node_group_name}")
        return node_group
        
    except Exception as e:
        print(f"❌ Error creating Realize Instances node group: {e}")
        return None


def add_realize_instances_modifier_after_cloners(obj):
    """
    Добавляет модификатор Realize Instances после всех клонеров на объекте
    
    Args:
        obj: Объект для добавления модификатора
    
    Returns:
        Созданный модификатор или None
    """
    try:
        # Создаем модификатор
        modifier = create_realize_instances_modifier(obj)
        if not modifier:
            return None
        
        # Перемещаем модификатор в конец стека
        # (по умолчанию он уже создается в конце, но убеждаемся)
        while obj.modifiers.find(modifier.name) < len(obj.modifiers) - 1:
            bpy.context.view_layer.objects.active = obj
            bpy.ops.object.modifier_move_down(modifier=modifier.name)
        
        print(f"✅ Realize Instances modifier added after cloners")
        return modifier
        
    except Exception as e:
        print(f"❌ Error positioning Realize Instances modifier: {e}")
        return None


def remove_realize_instances_modifier(obj):
    """
    Удаляет модификатор Realize Instances с объекта
    
    Args:
        obj: Объект для удаления модификатора
    
    Returns:
        True если успешно удален, False иначе
    """
    try:
        modifiers_to_remove = []
        
        # Находим все модификаторы Realize Instances
        for modifier in obj.modifiers:
            if (modifier.type == 'NODES' and 
                modifier.get("modifier_type") == "REALIZE_INSTANCES"):
                modifiers_to_remove.append(modifier)
        
        # Удаляем найденные модификаторы
        for modifier in modifiers_to_remove:
            # Сохраняем ссылку на node group для удаления
            node_group = modifier.node_group
            modifier_name = modifier.name
            
            # Удаляем модификатор
            obj.modifiers.remove(modifier)
            
            # Удаляем node group если она больше не используется
            if node_group and node_group.users <= 0:
                bpy.data.node_groups.remove(node_group)
                print(f"✅ Removed unused node group: {node_group.name}")
            
            print(f"✅ Removed Realize Instances modifier: {modifier_name}")
        
        return len(modifiers_to_remove) > 0
        
    except Exception as e:
        print(f"❌ Error removing Realize Instances modifier: {e}")
        return False


def has_realize_instances_modifier(obj):
    """
    Проверяет, есть ли на объекте модификатор Realize Instances
    
    Args:
        obj: Объект для проверки
    
    Returns:
        True если есть, False иначе
    """
    if not obj:
        return False
    
    for modifier in obj.modifiers:
        if (modifier.type == 'NODES' and 
            modifier.get("modifier_type") == "REALIZE_INSTANCES"):
            return True
    
    return False