"""
Icon Management System for ClonerPro
Загрузка и управление кастомными иконками
"""

import bpy
import bpy.utils.previews
import os

# Глобальная переменная для хранения preview коллекции
preview_collections = {}

def get_icon_id(icon_name):
    """Получить ID кастомной иконки по имени"""
    pcoll = preview_collections.get("clonerpro_icons")
    if pcoll and icon_name in pcoll:
        return pcoll[icon_name].icon_id
    return 0

def load_icons():
    """Загрузить все кастомные иконки"""
    import bpy.utils.previews
    
    # Создаем preview коллекцию
    pcoll = bpy.utils.previews.new()
    
    # Путь к папке с иконками (от корня аддона)
    addon_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    icons_dir = os.path.join(addon_dir, "icons")
    
    # Список иконок для загрузки
    icons_to_load = {
        "cloner_green": "cloner_green.png",
        "effector_purple": "effector_purple.png", 
        "field_pink": "field_pink.png"
    }
    
    # Загружаем каждую иконку
    for icon_name, filename in icons_to_load.items():
        icon_path = os.path.join(icons_dir, filename)
        
        if os.path.exists(icon_path):
            pcoll.load(icon_name, icon_path, 'IMAGE')
            print(f"✅ Loaded icon: {icon_name} from {filename}")
        else:
            print(f"⚠️ Icon not found: {icon_path}")
    
    # Сохраняем коллекцию
    preview_collections["clonerpro_icons"] = pcoll

def unload_icons():
    """Выгрузить все кастомные иконки"""
    for pcoll in preview_collections.values():
        bpy.utils.previews.remove(pcoll)
    preview_collections.clear()
    print("🗑️ Unloaded all custom icons")

def register():
    """Регистрация системы иконок"""
    load_icons()

def unregister():
    """Отмена регистрации системы иконок"""
    unload_icons()
