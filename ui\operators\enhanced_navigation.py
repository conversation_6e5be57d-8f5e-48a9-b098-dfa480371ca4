"""
Улучшенные операторы навигации для ClonerPro
Современные паттерны UI/UX для профессионального workflow
"""

import bpy
from bpy.types import Operator
from bpy.props import StringProperty, EnumProperty


class CLONERPRO_OT_quick_help(Operator):
    """Быстрая справка с контекстными подсказками"""
    bl_idname = "clonerpro.quick_help"
    bl_label = "Quick Help"
    bl_description = "Show contextual help and tips"
    bl_options = {'REGISTER'}

    def execute(self, context):
        # Показываем контекстную справку в зависимости от активного таба
        active_tab = getattr(context.scene, "clonerpro_active_tab", "CREATE")
        
        if active_tab == "CREATE":
            help_text = """
🎯 CREATE MODE HELP:
• Select an object first
• Choose creation mode (Object/Stacked/Collection)
• Click cloner type to create
• Use Quick Actions for common setups
• Enable Anti-Recursion to prevent infinite loops
            """
        elif active_tab == "MANAGE":
            help_text = """
🔧 MANAGE MODE HELP:
• Browse all components in scene
• Filter by type (Cloners/Effectors/Fields)
• Click component to select and edit
• Use visibility toggle to hide/show
• Delete with X button
            """
        elif active_tab == "LIBRARY":
            help_text = """
📚 LIBRARY MODE HELP:
• Browse preset configurations
• Click preset to apply to active object
• Categories: Architectural, Organic, Abstract
• Save your own presets for reuse
            """
        else:
            help_text = "ClonerPro - Professional cloning tools for Blender"
        
        self.report({'INFO'}, help_text)
        return {'FINISHED'}


class CLONERPRO_OT_refresh_all(Operator):
    """Обновить все компоненты и UI"""
    bl_idname = "clonerpro.refresh_all"
    bl_label = "Refresh All"
    bl_description = "Refresh all components and update UI"
    bl_options = {'REGISTER'}

    def execute(self, context):
        # Обновляем все области UI
        for area in context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
        
        # Принудительно обновляем модификаторы
        if context.active_object:
            context.active_object.update_tag()
        
        self.report({'INFO'}, "Interface refreshed")
        return {'FINISHED'}


class CLONERPRO_OT_select_any_object(Operator):
    """Выбрать любой объект в сцене"""
    bl_idname = "clonerpro.select_any_object"
    bl_label = "Select Any Object"
    bl_description = "Select any mesh object in the scene"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        # Найти первый mesh объект в сцене
        for obj in context.scene.objects:
            if obj.type == 'MESH':
                # Очистить выделение
                bpy.ops.object.select_all(action='DESELECT')
                # Выбрать объект
                obj.select_set(True)
                context.view_layer.objects.active = obj
                self.report({'INFO'}, f"Selected: {obj.name}")
                return {'FINISHED'}
        
        self.report({'WARNING'}, "No mesh objects found in scene")
        return {'CANCELLED'}


class CLONERPRO_OT_quick_create_combo(Operator):
    """Быстрое создание популярных комбинаций"""
    bl_idname = "clonerpro.quick_create_combo"
    bl_label = "Quick Create Combo"
    bl_description = "Create popular cloner + effector combinations"
    bl_options = {'REGISTER', 'UNDO'}

    combo_type: StringProperty(
        name="Combo Type",
        description="Type of combination to create",
        default="GRID_RANDOM"
    )

    def execute(self, context):
        if not context.active_object:
            self.report({'ERROR'}, "No active object selected")
            return {'CANCELLED'}

        try:
            if self.combo_type == "GRID_RANDOM":
                # Создаем Grid клонер + Random эффектор
                self.create_grid_random_combo(context)
                self.report({'INFO'}, "Created Grid + Random combination")
                
            elif self.combo_type == "CIRCLE_NOISE":
                # Создаем Circle клонер + Noise эффектор
                self.create_circle_noise_combo(context)
                self.report({'INFO'}, "Created Circle + Noise combination")
                
            elif self.combo_type == "LINEAR_STEP":
                # Создаем Linear клонер + Step эффектор
                self.create_linear_step_combo(context)
                self.report({'INFO'}, "Created Linear + Step combination")
                
            else:
                self.report({'ERROR'}, f"Unknown combo type: {self.combo_type}")
                return {'CANCELLED'}

        except Exception as e:
            self.report({'ERROR'}, f"Failed to create combo: {str(e)}")
            return {'CANCELLED'}

        return {'FINISHED'}

    def create_grid_random_combo(self, context):
        """Создать Grid клонер с Random эффектором"""
        # Создаем Grid клонер
        bpy.ops.clonerpro.create_cloner(component_type="GRID")
        
        # Создаем Random эффектор
        bpy.ops.clonerpro.create_effector(component_type="RANDOM")

    def create_circle_noise_combo(self, context):
        """Создать Circle клонер с Noise эффектором"""
        # Создаем Circle клонер
        bpy.ops.clonerpro.create_cloner(component_type="CIRCLE")
        
        # Создаем Noise эффектор
        bpy.ops.clonerpro.create_effector(component_type="NOISE")

    def create_linear_step_combo(self, context):
        """Создать Linear клонер с Step эффектором"""
        # Создаем Linear клонер
        bpy.ops.clonerpro.create_cloner(component_type="LINEAR")
        
        # Создаем Step эффектор
        bpy.ops.clonerpro.create_effector(component_type="STEP")


class CLONERPRO_OT_show_presets(Operator):
    """Показать пресеты по категории"""
    bl_idname = "clonerpro.show_presets"
    bl_label = "Show Presets"
    bl_description = "Show presets for selected category"
    bl_options = {'REGISTER'}

    category: StringProperty(
        name="Category",
        description="Preset category to show",
        default="ARCHITECTURAL"
    )

    def execute(self, context):
        # Устанавливаем активную категорию
        context.scene.clonerpro_active_preset_category = self.category
        
        # Обновляем UI
        for area in context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
        
        self.report({'INFO'}, f"Showing {self.category.lower()} presets")
        return {'FINISHED'}


class CLONERPRO_OT_apply_preset(Operator):
    """Применить пресет к активному объекту"""
    bl_idname = "clonerpro.apply_preset"
    bl_label = "Apply Preset"
    bl_description = "Apply preset configuration to active object"
    bl_options = {'REGISTER', 'UNDO'}

    preset_name: StringProperty(
        name="Preset Name",
        description="Name of preset to apply",
        default=""
    )

    def execute(self, context):
        if not context.active_object:
            self.report({'ERROR'}, "No active object selected")
            return {'CANCELLED'}

        try:
            if self.preset_name == "building_array":
                self.apply_building_array_preset(context)
            elif self.preset_name == "spiral_tower":
                self.apply_spiral_tower_preset(context)
            elif self.preset_name == "particle_cloud":
                self.apply_particle_cloud_preset(context)
            elif self.preset_name == "fence_pattern":
                self.apply_fence_pattern_preset(context)
            else:
                self.report({'ERROR'}, f"Unknown preset: {self.preset_name}")
                return {'CANCELLED'}

            self.report({'INFO'}, f"Applied preset: {self.preset_name}")
            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Failed to apply preset: {str(e)}")
            return {'CANCELLED'}

    def apply_building_array_preset(self, context):
        """Пресет для массива зданий"""
        # Grid клонер с большим расстоянием
        bpy.ops.clonerpro.create_cloner(component_type="GRID")
        # TODO: Установить параметры Grid клонера

    def apply_spiral_tower_preset(self, context):
        """Пресет для спиральной башни"""
        # Spiral клонер с вертикальным смещением
        bpy.ops.clonerpro.create_cloner(component_type="SPIRAL")
        # TODO: Установить параметры Spiral клонера

    def apply_particle_cloud_preset(self, context):
        """Пресет для облака частиц"""
        # Grid клонер + Random эффектор с большой силой
        bpy.ops.clonerpro.create_cloner(component_type="GRID")
        bpy.ops.clonerpro.create_effector(component_type="RANDOM")
        # TODO: Установить параметры

    def apply_fence_pattern_preset(self, context):
        """Пресет для паттерна забора"""
        # Linear клонер с равномерным шагом
        bpy.ops.clonerpro.create_cloner(component_type="LINEAR")
        # TODO: Установить параметры Linear клонера


def register():
    """Регистрация операторов улучшенной навигации"""
    bpy.utils.register_class(CLONERPRO_OT_quick_help)
    bpy.utils.register_class(CLONERPRO_OT_refresh_all)
    bpy.utils.register_class(CLONERPRO_OT_select_any_object)
    bpy.utils.register_class(CLONERPRO_OT_quick_create_combo)
    bpy.utils.register_class(CLONERPRO_OT_show_presets)
    bpy.utils.register_class(CLONERPRO_OT_apply_preset)


def unregister():
    """Отмена регистрации операторов улучшенной навигации"""
    bpy.utils.unregister_class(CLONERPRO_OT_apply_preset)
    bpy.utils.unregister_class(CLONERPRO_OT_show_presets)
    bpy.utils.unregister_class(CLONERPRO_OT_quick_create_combo)
    bpy.utils.unregister_class(CLONERPRO_OT_select_any_object)
    bpy.utils.unregister_class(CLONERPRO_OT_refresh_all)
    bpy.utils.unregister_class(CLONERPRO_OT_quick_help)
