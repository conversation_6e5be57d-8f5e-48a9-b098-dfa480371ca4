"""
Операторы управления компонентами
"""

import bpy
from bpy.types import Operator
from bpy.props import StringProperty


class CLONERPRO_OT_delete_cloner(Operator):
    """Удалить клонер - ТОЧНАЯ КОПИЯ ИЗ ADVANCED_CLONERS"""
    bl_idname = "object.delete_cloner"
    bl_label = "Delete Cloner"
    bl_description = "Delete selected cloner"
    bl_options = {'REGISTER', 'UNDO'}
    
    modifier_name: StringProperty(
        name="Modifier Name",
        description="Name of modifier to delete",
        default=""
    )
    
    object_name: StringProperty(
        name="Object Name",
        description="Name of object containing the cloner (optional, uses active object if empty)",
        default=""
    )

    def execute(self, context):
        """
        Удаление клонера - ТОЧНАЯ КОПИЯ ЛОГИКИ ИЗ ADVANCED_CLONERS
        """
        print(f"[DEBUG] DELETE CLONER CALLED: modifier_name='{self.modifier_name}'")
        
        if not self.modifier_name:
            print(f"[ERROR] Missing modifier_name parameter")
            self.report({'ERROR'}, "Modifier name required")
            return {'CANCELLED'}
        
        # Используем указанный объект или активный объект
        if self.object_name:
            # Если указан конкретный объект (из браузера)
            if self.object_name not in bpy.data.objects:
                self.report({'ERROR'}, f"Object {self.object_name} not found")
                return {'CANCELLED'}
            obj = bpy.data.objects[self.object_name]
        else:
            # Используем активный объект (из списка клонеров активного объекта)
            obj = context.active_object
            if not obj:
                self.report({'ERROR'}, "No active object")
                return {'CANCELLED'}
        
        # Найти модификатор
        if self.modifier_name not in obj.modifiers:
            self.report({'ERROR'}, f"Modifier {self.modifier_name} not found on {obj.name}")
            return {'CANCELLED'}
        
        modifier = obj.modifiers[self.modifier_name]
        
        # Выполнить удаление с восстановлением цепочек
        from ...core.chain.deletion import delete_cloner_with_chain_recovery
        
        # Используем новую систему удаления с автоматическим восстановлением цепи
        success = delete_cloner_with_chain_recovery(context, obj, self.modifier_name)
        
        if success:
            self.report({'INFO'}, f"Deleted cloner: {self.modifier_name}")
            return {'FINISHED'}
        else:
            self.report({'ERROR'}, f"Failed to delete cloner: {self.modifier_name}")
            return {'CANCELLED'}
    
    def delete_cloner_by_mode(self, context, obj, modifier):
        """
        Удаление клонера в зависимости от режима - ТОЧНАЯ ЛОГИКА ИЗ ADVANCED_CLONERS
        """
        # Определить режим клонера
        cloner_mode = modifier.get("cloner_mode", "OBJECT")
        is_stacked = modifier.get("is_stacked_cloner", False)
        source_type = modifier.get("source_type", "OBJECT")
        
        print(f"[DEBUG] Deleting cloner: mode={cloner_mode}, stacked={is_stacked}, source={source_type}")
        print(f"[DEBUG] Object: {obj.name}, Modifier: {modifier.name}")
        print(f"[DEBUG] All modifier keys: {list(modifier.keys())}")
        
        # Проверяем метаданные клонера
        cloner_mode_debug = modifier.get("cloner_mode", "NOT_SET")
        is_stacked_debug = modifier.get("is_stacked_cloner", "NOT_SET")
        source_type_debug = modifier.get("source_type", "NOT_SET")
        
        try:
            if is_stacked or cloner_mode == "STACKED":
                # STACKED MODE: Удалить только модификатор
                print(f"[DEBUG] Using STACKED deletion logic")
                return self.delete_stacked_cloner(context, obj, modifier)
            elif source_type == "COLLECTION" or cloner_mode == "COLLECTION":
                # COLLECTION MODE: Удалить объект + коллекции + восстановить оригинал
                print(f"[DEBUG] Using COLLECTION deletion logic")
                return self.delete_collection_cloner(context, obj, modifier)
            else:
                # OBJECT MODE: Удалить объект + коллекции + восстановить оригинал
                print(f"[DEBUG] Using OBJECT deletion logic")
                return self.delete_object_cloner(context, obj, modifier)
                
        except Exception as e:
            print(f"[ERROR] Error deleting cloner: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def delete_stacked_cloner(self, context, obj, modifier):
        """
        Удалить стековый клонер - ТОЛЬКО МОДИФИКАТОР (как в advanced_cloners)
        """
        print(f"Deleting stacked cloner modifier {modifier.name} from {obj.name}")
        
        # Удалить node group если больше не используется
        node_group = modifier.node_group
        
        # Удалить модификатор
        obj.modifiers.remove(modifier)
        
        # Удалить node group если он не используется
        if node_group and node_group.users == 1:
            print(f"Removing unused node group: {node_group.name}")
            bpy.data.node_groups.remove(node_group)
        
        # Обновить view layer
        context.view_layer.update()
        
        return True
    
    def delete_object_cloner(self, context, obj, modifier):
        """
        Удалить объектный клонер - ТОЧНАЯ КОПИЯ ЛОГИКИ ИЗ ADVANCED_CLONERS
        """
        print(f"Deleting object cloner {obj.name} with modifier {modifier.name}")
        
        # Получить метаданные
        original_obj_name = modifier.get("original_object", "")
        cloner_collection_name = modifier.get("cloner_collection", "")
        node_group = modifier.node_group
        
        # 1. Восстановить видимость оригинального объекта
        original_obj = None
        if original_obj_name and original_obj_name in bpy.data.objects:
            original_obj = bpy.data.objects[original_obj_name]
            original_obj.hide_viewport = False
            print(f"Restored visibility of original object: {original_obj_name}")
        
        # 2. Сохранить mesh data перед удалением
        mesh_data = obj.data if obj.data else None
        obj_name = obj.name  # Сохранить имя для отладки
        
        # 3. Удалить коллекцию клонера БЕЗ объекта клонера (как в advanced_cloners)
        if cloner_collection_name and cloner_collection_name in bpy.data.collections:
            cloner_collection = bpy.data.collections[cloner_collection_name]
            # Создаем копию списка объектов для безопасного удаления
            objects_to_remove = list(cloner_collection.objects)
            for collection_obj in objects_to_remove:
                if collection_obj != obj:  # НЕ удаляем сам объект клонера здесь
                    try:
                        obj_mesh = collection_obj.data if collection_obj.data else None
                        bpy.data.objects.remove(collection_obj)
                        if obj_mesh and obj_mesh.users == 0:
                            if obj_mesh in bpy.data.meshes:
                                bpy.data.meshes.remove(obj_mesh)
                            elif obj_mesh in bpy.data.curves:
                                bpy.data.curves.remove(obj_mesh)
                    except Exception as e:
                        print(f"Error removing collection object {collection_obj.name}: {e}")
            
            # Удалить саму коллекцию
            try:
                bpy.data.collections.remove(cloner_collection)
                print(f"Removed cloner collection: {cloner_collection_name}")
            except Exception as e:
                print(f"Error removing collection: {e}")
        
        # 4. Удалить объект клонера
        try:
            bpy.data.objects.remove(obj)
            print(f"Removed cloner object: {obj_name}")
        except Exception as e:
            print(f"Error removing cloner object: {e}")
        
        # 5. Удалить mesh data если не используется
        if mesh_data and mesh_data.users == 0:
            try:
                if mesh_data in bpy.data.meshes:
                    bpy.data.meshes.remove(mesh_data)
                elif mesh_data in bpy.data.curves:
                    bpy.data.curves.remove(mesh_data)
            except Exception as e:
                print(f"Error removing mesh data: {e}")
        
        # 6. Удалить node group если не используется
        if node_group and node_group.users == 1:
            try:
                print(f"Removing unused node group: {node_group.name}")
                bpy.data.node_groups.remove(node_group)
            except Exception as e:
                print(f"Error removing node group: {e}")
        
        # 7. Сделать оригинальный объект активным (если существует)
        if original_obj:
            try:
                for obj_sel in context.selected_objects:
                    obj_sel.select_set(False)
                original_obj.select_set(True)
                context.view_layer.objects.active = original_obj
                print(f"Made original object {original_obj.name} active")
            except Exception as e:
                print(f"Error setting active object: {e}")
        
        # 8. Обновить view layer
        context.view_layer.update()
        
        return True
    
    def delete_collection_cloner(self, context, obj, modifier):
        """
        Удалить коллекционный клонер - ТОЧНАЯ КОПИЯ ЛОГИКИ ИЗ ADVANCED_CLONERS
        """
        print(f"Deleting collection cloner {obj.name} with modifier {modifier.name}")
        
        # Получить метаданные
        original_collection_name = modifier.get("original_collection", "")
        cloner_collection_name = modifier.get("cloner_collection", "")
        node_group = modifier.node_group
        
        # 1. Восстановить видимость оригинальной коллекции
        if original_collection_name and original_collection_name in bpy.data.collections:
            original_collection = bpy.data.collections[original_collection_name]
            # В Blender коллекции не имеют hide_viewport, используем layer collection
            self.restore_collection_visibility(context, original_collection)
            print(f"Restored visibility of original collection: {original_collection_name}")
        
        # 2. Сохранить mesh data перед удалением
        mesh_data = obj.data if obj.data else None
        obj_name = obj.name  # Сохранить имя для отладки
        
        # 3. Удалить коллекцию клонера БЕЗ объекта клонера (как в advanced_cloners)
        if cloner_collection_name and cloner_collection_name in bpy.data.collections:
            cloner_collection = bpy.data.collections[cloner_collection_name]
            # Создаем копию списка объектов для безопасного удаления
            objects_to_remove = list(cloner_collection.objects)
            for collection_obj in objects_to_remove:
                if collection_obj != obj:  # НЕ удаляем сам объект клонера здесь
                    try:
                        obj_mesh = collection_obj.data if collection_obj.data else None
                        bpy.data.objects.remove(collection_obj)
                        if obj_mesh and obj_mesh.users == 0:
                            if obj_mesh in bpy.data.meshes:
                                bpy.data.meshes.remove(obj_mesh)
                            elif obj_mesh in bpy.data.curves:
                                bpy.data.curves.remove(obj_mesh)
                    except Exception as e:
                        print(f"Error removing collection object {collection_obj.name}: {e}")
            
            # Удалить саму коллекцию
            try:
                bpy.data.collections.remove(cloner_collection)
                print(f"Removed cloner collection: {cloner_collection_name}")
            except Exception as e:
                print(f"Error removing collection: {e}")
        
        # 4. Удалить объект клонера
        try:
            bpy.data.objects.remove(obj)
            print(f"Removed cloner object: {obj_name}")
        except Exception as e:
            print(f"Error removing cloner object: {e}")
        
        # 5. Удалить mesh data если не используется
        if mesh_data and mesh_data.users == 0:
            try:
                if mesh_data in bpy.data.meshes:
                    bpy.data.meshes.remove(mesh_data)
                elif mesh_data in bpy.data.curves:
                    bpy.data.curves.remove(mesh_data)
            except Exception as e:
                print(f"Error removing mesh data: {e}")
        
        # 6. Удалить node group если не используется
        if node_group and node_group.users == 1:
            try:
                print(f"Removing unused node group: {node_group.name}")
                bpy.data.node_groups.remove(node_group)
            except Exception as e:
                print(f"Error removing node group: {e}")
        
        # 7. Обновить view layer
        context.view_layer.update()
        
        return True
    
    def remove_collection_and_objects(self, collection):
        """
        Удалить коллекцию и все объекты в ней - КАК В ADVANCED_CLONERS
        """
        # Рекурсивно удалить дочерние коллекции
        for child in list(collection.children):
            self.remove_collection_and_objects(child)
        
        # Удалить все объекты в коллекции
        for obj in list(collection.objects):
            # Удалить mesh data если есть
            mesh_data = obj.data if obj.type == 'MESH' else None
            
            # Удалить объект
            bpy.data.objects.remove(obj)
            
            # Удалить mesh data если не используется
            if mesh_data and mesh_data.users == 0:
                bpy.data.meshes.remove(mesh_data)
        
        # Отвязать от родительских коллекций
        for parent in collection.users_dupli_group:
            if collection in parent.children:
                parent.children.unlink(collection)
        
        # Удалить коллекцию
        bpy.data.collections.remove(collection)
    
    def restore_collection_visibility(self, context, collection):
        """
        Восстановить видимость коллекции через layer collection
        """
        def find_layer_collection(layer_collection, target_collection):
            if layer_collection.collection == target_collection:
                return layer_collection
            for child in layer_collection.children:
                result = find_layer_collection(child, target_collection)
                if result:
                    return result
            return None
        
        layer_collection = find_layer_collection(context.view_layer.layer_collection, collection)
        if layer_collection:
            layer_collection.hide_viewport = False


class CLONERPRO_OT_toggle_component_visibility(Operator):
    """Переключить видимость компонента"""
    bl_idname = "clonerpro.toggle_component_visibility"
    bl_label = "Toggle Component Visibility"
    bl_description = "Toggle component visibility"
    bl_options = {'REGISTER', 'UNDO'}
    
    component_name: StringProperty(
        name="Component Name",
        description="Name of component",
        default=""
    )

    def execute(self, context):
        # TODO: Реализовать переключение видимости
        self.report({'INFO'}, f"Toggled visibility: {self.component_name}")
        return {'FINISHED'}


class CLONERPRO_OT_select_component(Operator):
    """Выбрать компонент"""
    bl_idname = "clonerpro.select_component"
    bl_label = "Select Component"
    bl_description = "Select component in scene"
    bl_options = {'REGISTER', 'UNDO'}
    
    component_name: StringProperty(
        name="Component Name",
        description="Name of component to select",
        default=""
    )

    def execute(self, context):
        # TODO: Реализовать выбор компонента
        self.report({'INFO'}, f"Selected component: {self.component_name}")
        return {'FINISHED'}


def register():
    """Регистрация операторов управления"""
    bpy.utils.register_class(CLONERPRO_OT_delete_cloner)
    bpy.utils.register_class(CLONERPRO_OT_toggle_component_visibility)
    bpy.utils.register_class(CLONERPRO_OT_select_component)


def unregister():
    """Отмена регистрации операторов управления"""
    try:
        bpy.utils.unregister_class(CLONERPRO_OT_select_component)
    except:
        pass
    try:
        bpy.utils.unregister_class(CLONERPRO_OT_toggle_component_visibility)
    except:
        pass
    try:
        bpy.utils.unregister_class(CLONERPRO_OT_delete_cloner)
    except:
        pass