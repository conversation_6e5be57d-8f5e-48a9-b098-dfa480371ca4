"""
Tabbed Cloners Panel для ClonerPro
Улучшенная организация UI с табами для Create/Manage
"""

import bpy
from bpy.types import Panel
from ...core.system.settings import get_settings


class CLONERS_PT_Main_Tabbed(Panel):
    """Главная панель клонеров с табами для лучшей организации"""
    bl_label = "ClonerPro"
    bl_idname = "CLONERS_PT_Main_Tabbed"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'ClonerPro'
    bl_order = 0

    def draw(self, context):
        layout = self.layout
        
        # === СИСТЕМА ТАБОВ ===
        self.draw_tab_header(layout, context)
        
        # === СОДЕРЖИМОЕ В ЗАВИСИМОСТИ ОТ АКТИВНОГО ТАБА ===
        active_tab = getattr(context.scene, "clonerpro_active_tab", "CREATE")

        if active_tab == "CREATE":
            self.draw_create_tab_content(layout, context)
        elif active_tab == "MANAGE":
            self.draw_manage_tab_content(layout, context)
        elif active_tab == "LIBRARY":
            self.draw_library_tab_content(layout, context)

    def draw_tab_header(self, layout, context):
        """Улучшенная отрисовка заголовка с современным дизайном"""
        # Главный контейнер с градиентом
        main_box = layout.box()
        main_box.use_property_split = False

        # === БРЕНДИНГ И СТАТУС ===
        brand_row = main_box.row(align=True)
        brand_row.scale_y = 1.2

        # Логотип с кастомной иконкой
        from ...ui.icons import get_icon_id
        cloner_icon = get_icon_id("cloner_green")
        brand_row.label(text="ClonerPro", icon_value=cloner_icon)

        # Статус активного объекта
        if context.active_object:
            # Показываем количество компонентов на активном объекте
            from ...core.services.cloner_scanner import get_active_object_cloners, get_active_object_effectors
            cloners = get_active_object_cloners(context)
            effectors = get_active_object_effectors(context)
            total = len(cloners) + len(effectors)

            if total > 0:
                status_text = f"Active: {context.active_object.name} ({total})"
                brand_row.label(text=status_text, icon="OBJECT_DATA")
            else:
                brand_row.label(text=f"Active: {context.active_object.name}", icon="OBJECT_DATAMODE")
        else:
            brand_row.label(text="No Active Object", icon="QUESTION")

        # Быстрые действия справа
        quick_actions = brand_row.row(align=True)
        quick_actions.scale_x = 0.8
        quick_actions.operator("clonerpro.quick_help", text="", icon="QUESTION", emboss=False)
        quick_actions.operator("clonerpro.refresh_all", text="", icon="FILE_REFRESH", emboss=False)

        # === НАВИГАЦИОННЫЕ ТАБЫ ===
        tab_container = main_box.box()
        tab_row = tab_container.row(align=True)
        tab_row.scale_y = 1.1

        active_tab = getattr(context.scene, "clonerpro_active_tab", "CREATE")

        # Tab: Create (с улучшенными иконками)
        create_op = tab_row.operator("clonerpro.switch_tab",
                                   text="Create",
                                   icon="ADD",
                                   depress=(active_tab == "CREATE"))
        create_op.tab_name = "CREATE"

        # Tab: Manage
        manage_op = tab_row.operator("clonerpro.switch_tab",
                                   text="Manage",
                                   icon="MODIFIER",
                                   depress=(active_tab == "MANAGE"))
        manage_op.tab_name = "MANAGE"

        # Tab: Library (новый таб для пресетов)
        library_op = tab_row.operator("clonerpro.switch_tab",
                                    text="Library",
                                    icon="ASSET_MANAGER",
                                    depress=(active_tab == "LIBRARY"))
        library_op.tab_name = "LIBRARY"

        # === КОНТЕКСТНАЯ ИНФОРМАЦИЯ ===
        if active_tab == "CREATE":
            self.draw_create_context_info(main_box, context)
        elif active_tab == "MANAGE":
            self.draw_manage_context_info(main_box, context)
        elif active_tab == "LIBRARY":
            self.draw_library_context_info(main_box, context)

    def draw_create_context_info(self, layout, context):
        """Контекстная информация для таба Create"""
        info_row = layout.row()
        info_row.scale_y = 0.8

        if not context.active_object:
            info_row.label(text="💡 Select an object to create cloners", icon="INFO")
        else:
            settings = get_settings()
            mode_text = {
                'OBJECT': "New cloner object",
                'STACKED': "Modifier on current object",
                'COLLECTION': "Clone entire collection"
            }.get(settings.creation_mode, "Unknown mode")
            info_row.label(text=f"Mode: {mode_text}", icon="SETTINGS")

    def draw_manage_context_info(self, layout, context):
        """Контекстная информация для таба Manage"""
        info_row = layout.row()
        info_row.scale_y = 0.8

        if context.active_object:
            from ...core.services.cloner_scanner import scan_scene_cloners, scan_scene_effectors
            scene_cloners = scan_scene_cloners(context)
            scene_effectors = scan_scene_effectors(context)
            total = len(scene_cloners) + len(scene_effectors)
            info_row.label(text=f"Scene: {total} components total", icon="WORLD")
        else:
            info_row.label(text="💡 Select an object to manage its components", icon="INFO")

    def draw_library_context_info(self, layout, context):
        """Контекстная информация для таба Library"""
        info_row = layout.row()
        info_row.scale_y = 0.8
        info_row.label(text="📚 Presets and templates for quick setup", icon="ASSET_MANAGER")

    def draw_create_tab_content(self, layout, context):
        """Улучшенное содержимое таба Create с быстрыми действиями"""
        settings = get_settings()

        # === БЫСТРЫЕ ДЕЙСТВИЯ ===
        self.draw_quick_create_actions(layout, context, settings)

        # === CLONERS CREATION SECTION ===
        self.draw_cloners_creation_section(layout, settings, context)

        # === EFFECTORS CREATION SECTION ===
        self.draw_effectors_creation_section(layout, context)

        # === FIELDS CREATION SECTION ===
        self.draw_fields_creation_section(layout, context)

    def draw_quick_create_actions(self, layout, context, settings):
        """Панель быстрых действий для создания"""
        if not context.active_object:
            # Показываем подсказку если нет активного объекта
            help_box = layout.box()
            help_row = help_box.row()
            help_row.label(text="⚠️ Select an object first", icon="ERROR")
            help_row.operator("clonerpro.select_any_object", text="Select Any", icon="RESTRICT_SELECT_OFF")
            return

        # Быстрые кнопки для популярных комбинаций
        quick_box = layout.box()
        quick_header = quick_box.row()
        quick_header.label(text="⚡ Quick Actions", icon="LIGHTNING")

        # Ряд быстрых действий
        quick_row = quick_box.row(align=True)
        quick_row.scale_y = 1.3

        # Быстрое создание Grid + Random
        grid_random = quick_row.operator("clonerpro.quick_create_combo", text="Grid + Random", icon="MESH_GRID")
        grid_random.combo_type = "GRID_RANDOM"

        # Быстрое создание Circle + Noise
        circle_noise = quick_row.operator("clonerpro.quick_create_combo", text="Circle + Noise", icon="MESH_CIRCLE")
        circle_noise.combo_type = "CIRCLE_NOISE"

        # Быстрое создание Linear + Step
        linear_step = quick_row.operator("clonerpro.quick_create_combo", text="Linear + Step", icon="IPO_LINEAR")
        linear_step.combo_type = "LINEAR_STEP"

    def draw_manage_tab_content(self, layout, context):
        """Улучшенное содержимое таба Manage с фильтрацией"""

        # === ФИЛЬТРЫ И ПОИСК ===
        self.draw_management_filters(layout, context)

        # === BROWSER SECTION ===
        self.draw_browser_section(layout, context)

        # === ACTIVE OBJECT SETTINGS ===
        self.draw_active_settings_section(layout, context)

    def draw_library_tab_content(self, layout, context):
        """Новый таб Library с пресетами"""

        # === КАТЕГОРИИ ПРЕСЕТОВ ===
        categories_box = layout.box()
        categories_row = categories_box.row(align=True)
        categories_row.label(text="📁 Categories", icon="FILE_FOLDER")

        # Кнопки категорий
        cat_row = categories_box.row(align=True)
        cat_row.operator("clonerpro.show_presets", text="Architectural", icon="HOME").category = "ARCHITECTURAL"
        cat_row.operator("clonerpro.show_presets", text="Organic", icon="OUTLINER_OB_MESH").category = "ORGANIC"
        cat_row.operator("clonerpro.show_presets", text="Abstract", icon="MESH_ICOSPHERE").category = "ABSTRACT"

        # === ПОПУЛЯРНЫЕ ПРЕСЕТЫ ===
        presets_box = layout.box()
        presets_row = presets_box.row()
        presets_row.label(text="⭐ Popular Presets", icon="SOLO_ON")

        # Сетка пресетов
        preset_grid = presets_box.grid_flow(columns=2, align=True)
        preset_grid.scale_y = 1.2

        preset_grid.operator("clonerpro.apply_preset", text="Building Array", icon="HOME").preset_name = "building_array"
        preset_grid.operator("clonerpro.apply_preset", text="Spiral Tower", icon="CURVE_BEZCIRCLE").preset_name = "spiral_tower"
        preset_grid.operator("clonerpro.apply_preset", text="Particle Cloud", icon="PARTICLES").preset_name = "particle_cloud"
        preset_grid.operator("clonerpro.apply_preset", text="Fence Pattern", icon="MESH_PLANE").preset_name = "fence_pattern"

    def draw_management_filters(self, layout, context):
        """Фильтры для управления компонентами"""
        filter_box = layout.box()
        filter_row = filter_box.row(align=True)
        filter_row.label(text="🔍 Filters", icon="VIEWZOOM")

        # Фильтры по типу
        type_row = filter_box.row(align=True)
        type_row.prop(context.scene, "clonerpro_filter_cloners", text="Cloners", toggle=True, icon="MESH_CUBE")
        type_row.prop(context.scene, "clonerpro_filter_effectors", text="Effectors", toggle=True, icon="FORCE_VORTEX")
        type_row.prop(context.scene, "clonerpro_filter_fields", text="Fields", toggle=True, icon="LIGHT_SUN")

        # Поиск по имени
        search_row = filter_box.row()
        search_row.prop(context.scene, "clonerpro_search_text", text="", icon="VIEWZOOM", placeholder="Search components...")

    def draw_cloners_creation_section(self, layout, settings, context):
        """Секция создания клонеров"""
        # Проверяем состояние раскрытия
        creation_expanded = getattr(context.scene, "clonerpro_cloners_creation_expanded", True)
        
        # === ЗАГОЛОВОК СЕКЦИИ С КОЛЛАПСОМ ===
        header_box = layout.box()
        header_row = header_box.row(align=True)
        
        # Треугольник expand/collapse
        expand_icon = "TRIA_DOWN" if creation_expanded else "TRIA_RIGHT"
        expand_op = header_row.operator("clonerpro.toggle_section_expanded", 
                                       text="", icon=expand_icon, emboss=False)
        expand_op.section_name = "cloners_creation"
        
        # Заголовок секции с зеленоватой иконкой
        # Используем иконку куба для клонеров и активное состояние
        cloners_row = header_row.row()
        cloners_row.active = True  # Активное состояние для визуального различия
        cloners_row.label(text="Cloners", icon='MESH_CUBE')
        
        # Счетчик созданных клонеров
        from ...core.services.cloner_scanner import scan_scene_cloners
        all_cloners = scan_scene_cloners(context)
        if all_cloners:
            header_row.label(text=f"({len(all_cloners)})", icon='BLANK1')
        
        # === СОДЕРЖИМОЕ СЕКЦИИ (только если развернуто) ===
        if not creation_expanded:
            return
            
        content_box = header_box.box()
        
        # Настройки создания клонеров
        self.draw_cloners_creation_settings(content_box, settings, context)
        
        # Кнопки создания клонеров
        self.draw_cloner_creation_buttons(content_box, settings, context)

    def draw_cloners_creation_settings(self, layout, settings, context):
        """Настройки создания клонеров"""
        # Режим клонирования
        mode_row = layout.row(align=True)
        mode_row.label(text="Mode:")
        
        obj_op = mode_row.operator("clonerpro.set_creation_mode", 
                                 text="Object", 
                                 depress=(settings.creation_mode == 'OBJECT'))
        obj_op.mode = 'OBJECT'
        
        coll_op = mode_row.operator("clonerpro.set_creation_mode",
                                  text="Collection", 
                                  depress=(settings.creation_mode == 'COLLECTION'))
        coll_op.mode = 'COLLECTION'
        
        # Anti-Recursion под режимами (а не справа)
        anti_row = layout.row()
        anti_row.scale_y = 0.9
        anti_row.prop(context.scene, "use_anti_recursion", 
                     text="Anti-Recursion", toggle=True, icon='FILE_REFRESH')
        
        # Collection selector перенесен в draw_cloner_creation_buttons
        
        # Дополнительные опции убраны - Anti-Recursion перенесен наверх

    def draw_cloner_creation_buttons(self, layout, settings, context):
        """Кнопки создания клонеров"""
        
        # === UNIFIED CLONERS ===
        layout.separator()
        unified_label = layout.row()
        unified_label.label(text="Base Cloners:", icon='MESH_DATA')
        
        # Base Stack переключатель под заголовком (только для Object режима)
        if settings.creation_mode == 'OBJECT':
            stack_row = layout.row()
            stack_row.scale_y = 0.9
            stack_row.alignment = 'RIGHT'  # Меняем с LEFT на RIGHT
            
            # Используем split, но теперь кнопка справа
            split = stack_row.split(factor=0.5)  # 50% ширины
            
            # Левая часть остается пустой
            left_col = split.column()
            left_col.label(text="")
            
            # Правая часть - Stack кнопка справа
            right_col = split.column()
            right_col.scale_x = 0.8
            right_col.prop(context.scene, "use_stacked_modifiers", 
                          text="Stack", toggle=True, icon='MODIFIER')
        
        # Collection selector для Collection режима (перенесен сюда)
        if settings.creation_mode == 'COLLECTION':
            coll_row = layout.row()
            coll_row.prop_search(settings, "collection_name", bpy.data, "collections", text="")
        
        # 2x2 сетка unified клонеров с кастомными зелеными иконками
        unified_box = layout.box()

        row1 = unified_box.row(align=True)
        row1.scale_y = 1.1

        # Получаем кастомные иконки
        from ...ui.icons import get_icon_id
        cloner_icon = get_icon_id("cloner_green")

        grid_op = row1.operator("clonerpro.create_cloner", text="Grid", icon_value=cloner_icon)
        grid_op.component_type = "GRID"

        linear_op = row1.operator("clonerpro.create_cloner", text="Linear", icon_value=cloner_icon)
        linear_op.component_type = "LINEAR"

        row2 = unified_box.row(align=True)
        row2.scale_y = 1.1

        circle_op = row2.operator("clonerpro.create_cloner", text="Circle", icon_value=cloner_icon)
        circle_op.component_type = "CIRCLE"

        spiral_op = row2.operator("clonerpro.create_cloner", text="Spiral", icon_value=cloner_icon)
        spiral_op.component_type = "SPIRAL"
        
        # === MESH CLONERS ===
        layout.separator()
        mesh_label = layout.row()
        mesh_label.label(text="Mesh Cloners:", icon='OBJECT_DATA')
        
        # Mesh Stack переключатель под заголовком (для Object и Collection режимов)
        if settings.creation_mode in ['OBJECT', 'COLLECTION']:
            mesh_stack_row = layout.row()
            mesh_stack_row.scale_y = 0.9
            mesh_stack_row.alignment = 'RIGHT'  # Меняем с LEFT на RIGHT
            
            # Используем split, но теперь кнопка справа
            mesh_split = mesh_stack_row.split(factor=0.5)  # 50% ширины
            
            # Левая часть остается пустой
            mesh_left_col = mesh_split.column()
            mesh_left_col.label(text="")
            
            # Правая часть - Stack кнопка справа
            mesh_right_col = mesh_split.column()
            mesh_right_col.scale_x = 0.8
            mesh_right_col.prop(context.scene, "use_mesh_stacked_modifiers", 
                               text="Stack", toggle=True, icon='MODIFIER')
        
        # 2x1 сетка mesh клонеров с кастомными зелеными иконками
        mesh_box = layout.box()

        row1 = mesh_box.row(align=True)
        row1.scale_y = 1.1

        # Используем те же зеленые иконки для mesh клонеров
        obj_op = row1.operator("clonerpro.create_cloner", text="Object", icon_value=cloner_icon)
        obj_op.component_type = "OBJECT"

        spline_op = row1.operator("clonerpro.create_cloner", text="Spline", icon_value=cloner_icon)
        spline_op.component_type = "SPLINE"

        row2 = mesh_box.row(align=True)
        row2.scale_y = 1.1

        vol_op = row2.operator("clonerpro.create_cloner", text="Volume", icon_value=cloner_icon)
        vol_op.component_type = "VOLUME"

        row2.label(text="")  # Заполнить пустое место

    def draw_effectors_creation_section(self, layout, context):
        """Секция создания эффекторов"""
        # Проверяем состояние раскрытия (по умолчанию развернуто)
        creation_expanded = getattr(context.scene, "clonerpro_effectors_creation_expanded", True)
        
        # === ЗАГОЛОВОК СЕКЦИИ С КОЛЛАПСОМ ===
        header_box = layout.box()
        header_row = header_box.row(align=True)
        
        # Треугольник expand/collapse
        expand_icon = "TRIA_DOWN" if creation_expanded else "TRIA_RIGHT"
        expand_op = header_row.operator("clonerpro.toggle_section_expanded", 
                                       text="", icon=expand_icon, emboss=False)
        expand_op.section_name = "effectors_creation"
        
        # Заголовок секции с фиолетовой иконкой
        # Используем иконку силы для эффекторов и обычное состояние
        effectors_row = header_row.row()
        effectors_row.scale_x = 1.1  # Немного увеличиваем для визуального различия
        effectors_row.label(text="Effectors", icon='FORCE_VORTEX')  # Меняем иконку на более фиолетовую
        
        # Счетчик созданных эффекторов
        from ...core.services.cloner_scanner import scan_scene_effectors
        all_effectors = scan_scene_effectors(context)
        if all_effectors:
            header_row.label(text=f"({len(all_effectors)})", icon='BLANK1')
        
        # === СОДЕРЖИМОЕ СЕКЦИИ (только если развернуто) ===
        if not creation_expanded:
            return
            
        content_box = header_box.box()

        # 2x3 сетка кнопок создания эффекторов
        self.draw_effector_creation_grid(content_box)

    def draw_effector_creation_grid(self, layout):
        """Динамическая сетка кнопок создания эффекторов из реестра"""
        from ...core.registry.effector_registry import list_effector_types, get_effector_info
        from ...ui.icons import get_icon_id

        # Кастомные фиолетовые иконки для эффекторов
        effector_icon = get_icon_id("effector_purple")

        # Получаем все доступные эффекторы из реестра
        effector_types = list_effector_types()

        if not effector_types:
            layout.label(text="No effectors registered", icon='ERROR')
            return

        # Создаем сетку 2 колонки
        cols_per_row = 2
        current_row = None

        for i, effector_type in enumerate(effector_types):
            # Создаем новый ряд каждые 2 элемента
            if i % cols_per_row == 0:
                current_row = layout.row(align=True)
                current_row.scale_y = 1.2

            # Получаем информацию об эффекторе
            effector_info = get_effector_info(effector_type)
            display_name = effector_info.get('display_name', effector_type.title())

            # Создаем кнопку
            op = current_row.operator("clonerpro.create_effector", text=display_name, icon_value=effector_icon)
            op.component_type = effector_type

        # Заполняем пустые места в последнем ряду
        remaining_slots = cols_per_row - (len(effector_types) % cols_per_row)
        if remaining_slots < cols_per_row and current_row:
            for _ in range(remaining_slots):
                current_row.label(text="")

    def draw_fields_creation_section(self, layout, context):
        """Секция создания полей"""
        # Проверяем состояние раскрытия (по умолчанию развернуто)
        creation_expanded = getattr(context.scene, "clonerpro_fields_creation_expanded", True)
        
        # === ЗАГОЛОВОК СЕКЦИИ С КОЛЛАПСОМ ===
        header_box = layout.box()
        header_row = header_box.row(align=True)
        
        # Треугольник expand/collapse
        expand_icon = "TRIA_DOWN" if creation_expanded else "TRIA_RIGHT"
        expand_op = header_row.operator("clonerpro.toggle_section_expanded", 
                                       text="", icon=expand_icon, emboss=False)
        expand_op.section_name = "fields_creation"
        
        # Заголовок секции с розовой иконкой
        # Используем розоватую иконку света для полей
        fields_row = header_row.row()
        fields_row.scale_y = 1.2  # Немного увеличиваем по высоте для визуального различия
        fields_row.label(text="Fields", icon='LIGHT_SUN')  # Меняем иконку на солнце (розоватая)
        
        # Счетчик созданных полей (заглушка)
        header_row.label(text="(0)", icon='BLANK1')
        
        # === СОДЕРЖИМОЕ СЕКЦИИ (только если развернуто) ===
        if not creation_expanded:
            return
            
        content_box = header_box.box()

        # 2x3 сетка кнопок создания полей
        self.draw_field_creation_grid(content_box)

    def draw_field_creation_grid(self, layout):
        """Динамическая сетка кнопок создания полей из реестра"""
        from ...core.registry.field_registry import list_field_types, get_field_info
        from ...ui.icons import get_icon_id

        # Кастомные розовые иконки для полей
        field_icon = get_icon_id("field_pink")

        # Получаем все доступные поля из реестра
        field_types = list_field_types()

        if not field_types:
            layout.label(text="No fields registered", icon='ERROR')
            return

        # Создаем сетку 2 колонки
        cols_per_row = 2
        current_row = None

        for i, field_type in enumerate(field_types):
            # Создаем новый ряд каждые 2 элемента
            if i % cols_per_row == 0:
                current_row = layout.row(align=True)
                current_row.scale_y = 1.2

            # Получаем информацию о поле
            field_info = get_field_info(field_type)
            display_name = field_info.get('display_name', field_type.title())

            # Создаем кнопку
            op = current_row.operator("clonerpro.create_field", text=display_name, icon_value=field_icon)
            op.component_type = field_type

        # Заполняем пустые места в последнем ряду
        remaining_slots = cols_per_row - (len(field_types) % cols_per_row)
        if remaining_slots < cols_per_row and current_row:
            for _ in range(remaining_slots):
                current_row.label(text="")

    def draw_browser_section(self, layout, context):
        """Коллапсируемая секция браузера клонеров/эффекторов"""
        from ...core.services.cloner_scanner import scan_scene_cloners, scan_scene_effectors
        
        # === ЗАГОЛОВОК СЕКЦИИ С КОЛЛАПСОМ ===
        browser_expanded = getattr(context.scene, "clonerpro_browser_section_expanded", True)
        
        header_box = layout.box()
        header_row = header_box.row(align=True)
        
        # Треугольник expand/collapse
        expand_icon = "TRIA_DOWN" if browser_expanded else "TRIA_RIGHT"
        expand_op = header_row.operator("clonerpro.toggle_section_expanded", 
                                       text="", icon=expand_icon, emboss=False)
        expand_op.section_name = "browser"
        
        # Заголовок с количеством элементов
        all_cloners = scan_scene_cloners(context)
        all_effectors = scan_scene_effectors(context)
        
        total_items = len(all_cloners) + len(all_effectors)
        header_row.label(text=f"Scene Browser ({total_items})", icon="WORLD")
        
        # === СОДЕРЖИМОЕ БРАУЗЕРА (только если развернуто) ===
        if not browser_expanded:
            return
        
        content_box = header_box.box()
        
        if not all_cloners and not all_effectors:
            content_box.label(text="No cloners or effectors found", icon="INFO")
            return
        
        # Отображение групп клонеров
        if all_cloners:
            groups = self.group_discovered_cloners(all_cloners)
            for group_name, group_cloners in groups.items():
                self.draw_browser_group_compact(content_box, group_name, group_cloners)
        
        # Отображение эффекторов
        if all_effectors:
            self.draw_effectors_group_compact(content_box, all_effectors)

    def draw_active_settings_section(self, layout, context):
        """Коллапсируемая секция настроек активных клонеров/эффекторов/полей"""
        from ...core.services.cloner_scanner import get_active_object_cloners, get_active_object_effectors
        
        # Получаем элементы активного объекта
        active_cloners = get_active_object_cloners(context)
        active_effectors = get_active_object_effectors(context)
        active_fields = self.get_active_object_fields(context)
        
        if not active_cloners and not active_effectors and not active_fields:
            return
        
        # === ЗАГОЛОВОК СЕКЦИИ С КОЛЛАПСОМ ===
        settings_expanded = getattr(context.scene, "clonerpro_settings_section_expanded", True)
        
        header_box = layout.box()
        header_row = header_box.row(align=True)
        
        # Треугольник expand/collapse
        expand_icon = "TRIA_DOWN" if settings_expanded else "TRIA_RIGHT"
        expand_op = header_row.operator("clonerpro.toggle_section_expanded", 
                                       text="", icon=expand_icon, emboss=False)
        expand_op.section_name = "settings"
        
        # Заголовок с количеством элементов
        total_elements = len(active_cloners) + len(active_effectors) + len(active_fields)
        header_row.label(text=f"Active Object ({total_elements})", icon="MODIFIER")
        
        # === СОДЕРЖИМОЕ НАСТРОЕК (только если развернуто) ===
        if not settings_expanded:
            return
        
        content_box = header_box.box()
        
        # Отрисовка клонеров
        for cloner_info in active_cloners:
            self.draw_individual_cloner_compact(content_box, cloner_info, context)
        
        # Отрисовка полей
        for field_info in active_fields:
            self.draw_individual_field_compact(content_box, field_info)
        
        # Отрисовка эффекторов
        for effector_info in active_effectors:
            self.draw_individual_effector_compact(content_box, effector_info)

    # === ВСПОМОГАТЕЛЬНЫЕ МЕТОДЫ (копии из оригинальной панели) ===

    def group_discovered_cloners(self, cloners):
        """Группировка обнаруженных клонеров для браузера"""
        groups = {}
        
        for cloner_info in cloners:
            cloner_mode = cloner_info.get('cloner_mode', 'Unknown')
            group_name = f"{cloner_mode.title()} Cloners"
            
            if group_name not in groups:
                groups[group_name] = []
            groups[group_name].append(cloner_info)
        
        return groups

    def draw_browser_group_compact(self, layout, group_name, cloners):
        """Компактное отображение группы клонеров в браузере"""
        from ...core.system.settings import get_settings
        
        settings = get_settings()
        
        # Получить состояние группы
        group_state = None
        for state in settings.browser_group_states:
            if state.group_name == group_name:
                group_state = state
                break
        
        is_expanded = group_state.is_expanded if group_state else True
        
        # Компактный заголовок группы
        group_row = layout.row(align=True)
        group_row.scale_y = 0.9
        
        # Треугольник для expand/collapse
        icon = 'TRIA_DOWN' if is_expanded else 'TRIA_RIGHT'
        toggle_op = group_row.operator("clonerpro.toggle_group_expanded", 
                                      text="", icon=icon, emboss=False)
        toggle_op.group_name = group_name
        
        # Название группы
        group_row.label(text=f"{group_name} ({len(cloners)})")
        
        # Отображение клонеров только если развернуто
        if is_expanded:
            items_box = layout.box()
            for cloner_info in cloners:
                self.draw_browser_cloner_item_compact(items_box, cloner_info)

    def draw_browser_cloner_item_compact(self, layout, cloner_info):
        """Компактное отображение элемента клонера в браузере"""
        item_row = layout.row(align=True)
        item_row.scale_y = 0.9
        
        # Получаем данные
        cloner_obj = cloner_info.get('object')
        modifier = cloner_info.get('modifier')
        display_name = cloner_info.get('display_name', 'Unknown Cloner')
        
        # Определяем активен ли клонер
        is_active = (bpy.context.active_object == cloner_obj if cloner_obj else False)
        
        # Кнопка выбора клонера
        select_op = item_row.operator(
            "clonerpro.select_cloner",
            text=display_name,
            icon='LAYER_ACTIVE' if is_active else 'RADIOBUT_OFF'
        )
        
        if cloner_obj:
            select_op.object_name = cloner_obj.name
        if modifier:
            select_op.modifier_name = modifier.name
        
        # Компактные кнопки управления
        actions = item_row.row(align=True)
        actions.scale_x = 0.8
        
        # Видимость
        if modifier:
            vis_icon = "HIDE_OFF" if modifier.show_viewport else "HIDE_ON"
            vis_op = actions.operator("clonerpro.toggle_component_visibility", 
                                     text="", icon=vis_icon, emboss=False)
            vis_op.object_name = cloner_obj.name if cloner_obj else ""
            vis_op.modifier_name = modifier.name
        
        # Удаление
        if modifier and cloner_obj:
            del_op = actions.operator("object.delete_cloner", text="", icon="X", emboss=False)
            del_op.object_name = cloner_obj.name
            del_op.modifier_name = modifier.name

    def draw_effectors_group_compact(self, layout, effectors):
        """Компактное отображение группы эффекторов"""
        # Компактный заголовок
        group_row = layout.row(align=True)
        group_row.scale_y = 0.9
        group_row.label(text=f"Effectors ({len(effectors)})", icon='FORCE_TURBULENCE')
        
        # Список эффекторов в компактном формате
        for effector_info in effectors:
            self.draw_browser_effector_item_compact(layout, effector_info)

    def draw_browser_effector_item_compact(self, layout, effector_info):
        """Компактное отображение эффектора в браузере"""
        item_row = layout.row(align=True)
        item_row.scale_y = 0.9
        
        # Получаем данные
        effector_object = effector_info.get('object')
        active_object = bpy.context.active_object
        display_name = effector_info.get('display_name', 'Unknown Effector')
        object_name = effector_info.get('object_name', '')
        
        # Индикатор принадлежности к активному объекту
        if effector_object and active_object and effector_object == active_object:
            item_row.label(text="", icon='LAYER_ACTIVE')
        else:
            item_row.label(text="", icon='BLANK1')
        
        # Тип эффектора с иконкой
        effector_type = effector_info.get('effector_type', 'UNKNOWN')
        effector_icons = {
            'RANDOM': 'FORCE_HARMONIC',
            'NOISE': 'FORCE_TURBULENCE', 
            'PLAIN': 'EMPTY_ARROWS'
        }
        icon = effector_icons.get(effector_type, 'MODIFIER')
        
        # Название
        label_text = f"{display_name} [{object_name}]"
        item_row.label(text=label_text, icon=icon)
        
        # Компактные кнопки управления
        actions = item_row.row(align=True)
        actions.scale_x = 0.8
        
        modifier = effector_info.get('modifier')
        obj = effector_info.get('object')
        
        if modifier and obj:
            # Видимость
            vis_icon = "HIDE_OFF" if modifier.show_viewport else "HIDE_ON"
            vis_op = actions.operator("clonerpro.toggle_effector_visibility", 
                                     text="", icon=vis_icon, emboss=False)
            vis_op.object_name = obj.name
            vis_op.modifier_name = modifier.name
            
            # Удаление
            del_op = actions.operator("clonerpro.delete_effector", text="", icon="X", emboss=False)
            del_op.object_name = obj.name
            del_op.modifier_name = modifier.name

    def draw_individual_cloner_compact(self, layout, cloner_info, context):
        """Компактное отображение индивидуального клонера с настройками"""
        from ...ui.utils.ui_helpers import is_element_expanded
        
        cloner_obj = cloner_info['object']
        modifier = cloner_info['modifier']
        display_name = cloner_info.get('display_name', 'Unknown Cloner')
        
        # Получаем состояние раскрытия
        expanded = is_element_expanded(context, cloner_obj.name, modifier.name, "cloner_expanded_states")
        
        # === КОМПАКТНЫЙ ЗАГОЛОВОК КЛОНЕРА ===
        cloner_box = layout.box()
        header_row = cloner_box.row(align=True)
        header_row.scale_y = 0.95
        
        # Expand/collapse кнопка
        expand_op = header_row.operator("clonerpro.toggle_cloner_expanded", text="", 
                                      icon='TRIA_DOWN' if expanded else 'TRIA_RIGHT', emboss=False)
        expand_op.obj_name = cloner_obj.name
        expand_op.modifier_name = modifier.name
        
        # Название клонера с кастомной зеленой иконкой
        from ...ui.icons import get_icon_id
        cloner_icon = get_icon_id("cloner_green")
        header_row.label(text=display_name, icon_value=cloner_icon)
        
        # Компактные кнопки управления
        controls = header_row.row(align=True)
        controls.scale_x = 0.8
        
        # Видимость
        vis_icon = "HIDE_OFF" if modifier.show_viewport else "HIDE_ON"
        vis_op = controls.operator("clonerpro.toggle_component_visibility", 
                                  text="", icon=vis_icon, emboss=False)
        vis_op.object_name = cloner_obj.name
        vis_op.modifier_name = modifier.name
        
        # Удаление
        del_op = controls.operator("object.delete_cloner", text="", icon="X", emboss=False)
        del_op.object_name = cloner_obj.name
        del_op.modifier_name = modifier.name
        
        # === ПАРАМЕТРЫ КЛОНЕРА (только если развернуто) ===
        if expanded:
            content_box = cloner_box.box()
            self.draw_cloner_parameters_compact(content_box, cloner_info)

    def draw_cloner_parameters_compact(self, layout, cloner_info):
        """Компактное отображение параметров клонера"""
        from ..generators.cloner_ui import UnifiedClonerUI
        
        cloner_type = cloner_info.get('cloner_type')
        modifier = cloner_info.get('modifier')
        
        if not cloner_type or not modifier:
            return
        
        # Отрисовываем настройки с компактным стилем
        UnifiedClonerUI.draw_cloner_settings(layout, bpy.context, modifier)

    def draw_individual_effector_compact(self, layout, effector_info):
        """Компактное отображение индивидуального эффектора"""
        # Получаем объект и модификатор из effector_info
        obj = effector_info.get('object')
        modifier = effector_info.get('modifier')
        
        if not obj or not modifier:
            return
        
        # Используем нашу систему отображения эффекторов
        from ...ui.generators.effector_ui import draw_effector_ui_simple
        draw_effector_ui_simple(bpy.context, layout, obj, modifier)
    
    def get_active_object_fields(self, context):
        """Получение field модификаторов активного объекта"""
        fields = []
        
        if not context.active_object:
            return fields
        
        obj = context.active_object
        
        for modifier in obj.modifiers:
            if modifier.type == 'NODES' and modifier.node_group:
                node_group_name = modifier.node_group.name.lower()
                
                # Поиск field модификаторов
                if 'field' in node_group_name and 'modifier' in node_group_name:
                    # Определяем тип поля
                    field_type = "Unknown"
                    if 'sphere' in node_group_name:
                        field_type = "Sphere"
                    elif 'box' in node_group_name:
                        field_type = "Box"
                    elif 'ring' in node_group_name:
                        field_type = "Ring"
                    
                    fields.append({
                        'object': obj,
                        'modifier': modifier,
                        'field_type': field_type,
                        'display_name': f"{field_type} Field"
                    })
        
        return fields
    
    def draw_individual_field_compact(self, layout, field_info):
        """Компактное отображение индивидуального поля"""
        from ...ui.utils.ui_helpers import is_element_expanded
        
        field_obj = field_info['object']
        modifier = field_info['modifier']
        display_name = field_info.get('display_name', 'Unknown Field')
        
        # Получаем состояние раскрытия
        expanded = is_element_expanded(bpy.context, field_obj.name, modifier.name, "field_expanded_states")
        
        # === КОМПАКТНЫЙ ЗАГОЛОВОК ПОЛЯ ===
        field_box = layout.box()
        header_row = field_box.row(align=True)
        header_row.scale_y = 0.95
        
        # Expand/collapse кнопка
        expand_op = header_row.operator("clonerpro.toggle_field_expanded", text="", 
                                      icon='TRIA_DOWN' if expanded else 'TRIA_RIGHT', emboss=False)
        expand_op.obj_name = field_obj.name
        expand_op.modifier_name = modifier.name
        
        # Название поля с кастомной розовой иконкой
        from ...ui.icons import get_icon_id
        field_icon = get_icon_id("field_pink")
        header_row.label(text=display_name, icon_value=field_icon)
        
        # Компактные кнопки управления
        controls = header_row.row(align=True)
        controls.scale_x = 0.8
        
        # Видимость
        vis_icon = "HIDE_OFF" if modifier.show_viewport else "HIDE_ON"
        vis_op = controls.operator("clonerpro.toggle_component_visibility", 
                                  text="", icon=vis_icon, emboss=False)
        vis_op.object_name = field_obj.name
        vis_op.modifier_name = modifier.name
        
        # Удаление
        del_op = controls.operator("clonerpro.delete_field", text="", icon="X", emboss=False)
        del_op.object_name = field_obj.name
        del_op.modifier_name = modifier.name
        
        # === ПАРАМЕТРЫ ПОЛЯ (только если развернуто) ===
        if expanded:
            content_box = field_box.box()
            self.draw_field_parameters_compact(content_box, field_info)
    
    def draw_field_parameters_compact(self, layout, field_info):
        """Компактное отображение параметров поля"""
        from ..generators.field_ui import draw_field_parameters
        
        field_type = field_info.get('field_type', 'SPHERE')
        modifier = field_info.get('modifier')
        
        if not modifier:
            layout.label(text="No modifier found", icon='ERROR')
            return
        
        # Отрисовываем настройки поля
        draw_field_parameters(layout, modifier, field_type)


# === ОПЕРАТОРЫ ДЛЯ ПОЛЕЙ ===

class CLONERPRO_OT_toggle_field_expanded(bpy.types.Operator):
    """Toggle field expanded state"""
    bl_idname = "clonerpro.toggle_field_expanded"
    bl_label = "Toggle Field Expanded"
    bl_description = "Toggle field expanded state"
    bl_options = {'REGISTER'}
    
    obj_name: bpy.props.StringProperty()
    modifier_name: bpy.props.StringProperty()
    
    def execute(self, context):
        from ...ui.utils.ui_helpers import toggle_element_expanded
        toggle_element_expanded(context, self.obj_name, self.modifier_name, "field_expanded_states")
        return {'FINISHED'}


class CLONERPRO_OT_delete_field(bpy.types.Operator):
    """Delete field modifier"""
    bl_idname = "clonerpro.delete_field"
    bl_label = "Delete Field"
    bl_description = "Delete field modifier"
    bl_options = {'REGISTER', 'UNDO'}
    
    object_name: bpy.props.StringProperty()
    modifier_name: bpy.props.StringProperty()
    
    def execute(self, context):
        try:
            obj = bpy.data.objects.get(self.object_name)
            if not obj or self.modifier_name not in obj.modifiers:
                self.report({'ERROR'}, f"Field {self.modifier_name} not found")
                return {'CANCELLED'}

            modifier = obj.modifiers[self.modifier_name]

            # ВАЖНО: Полная очистка Field нодов из всех эффекторов перед удалением
            self.cleanup_field_from_all_effectors(obj, modifier)

            # Очистить из сервиса синхронизации
            try:
                from ...core.services.field_synchronization import get_field_sync_service
                sync_service = get_field_sync_service()
                sync_service.cleanup_field_connections(obj, modifier)
            except Exception as e:
                print(f"Warning: Could not cleanup field sync service: {e}")

            # Удалить сам модификатор
            obj.modifiers.remove(modifier)
            self.report({'INFO'}, f"Deleted field {self.modifier_name} and cleaned up all connections")

        except Exception as e:
            self.report({'ERROR'}, f"Failed to delete field: {str(e)}")
            import traceback
            traceback.print_exc()

        return {'FINISHED'}

    def cleanup_field_from_all_effectors(self, field_obj, field_modifier):
        """Удалить все Field ноды из всех эффекторов на объекте"""
        try:
            # Найти все эффекторы на том же объекте
            for modifier in field_obj.modifiers:
                if modifier.type == 'NODES' and modifier.node_group and modifier != field_modifier:
                    node_group_name = modifier.node_group.name.lower()
                    if ('effector' in node_group_name or 'random' in node_group_name or 'noise' in node_group_name):
                        self.remove_field_node_from_effector(modifier)

        except Exception as e:
            print(f"Error cleaning up field from effectors: {e}")

    def remove_field_node_from_effector(self, effector_modifier):
        """Удалить Field ноду из конкретного эффектора и восстановить прямые подключения"""
        try:
            if not effector_modifier.node_group:
                return

            # Найти Field ноду
            field_node = None
            for node in effector_modifier.node_group.nodes:
                if node.name == "Field" and node.type == 'GROUP':
                    field_node = node
                    break

            if not field_node:
                return

            # Найти Group Input
            group_input = None
            for node in effector_modifier.node_group.nodes:
                if node.type == 'GROUP_INPUT':
                    group_input = node
                    break

            if group_input:
                # Сохранить что было подключено к выходу Field ноды
                field_targets = []
                for output in field_node.outputs:
                    for link in output.links:
                        field_targets.append((link.to_node, link.to_socket))

                # Удалить Field ноду
                effector_modifier.node_group.nodes.remove(field_node)

                # Восстановить прямые подключения к Field input из Group Input
                for field_input in group_input.outputs:
                    if field_input.name == "Field":
                        for target_node, target_socket in field_targets:
                            effector_modifier.node_group.links.new(field_input, target_socket)
                        break

                print(f"Removed Field node from effector: {effector_modifier.name}")

        except Exception as e:
            print(f"Error removing field node from {effector_modifier.name}: {e}")


# === ОПЕРАТОР ПЕРЕКЛЮЧЕНИЯ ТАБОВ ===

class CLONERPRO_OT_switch_tab(bpy.types.Operator):
    """Переключить активный таб"""
    bl_idname = "clonerpro.switch_tab"
    bl_label = "Switch Tab"
    bl_description = "Switch active tab in ClonerPro interface"
    bl_options = {'REGISTER'}
    
    tab_name: bpy.props.StringProperty()
    
    def execute(self, context):
        context.scene.clonerpro_active_tab = self.tab_name
        
        # Обновляем UI
        for area in context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
        
        return {'FINISHED'}


def register():
    """Регистрация панели с табами"""
    bpy.utils.register_class(CLONERPRO_OT_toggle_field_expanded)
    bpy.utils.register_class(CLONERPRO_OT_delete_field)
    bpy.utils.register_class(CLONERPRO_OT_switch_tab)
    bpy.utils.register_class(CLONERS_PT_Main_Tabbed)


def unregister():
    """Отмена регистрации панели с табами"""
    bpy.utils.unregister_class(CLONERS_PT_Main_Tabbed)
    bpy.utils.unregister_class(CLONERPRO_OT_switch_tab)
    bpy.utils.unregister_class(CLONERPRO_OT_delete_field)
    bpy.utils.unregister_class(CLONERPRO_OT_toggle_field_expanded)
