"""
Step Effector - Class-based implementation
Пошаговый эффектор, применяющий трансформации на основе индекса инстанса
Адаптация из exampleStepEffector.py под архитектуру ClonerPro
"""

import bpy
from ..base_effector import BaseEffector


class StepEffector(BaseEffector):
    """
    Step Effector - применяет пошаговые трансформации к клонам на основе их индекса
    Объединяет всю логику, конфигурацию и UI в одном классе
    Адаптирует функционал из exampleStepEffector.py под архитектуру проекта
    """
    
    bl_idname = "STEP"
    bl_label = "Step Effector"
    
    def __init__(self):
        super().__init__()
    
    def register_properties(self, props_owner):
        """
        Регистрация всех свойств Step Effector
        """
        # Регистрируем базовые свойства
        super().register_properties(props_owner)
        
        # Step Settings - специфичные для Step Effector
        props_owner.step_uniform_scale = bpy.props.BoolProperty(
            name="Uniform Scale",
            description="Use only first scale input (x-value) for all axes",
            default=False
        )
    
    def get_specific_sockets(self):
        """
        Специфичные сокеты для Step Effector
        
        Returns:
            list: Список специфичных сокетов (name, socket_type, in_out, default, min, max)
        """
        return [
            ("Uniform Scale", "NodeSocketBool", "INPUT", False),
        ]
    
    def get_default_parameters(self):
        """Параметры по умолчанию для Step Effector"""
        base_defaults = super().get_default_parameters()
        step_defaults = {
            "uniform_scale": False
        }
        return {**base_defaults, **step_defaults}
    
    def get_parameter_groups(self):
        """Группировка параметров для UI"""
        base_groups = super().get_effector_parameter_groups()
        step_groups = {
            "Step Settings": ["Uniform Scale"],
        }
        return {**step_groups, **base_groups}

    def draw_ui(self, layout, context, modifier):
        """
        Отрисовка UI для Step Effector
        """
        from ...ui.utils.ui_helpers import display_socket_prop

        # Получаем группы параметров
        parameter_groups = self.get_parameter_groups()

        # Отображаем группы параметров
        for group_name, param_names in parameter_groups.items():
            if param_names:
                # Создаем бокс для группы
                box = layout.box()
                col = box.column(align=True)

                # Заголовок группы с иконкой
                icon_map = {
                    "Step Settings": "SORTTIME",
                    "Effector Settings": "SETTINGS",
                    "Transform": "ORIENTATION_GIMBAL",
                    "Random Settings": "FORCE_HARMONIC"
                }
                icon = icon_map.get(group_name, "DOT")
                col.label(text=f"{group_name}:", icon=icon)

                # Добавляем параметры
                col.separator()
                for param_name in param_names:
                    display_socket_prop(col, modifier, param_name, text=param_name)
    
    def _create_effector_logic(self, base_nodes):
        """
        Основная логика Step Effector - адаптация из exampleStepEffector.py
        
        Args:
            base_nodes: Словарь с базовыми нодами
            
        Returns:
            NodeSocket: Финальный выход геометрии с примененным эффектом
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        index = base_nodes['index']
        
        # Domain Size для получения общего количества инстансов
        domain_size = nodes.new('GeometryNodeAttributeDomainSize')
        domain_size.component = 'INSTANCES'
        domain_size.location = (-600, -400)
        links.new(group_input.outputs['Geometry'], domain_size.inputs['Geometry'])
        
        # Math node для деления 1.0 на количество инстансов
        math_divide = nodes.new('ShaderNodeMath')
        math_divide.operation = 'DIVIDE'
        math_divide.use_clamp = False
        math_divide.inputs[0].default_value = 1.0
        math_divide.location = (-400, -400)
        links.new(domain_size.outputs['Instance Count'], math_divide.inputs[1])
        
        # Math node для умножения индекса на результат деления
        math_multiply = nodes.new('ShaderNodeMath')
        math_multiply.operation = 'MULTIPLY'
        math_multiply.use_clamp = False
        math_multiply.location = (-200, -300)
        links.new(index.outputs['Index'], math_multiply.inputs[0])
        links.new(math_divide.outputs[0], math_multiply.inputs[1])
        
        # =======================================================================
        # TRANSLATION (Position) - аналогично примеру
        # =======================================================================
        
        # Vector Math для умножения translation на step value
        translation_multiply = nodes.new('ShaderNodeVectorMath')
        translation_multiply.operation = 'MULTIPLY'
        translation_multiply.location = (0, 100)
        links.new(group_input.outputs['Position'], translation_multiply.inputs[0])
        links.new(math_multiply.outputs[0], translation_multiply.inputs[1])
        
        # =======================================================================
        # ROTATION - аналогично примеру
        # =======================================================================
        
        # Vector Math для умножения rotation на step value
        rotation_multiply = nodes.new('ShaderNodeVectorMath')
        rotation_multiply.operation = 'MULTIPLY'
        rotation_multiply.location = (0, -100)
        links.new(group_input.outputs['Rotation'], rotation_multiply.inputs[0])
        links.new(math_multiply.outputs[0], rotation_multiply.inputs[1])
        
        # =======================================================================
        # SCALE - более сложная логика как в примере
        # =======================================================================
        
        # Separate XYZ для разделения scale компонентов
        separate_xyz = nodes.new('ShaderNodeSeparateXYZ')
        separate_xyz.location = (-400, -600)
        links.new(group_input.outputs['Scale'], separate_xyz.inputs[0])
        
        # --- Scale X ---
        # (scale.x - 1) / instance_count
        scale_x_minus_one = nodes.new('ShaderNodeMath')
        scale_x_minus_one.operation = 'SUBTRACT'
        scale_x_minus_one.inputs[1].default_value = 1.0
        scale_x_minus_one.location = (-200, -500)
        links.new(separate_xyz.outputs[0], scale_x_minus_one.inputs[0])
        
        scale_x_divide = nodes.new('ShaderNodeMath')
        scale_x_divide.operation = 'DIVIDE'
        scale_x_divide.location = (-100, -500)
        links.new(scale_x_minus_one.outputs[0], scale_x_divide.inputs[0])
        links.new(domain_size.outputs['Instance Count'], scale_x_divide.inputs[1])
        
        # index * divided_scale_x
        scale_x_multiply = nodes.new('ShaderNodeMath')
        scale_x_multiply.operation = 'MULTIPLY'
        scale_x_multiply.location = (0, -500)
        links.new(index.outputs['Index'], scale_x_multiply.inputs[0])
        links.new(scale_x_divide.outputs[0], scale_x_multiply.inputs[1])
        
        # 1 + result
        scale_x_add_one = nodes.new('ShaderNodeMath')
        scale_x_add_one.operation = 'ADD'
        scale_x_add_one.inputs[1].default_value = 1.0
        scale_x_add_one.location = (100, -500)
        links.new(scale_x_multiply.outputs[0], scale_x_add_one.inputs[0])
        
        # --- Scale Y ---
        scale_y_minus_one = nodes.new('ShaderNodeMath')
        scale_y_minus_one.operation = 'SUBTRACT'
        scale_y_minus_one.inputs[1].default_value = 1.0
        scale_y_minus_one.location = (-200, -600)
        links.new(separate_xyz.outputs[1], scale_y_minus_one.inputs[0])
        
        scale_y_divide = nodes.new('ShaderNodeMath')
        scale_y_divide.operation = 'DIVIDE'
        scale_y_divide.location = (-100, -600)
        links.new(scale_y_minus_one.outputs[0], scale_y_divide.inputs[0])
        links.new(domain_size.outputs['Instance Count'], scale_y_divide.inputs[1])
        
        scale_y_multiply = nodes.new('ShaderNodeMath')
        scale_y_multiply.operation = 'MULTIPLY'
        scale_y_multiply.location = (0, -600)
        links.new(index.outputs['Index'], scale_y_multiply.inputs[0])
        links.new(scale_y_divide.outputs[0], scale_y_multiply.inputs[1])
        
        scale_y_add_one = nodes.new('ShaderNodeMath')
        scale_y_add_one.operation = 'ADD'
        scale_y_add_one.inputs[1].default_value = 1.0
        scale_y_add_one.location = (100, -600)
        links.new(scale_y_multiply.outputs[0], scale_y_add_one.inputs[0])
        
        # --- Scale Z ---
        scale_z_minus_one = nodes.new('ShaderNodeMath')
        scale_z_minus_one.operation = 'SUBTRACT'
        scale_z_minus_one.inputs[1].default_value = 1.0
        scale_z_minus_one.location = (-200, -700)
        links.new(separate_xyz.outputs[2], scale_z_minus_one.inputs[0])
        
        scale_z_divide = nodes.new('ShaderNodeMath')
        scale_z_divide.operation = 'DIVIDE'
        scale_z_divide.location = (-100, -700)
        links.new(scale_z_minus_one.outputs[0], scale_z_divide.inputs[0])
        links.new(domain_size.outputs['Instance Count'], scale_z_divide.inputs[1])
        
        scale_z_multiply = nodes.new('ShaderNodeMath')
        scale_z_multiply.operation = 'MULTIPLY'
        scale_z_multiply.location = (0, -700)
        links.new(index.outputs['Index'], scale_z_multiply.inputs[0])
        links.new(scale_z_divide.outputs[0], scale_z_multiply.inputs[1])
        
        scale_z_add_one = nodes.new('ShaderNodeMath')
        scale_z_add_one.operation = 'ADD'
        scale_z_add_one.inputs[1].default_value = 1.0
        scale_z_add_one.location = (100, -700)
        links.new(scale_z_multiply.outputs[0], scale_z_add_one.inputs[0])
        
        # Combine XYZ для создания вектора scale (non-uniform)
        combine_xyz_nonuniform = nodes.new('ShaderNodeCombineXYZ')
        combine_xyz_nonuniform.location = (200, -600)
        links.new(scale_x_add_one.outputs[0], combine_xyz_nonuniform.inputs[0])
        links.new(scale_y_add_one.outputs[0], combine_xyz_nonuniform.inputs[1])
        links.new(scale_z_add_one.outputs[0], combine_xyz_nonuniform.inputs[2])
        
        # Combine XYZ для создания uniform scale (только X компонент)
        combine_xyz_uniform = nodes.new('ShaderNodeCombineXYZ')
        combine_xyz_uniform.location = (200, -500)
        links.new(scale_x_add_one.outputs[0], combine_xyz_uniform.inputs[0])
        links.new(scale_x_add_one.outputs[0], combine_xyz_uniform.inputs[1])
        links.new(scale_x_add_one.outputs[0], combine_xyz_uniform.inputs[2])
        
        # Switch между uniform и non-uniform scale
        scale_switch = nodes.new('GeometryNodeSwitch')
        scale_switch.input_type = 'VECTOR'
        scale_switch.location = (400, -550)
        links.new(group_input.outputs['Uniform Scale'], scale_switch.inputs[0])  # Switch
        links.new(combine_xyz_nonuniform.outputs[0], scale_switch.inputs[1])     # False (non-uniform)
        links.new(combine_xyz_uniform.outputs[0], scale_switch.inputs[2])        # True (uniform)
        
        # =======================================================================
        # ПРИМЕНЕНИЕ STRENGTH И FIELD (как в других эффекторах)
        # =======================================================================
        
        # Combine strength с field mask
        field_strength_combine = nodes.new('ShaderNodeMath')
        field_strength_combine.operation = 'MULTIPLY'
        field_strength_combine.location = (200, -800)
        links.new(group_input.outputs['Strength'], field_strength_combine.inputs[0])
        links.new(group_input.outputs['Field'], field_strength_combine.inputs[1])
        
        # Apply field+strength к translation
        translation_final = nodes.new('ShaderNodeVectorMath')
        translation_final.operation = 'MULTIPLY'
        translation_final.location = (300, 100)
        links.new(translation_multiply.outputs[0], translation_final.inputs[0])
        links.new(field_strength_combine.outputs[0], translation_final.inputs[1])
        
        # Apply field+strength к rotation
        rotation_final = nodes.new('ShaderNodeVectorMath')
        rotation_final.operation = 'MULTIPLY'
        rotation_final.location = (300, -100)
        links.new(rotation_multiply.outputs[0], rotation_final.inputs[0])
        links.new(field_strength_combine.outputs[0], rotation_final.inputs[1])
        
        # Apply field+strength к scale: 1.0 + (scale - 1.0) * field_strength
        scale_offset = nodes.new('ShaderNodeVectorMath')
        scale_offset.operation = 'SUBTRACT'
        scale_offset.inputs[1].default_value = (1.0, 1.0, 1.0)
        scale_offset.location = (500, -550)
        links.new(scale_switch.outputs[0], scale_offset.inputs[0])
        
        # Create vector из field_strength для scale multiplication
        field_strength_vector = nodes.new('ShaderNodeCombineXYZ')
        field_strength_vector.location = (300, -800)
        links.new(field_strength_combine.outputs[0], field_strength_vector.inputs[0])
        links.new(field_strength_combine.outputs[0], field_strength_vector.inputs[1])
        links.new(field_strength_combine.outputs[0], field_strength_vector.inputs[2])
        
        scale_mul_field = nodes.new('ShaderNodeVectorMath')
        scale_mul_field.operation = 'MULTIPLY'
        scale_mul_field.location = (600, -550)
        links.new(scale_offset.outputs[0], scale_mul_field.inputs[0])
        links.new(field_strength_vector.outputs[0], scale_mul_field.inputs[1])
        
        scale_final = nodes.new('ShaderNodeVectorMath')
        scale_final.operation = 'ADD'
        scale_final.inputs[1].default_value = (1.0, 1.0, 1.0)
        scale_final.location = (700, -550)
        links.new(scale_mul_field.outputs[0], scale_final.inputs[0])
        
        # =======================================================================
        # ПРИМЕНЕНИЕ ТРАНСФОРМАЦИЙ К ИНСТАНСАМ
        # =======================================================================
        
        # Translate Instances
        translate_instances = nodes.new('GeometryNodeTranslateInstances')
        translate_instances.location = (500, 100)
        links.new(group_input.outputs['Geometry'], translate_instances.inputs['Instances'])
        links.new(translation_final.outputs[0], translate_instances.inputs['Translation'])
        
        # Rotate Instances
        rotate_instances = nodes.new('GeometryNodeRotateInstances')
        rotate_instances.location = (700, 0)
        links.new(translate_instances.outputs['Instances'], rotate_instances.inputs['Instances'])
        links.new(rotation_final.outputs[0], rotate_instances.inputs['Rotation'])
        
        # Scale Instances
        scale_instances = nodes.new('GeometryNodeScaleInstances')
        scale_instances.location = (900, -100)
        links.new(rotate_instances.outputs['Instances'], scale_instances.inputs['Instances'])
        links.new(scale_final.outputs[0], scale_instances.inputs['Scale'])
        
        return scale_instances.outputs['Instances']


# Compatibility function for easy integration
def create_step_effector(name_suffix=""):
    """Create Step Effector using class-based approach"""
    effector = StepEffector()
    return effector.create_node_group(name_suffix)


# Alias for backward compatibility
def create_step_effector_logic_group(name_suffix=""):
    """Create Step Effector logic group - class-based compatibility function"""
    return create_step_effector(name_suffix)