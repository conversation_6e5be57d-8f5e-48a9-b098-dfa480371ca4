# ClonerPro Registry System

Современная система регистрации компонентов с поддержкой wrapper архитектуры.

## 📁 Структура

```
core/registry/
├── main_registry.py      # Главный реестр (новый)
├── cloner_registry.py    # Реестр клонеров
├── effector_registry.py  # Реестр эффекторов
├── field_registry.py     # Реестр полей
├── __init__.py          # Экспорты и обратная совместимость
└── README.md            # Этот файл
```

## 🏗️ Архитектура

### Главный реестр (`main_registry.py`)

Центральная точка доступа ко всем компонентам:

```python
from core.registry import main_registry

# Получить компонент
cloner_class = main_registry.get_cloner("GRID")
effector_class = main_registry.get_effector("RANDOM")
field_class = main_registry.get_field("SPHERE")

# Универсальный доступ
component = main_registry.get_component("GRID", "cloner")

# Списки компонентов
all_cloners = main_registry.list_cloners()
all_components = main_registry.list_all_components()

# Информация о компоненте
info = main_registry.get_component_info("GRID", "cloner")
```

### Специализированные реестры

Каждый тип компонента имеет свой реестр:

- **`cloner_registry.py`** - все клонеры (Grid, Linear, Circle, Object, Spline, Spiral)
- **`effector_registry.py`** - все эффекторы (Random, Noise)
- **`field_registry.py`** - все поля (Sphere)

## 🎯 Современный API

Используйте новый главный реестр для всех операций:

```python
# Рекомендуемый способ
from core.registry import main_registry
cloner = main_registry.get_cloner("GRID")
effector = main_registry.get_effector("RANDOM")
field = main_registry.get_field("SPHERE")
```

## ✅ Wrapper поддержка

Все компоненты используют единую wrapper архитектуру:

- **Консистентность** - все компоненты работают одинаково
- **Управление** - параметры доступны в модификаторе и нодах
- **Переносимость** - можно копировать как единую ноду

## 📊 Валидация

Автоматическая проверка корректности регистрации:

```python
# Проверить все регистрации
is_valid = main_registry.validate_all_registrations()

# Статистика реестра
main_registry.print_registry_stats()
```

## 🚀 Добавление нового компонента

### 1. Создать класс компонента

```python
# components/cloners/my_cloner.py
from ..base_cloner import BaseCloner

class MyCloner(BaseCloner):
    bl_label = "My Cloner"
    bl_description = "My custom cloner"
    
    def _create_cloner_logic(self, base_nodes, mode):
        # Ваша логика здесь
        pass
```

### 2. Зарегистрировать в реестре

```python
# core/registry/cloner_registry.py
from ...components.cloners.my_cloner import MyCloner

CLONER_REGISTRY = {
    # ... существующие клонеры
    "MY_CLONER": MyCloner,
}
```

### 3. Готово!

Компонент автоматически появится в главном реестре и будет доступен через все API.

## 🚀 Простое использование

```python
from core.registry import main_registry

# Получить и создать компонент
cloner_class = main_registry.get_cloner("GRID")
if cloner_class:
    cloner_instance = cloner_class()
    node_group = cloner_instance.create_node_group()
```

## 📈 Преимущества новой системы

1. **Единый интерфейс** - все компоненты через один API
2. **Wrapper архитектура** - консистентное поведение
3. **Типизация** - полная поддержка типов Python
4. **Валидация** - автоматическая проверка корректности
5. **Расширяемость** - легко добавлять новые компоненты
6. **Обратная совместимость** - старый код продолжает работать

## 🎯 Использование в коде

### Рекомендуемый способ:
```python
from core.registry import main_registry

# Получить и создать компонент
cloner_class = main_registry.get_cloner("GRID")
if cloner_class:
    cloner_instance = cloner_class()
    node_group = cloner_instance.create_node_group()
```

### Для обратной совместимости:
```python
from core.registry import get_cloner_creator

# Старый API продолжает работать
cloner_class = get_cloner_creator("GRID")
```
