"""
Grid Cloner - Class-based implementation
Объединяет логику и конфигурацию в один класс
"""

import bpy
from ..base_cloner import BaseCloner


class GridCloner(BaseCloner):
    """
    Grid Cloner - создает 2D/3D сетку клонов
    Объединяет всю логику, конфигурацию и UI в одном классе
    """
    
    bl_idname = "GRID"
    bl_label = "Grid Cloner"
    
    def __init__(self):
        super().__init__()
    
    def register_properties(self, props_owner):
        """
        Регистрация всех свойств Grid Cloner
        Заменяет старый grid_config.py
        """
        # Grid Settings
        props_owner.grid_count_x = bpy.props.IntProperty(
            name="Count X",
            description="Number of instances along X axis",
            default=3,
            min=1,
            max=100
        )
        props_owner.grid_count_y = bpy.props.IntProperty(
            name="Count Y", 
            description="Number of instances along Y axis",
            default=3,
            min=1,
            max=100
        )
        props_owner.grid_count_z = bpy.props.IntProperty(
            name="Count Z",
            description="Number of instances along Z axis", 
            default=1,
            min=1,
            max=100
        )
        props_owner.grid_spacing = bpy.props.FloatVectorProperty(
            name="Spacing",
            description="Spacing between instances",
            default=(3.0, 3.0, 3.0),
            size=3
        )

        # 🆕 НОВЫЕ ПАРАМЕТРЫ из ROADMAP:
        props_owner.grid_offset_pattern = bpy.props.FloatVectorProperty(
            name="Offset Pattern",
            description="Checkerboard pattern offset for alternating rows/columns",
            default=(0.0, 0.0, 0.0),
            size=3
        )
        props_owner.grid_scale_falloff = bpy.props.FloatProperty(
            name="Scale Falloff",
            description="Scale falloff towards grid edges (0.0 = no falloff, 1.0 = full falloff)",
            default=0.0,
            min=0.0,
            max=1.0
        )
        props_owner.grid_rotation_variation = bpy.props.FloatVectorProperty(
            name="Rotation Variation",
            description="Random rotation variation for each grid element",
            default=(0.0, 0.0, 0.0),
            size=3
        )

        # Instance Transform (базовые свойства уже определены в BaseCloner)
        # Randomization (базовые свойства уже определены в BaseCloner)
        # Global Transform (базовые свойства уже определены в BaseCloner)
    
    def get_specific_sockets(self):
        """
        Специфичные сокеты для Grid Cloner

        Returns:
            list: Список специфичных сокетов (name, socket_type, in_out, default)
        """
        return [
            # Основные параметры сетки
            ("Count X", "NodeSocketInt", "INPUT", 3),
            ("Count Y", "NodeSocketInt", "INPUT", 3),
            ("Count Z", "NodeSocketInt", "INPUT", 1),
            ("Spacing", "NodeSocketVector", "INPUT", (3.0, 3.0, 3.0)),

            # 🆕 НОВЫЕ ВОЗМОЖНОСТИ из ROADMAP:
            ("Offset Pattern", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),  # Шахматный паттерн
            ("Scale Falloff", "NodeSocketFloat", "INPUT", 0.0),               # Уменьшение к краям
            ("Rotation Variation", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),  # Вариация поворота
        ]
    
    
    def _create_cloner_logic(self, base_nodes, mode):
        """
        Основная логика Grid Cloner
        
        Args:
            base_nodes: Словарь с базовыми нодами
            mode: Режим клонера
            
        Returns:
            NodeSocket: Финальный выход геометрии
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Получаем input геометрию в зависимости от режима
        geometry_input = self.get_geometry_input(base_nodes, mode)
        
        # Создаем сетку точек
        grid_points = self._create_grid_points(base_nodes)
        
        # Применяем центрирование
        centered_points = self._apply_grid_centering(base_nodes, grid_points)

        # 🆕 Применяем шахматный паттерн (Offset Pattern)
        pattern_points = self._apply_offset_pattern(base_nodes, centered_points)

        # Инстансируем геометрию на точки
        instance_node = nodes.new('GeometryNodeInstanceOnPoints')
        instance_node.name = "Instance on Grid"
        instance_node.location = (400, 0)
        links.new(pattern_points, instance_node.inputs['Points'])
        links.new(geometry_input, instance_node.inputs['Instance'])

        # Применяем Collection Random для unified клонеров в Collection режиме
        self.apply_collection_randomization(base_nodes, instance_node, mode)

        # Получаем индекс для рандомизации
        index_node = nodes.new('GeometryNodeInputIndex')
        index_node.location = (200, -200)
        
        # Применяем instance трансформации
        instances_with_transforms = self.apply_instance_transforms(base_nodes, instance_node.outputs['Instances'])

        # 🆕 Применяем Scale Falloff (уменьшение к краям)
        instances_with_falloff = self._apply_scale_falloff(base_nodes, instances_with_transforms)

        # 🆕 Применяем Rotation Variation (вариация поворота)
        instances_with_rotation_var = self._apply_rotation_variation(base_nodes, instances_with_falloff)

        # Применяем рандомизацию
        randomized_instances = self.apply_random_transforms(base_nodes, instances_with_rotation_var, index_node.outputs['Index'])

        # Применяем глобальные трансформации
        final_geometry = self.apply_global_transforms(base_nodes, randomized_instances)
        
        return final_geometry
    
    def _create_grid_points(self, base_nodes):
        """
        Создание сетки точек с 2D/3D логикой
        
        Args:
            base_nodes: Словарь с базовыми нодами
            
        Returns:
            NodeSocket: Выход с сеткой точек
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Множитель для spacing
        spacing_multiplier = nodes.new('ShaderNodeVectorMath')
        spacing_multiplier.operation = 'MULTIPLY'
        spacing_multiplier.inputs[1].default_value = (1.0, 1.0, 1.0)
        spacing_multiplier.location = (-500, 300)
        links.new(group_input.outputs['Spacing'], spacing_multiplier.inputs[0])
        
        # Разделяем spacing по компонентам
        separate_xyz_spacing = nodes.new('ShaderNodeSeparateXYZ')
        separate_xyz_spacing.location = (-400, 300)
        links.new(spacing_multiplier.outputs['Vector'], separate_xyz_spacing.inputs['Vector'])
        
        # Step 1: Линия X
        line_x = nodes.new('GeometryNodeMeshLine')
        line_x.name = "Line X Points"
        line_x.mode = 'OFFSET'
        line_x.count_mode = 'TOTAL'
        line_x.location = (-300, 400)
        links.new(group_input.outputs['Count X'], line_x.inputs['Count'])
        
        # Офсет для X оси
        combine_x_offset = nodes.new('ShaderNodeCombineXYZ')
        combine_x_offset.location = (-400, 400)
        links.new(separate_xyz_spacing.outputs['X'], combine_x_offset.inputs['X'])
        combine_x_offset.inputs['Y'].default_value = 0.0
        combine_x_offset.inputs['Z'].default_value = 0.0
        links.new(combine_x_offset.outputs['Vector'], line_x.inputs['Offset'])
        
        # Step 2: Линия Y
        line_y = nodes.new('GeometryNodeMeshLine')
        line_y.name = "Line Y Points"
        line_y.mode = 'OFFSET'
        line_y.count_mode = 'TOTAL'
        line_y.location = (-300, 300)
        links.new(group_input.outputs['Count Y'], line_y.inputs['Count'])
        
        # Офсет для Y оси
        combine_y_offset = nodes.new('ShaderNodeCombineXYZ')
        combine_y_offset.location = (-400, 300)
        combine_y_offset.inputs['X'].default_value = 0.0
        links.new(separate_xyz_spacing.outputs['Y'], combine_y_offset.inputs['Y'])
        combine_y_offset.inputs['Z'].default_value = 0.0
        links.new(combine_y_offset.outputs['Vector'], line_y.inputs['Offset'])
        
        # Step 3: Инстансирование X на Y для 2D сетки
        instance_x_on_y = nodes.new('GeometryNodeInstanceOnPoints')
        instance_x_on_y.name = "Instance X on Y"
        instance_x_on_y.location = (-200, 350)
        links.new(line_y.outputs['Mesh'], instance_x_on_y.inputs['Points'])
        links.new(line_x.outputs['Mesh'], instance_x_on_y.inputs['Instance'])
        
        # Реализация 2D сетки
        realize_2d_grid = nodes.new('GeometryNodeRealizeInstances')
        realize_2d_grid.name = "Realize 2D Grid"
        realize_2d_grid.location = (-100, 350)
        links.new(instance_x_on_y.outputs['Instances'], realize_2d_grid.inputs['Geometry'])
        
        # Step 4: Линия Z
        line_z = nodes.new('GeometryNodeMeshLine')
        line_z.name = "Line Z Points"
        line_z.mode = 'OFFSET'
        line_z.count_mode = 'TOTAL'
        line_z.location = (-300, 200)
        links.new(group_input.outputs['Count Z'], line_z.inputs['Count'])
        
        # Офсет для Z оси
        combine_z_offset = nodes.new('ShaderNodeCombineXYZ')
        combine_z_offset.location = (-400, 200)
        combine_z_offset.inputs['X'].default_value = 0.0
        combine_z_offset.inputs['Y'].default_value = 0.0
        links.new(separate_xyz_spacing.outputs['Z'], combine_z_offset.inputs['Z'])
        links.new(combine_z_offset.outputs['Vector'], line_z.inputs['Offset'])
        
        # Step 5: Инстансирование 2D на Z для 3D сетки
        instance_2d_on_z = nodes.new('GeometryNodeInstanceOnPoints')
        instance_2d_on_z.name = "Instance 2D on Z"
        instance_2d_on_z.location = (0, 300)
        links.new(line_z.outputs['Mesh'], instance_2d_on_z.inputs['Points'])
        links.new(realize_2d_grid.outputs['Geometry'], instance_2d_on_z.inputs['Instance'])
        
        # Реализация 3D сетки
        realize_3d_grid = nodes.new('GeometryNodeRealizeInstances')
        realize_3d_grid.name = "Realize 3D Grid"
        realize_3d_grid.location = (100, 300)
        links.new(instance_2d_on_z.outputs['Instances'], realize_3d_grid.inputs['Geometry'])
        
        # Переключение между 2D и 3D
        compare_z_count = nodes.new('FunctionNodeCompare')
        compare_z_count.data_type = 'INT'
        compare_z_count.operation = 'GREATER_THAN'
        compare_z_count.inputs[3].default_value = 1
        compare_z_count.location = (0, 200)
        links.new(group_input.outputs['Count Z'], compare_z_count.inputs[2])
        
        switch_points = nodes.new('GeometryNodeSwitch')
        switch_points.name = "Switch 2D/3D Points"
        switch_points.input_type = 'GEOMETRY'
        switch_points.location = (200, 250)
        links.new(compare_z_count.outputs['Result'], switch_points.inputs['Switch'])
        links.new(realize_2d_grid.outputs['Geometry'], switch_points.inputs[False])
        links.new(realize_3d_grid.outputs['Geometry'], switch_points.inputs[True])
        
        # Сохраняем separate_xyz_spacing в классе для использования в centering
        self._separate_xyz_spacing = separate_xyz_spacing
        
        return switch_points.outputs['Output']
    
    def _apply_grid_centering(self, base_nodes, grid_points):
        """
        Применение центрирования к сетке
        
        Args:
            base_nodes: Словарь с базовыми нодами
            grid_points: Входной сокет с точками сетки
            
        Returns:
            NodeSocket: Выход с отцентрированной сеткой
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        separate_xyz_spacing = self._separate_xyz_spacing
        
        # Вычисляем X размер: (Count X - 1) * Spacing X
        count_x_minus_one = nodes.new('ShaderNodeMath')
        count_x_minus_one.operation = 'SUBTRACT'
        count_x_minus_one.inputs[1].default_value = 1.0
        count_x_minus_one.location = (-200, 100)
        links.new(group_input.outputs['Count X'], count_x_minus_one.inputs[0])
        
        total_size_x = nodes.new('ShaderNodeMath')
        total_size_x.operation = 'MULTIPLY'
        total_size_x.location = (-100, 100)
        links.new(count_x_minus_one.outputs['Value'], total_size_x.inputs[0])
        links.new(separate_xyz_spacing.outputs['X'], total_size_x.inputs[1])
        
        # Делим на -2 для получения половинного офсета
        half_offset_x = nodes.new('ShaderNodeMath')
        half_offset_x.operation = 'DIVIDE'
        half_offset_x.inputs[1].default_value = -2.0
        half_offset_x.location = (0, 100)
        links.new(total_size_x.outputs['Value'], half_offset_x.inputs[0])
        
        # То же самое для Y
        count_y_minus_one = nodes.new('ShaderNodeMath')
        count_y_minus_one.operation = 'SUBTRACT'
        count_y_minus_one.inputs[1].default_value = 1.0
        count_y_minus_one.location = (-200, 0)
        links.new(group_input.outputs['Count Y'], count_y_minus_one.inputs[0])
        
        total_size_y = nodes.new('ShaderNodeMath')
        total_size_y.operation = 'MULTIPLY'
        total_size_y.location = (-100, 0)
        links.new(count_y_minus_one.outputs['Value'], total_size_y.inputs[0])
        links.new(separate_xyz_spacing.outputs['Y'], total_size_y.inputs[1])
        
        half_offset_y = nodes.new('ShaderNodeMath')
        half_offset_y.operation = 'DIVIDE'
        half_offset_y.inputs[1].default_value = -2.0
        half_offset_y.location = (0, 0)
        links.new(total_size_y.outputs['Value'], half_offset_y.inputs[0])
        
        # То же самое для Z
        count_z_minus_one = nodes.new('ShaderNodeMath')
        count_z_minus_one.operation = 'SUBTRACT'
        count_z_minus_one.inputs[1].default_value = 1.0
        count_z_minus_one.location = (-200, -100)
        links.new(group_input.outputs['Count Z'], count_z_minus_one.inputs[0])
        
        total_size_z = nodes.new('ShaderNodeMath')
        total_size_z.operation = 'MULTIPLY'
        total_size_z.location = (-100, -100)
        links.new(count_z_minus_one.outputs['Value'], total_size_z.inputs[0])
        links.new(separate_xyz_spacing.outputs['Z'], total_size_z.inputs[1])
        
        half_offset_z = nodes.new('ShaderNodeMath')
        half_offset_z.operation = 'DIVIDE'
        half_offset_z.inputs[1].default_value = -2.0
        half_offset_z.location = (0, -100)
        links.new(total_size_z.outputs['Value'], half_offset_z.inputs[0])
        
        # Объединяем в вектор центрирования
        combine_center_offset = nodes.new('ShaderNodeCombineXYZ')
        combine_center_offset.location = (100, 0)
        links.new(half_offset_x.outputs['Value'], combine_center_offset.inputs['X'])
        links.new(half_offset_y.outputs['Value'], combine_center_offset.inputs['Y'])
        links.new(half_offset_z.outputs['Value'], combine_center_offset.inputs['Z'])
        
        # Применяем центрирование
        set_position = nodes.new('GeometryNodeSetPosition')
        set_position.name = "Center Grid Points"
        set_position.location = (200, 200)
        links.new(grid_points, set_position.inputs['Geometry'])
        links.new(combine_center_offset.outputs['Vector'], set_position.inputs['Offset'])
        
        return set_position.outputs['Geometry']

    def _apply_offset_pattern(self, base_nodes, grid_points):
        """
        Применение шахматного паттерна к сетке

        Args:
            base_nodes: Словарь с базовыми нодами
            grid_points: Входной сокет с точками сетки

        Returns:
            NodeSocket: Выход с примененным паттерном
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']

        # Получаем позиции точек
        position_node = nodes.new('GeometryNodeInputPosition')
        position_node.location = (300, -200)

        # Разделяем координаты
        separate_xyz = nodes.new('ShaderNodeSeparateXYZ')
        separate_xyz.location = (400, -200)
        links.new(position_node.outputs['Position'], separate_xyz.inputs['Vector'])

        # Создаем индексы для шахматного паттерна
        # Делим X и Y координаты на spacing для получения индексов
        separate_spacing = nodes.new('ShaderNodeSeparateXYZ')
        separate_spacing.location = (300, -300)
        links.new(group_input.outputs['Spacing'], separate_spacing.inputs['Vector'])

        # X индекс = floor(X / spacing_x)
        x_index = nodes.new('ShaderNodeMath')
        x_index.operation = 'DIVIDE'
        x_index.location = (500, -150)
        links.new(separate_xyz.outputs['X'], x_index.inputs[0])
        links.new(separate_spacing.outputs['X'], x_index.inputs[1])

        x_index_floor = nodes.new('ShaderNodeMath')
        x_index_floor.operation = 'FLOOR'
        x_index_floor.location = (600, -150)
        links.new(x_index.outputs['Value'], x_index_floor.inputs[0])

        # Y индекс = floor(Y / spacing_y)
        y_index = nodes.new('ShaderNodeMath')
        y_index.operation = 'DIVIDE'
        y_index.location = (500, -250)
        links.new(separate_xyz.outputs['Y'], y_index.inputs[0])
        links.new(separate_spacing.outputs['Y'], y_index.inputs[1])

        y_index_floor = nodes.new('ShaderNodeMath')
        y_index_floor.operation = 'FLOOR'
        y_index_floor.location = (600, -250)
        links.new(y_index.outputs['Value'], y_index_floor.inputs[0])

        # Шахматный паттерн: (x_index + y_index) % 2
        sum_indices = nodes.new('ShaderNodeMath')
        sum_indices.operation = 'ADD'
        sum_indices.location = (700, -200)
        links.new(x_index_floor.outputs['Value'], sum_indices.inputs[0])
        links.new(y_index_floor.outputs['Value'], sum_indices.inputs[1])

        modulo_pattern = nodes.new('ShaderNodeMath')
        modulo_pattern.operation = 'MODULO'
        modulo_pattern.inputs[1].default_value = 2.0
        modulo_pattern.location = (800, -200)
        links.new(sum_indices.outputs['Value'], modulo_pattern.inputs[0])

        # Применяем offset только к нечетным элементам
        # Создаем вектор смещения
        pattern_vector = nodes.new('ShaderNodeVectorMath')
        pattern_vector.operation = 'MULTIPLY'
        pattern_vector.location = (900, -200)
        links.new(group_input.outputs['Offset Pattern'], pattern_vector.inputs[0])

        # Преобразуем модуло в вектор
        combine_pattern = nodes.new('ShaderNodeCombineXYZ')
        combine_pattern.location = (850, -250)
        links.new(modulo_pattern.outputs['Value'], combine_pattern.inputs['X'])
        links.new(modulo_pattern.outputs['Value'], combine_pattern.inputs['Y'])
        links.new(modulo_pattern.outputs['Value'], combine_pattern.inputs['Z'])

        links.new(combine_pattern.outputs['Vector'], pattern_vector.inputs[1])

        # Применяем смещение
        set_position = nodes.new('GeometryNodeSetPosition')
        set_position.name = "Apply Offset Pattern"
        set_position.location = (1000, -100)
        links.new(grid_points, set_position.inputs['Geometry'])
        links.new(pattern_vector.outputs['Vector'], set_position.inputs['Offset'])

        return set_position.outputs['Geometry']

    def _apply_scale_falloff(self, base_nodes, instances_input):
        """
        Применение Scale Falloff - уменьшение размера к краям сетки

        Args:
            base_nodes: Словарь с базовыми нодами
            instances_input: Входной сокет с инстансами

        Returns:
            NodeSocket: Выход с примененным falloff
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']

        # Получаем позиции инстансов
        position_node = nodes.new('GeometryNodeInputPosition')
        position_node.location = (200, -400)

        # Разделяем координаты
        separate_xyz = nodes.new('ShaderNodeSeparateXYZ')
        separate_xyz.location = (300, -400)
        links.new(position_node.outputs['Position'], separate_xyz.inputs['Vector'])

        # Вычисляем размеры сетки
        # Total X = (Count X - 1) * Spacing X
        count_x_minus_one = nodes.new('ShaderNodeMath')
        count_x_minus_one.operation = 'SUBTRACT'
        count_x_minus_one.inputs[1].default_value = 1.0
        count_x_minus_one.location = (200, -500)
        links.new(group_input.outputs['Count X'], count_x_minus_one.inputs[0])

        separate_spacing = nodes.new('ShaderNodeSeparateXYZ')
        separate_spacing.location = (200, -550)
        links.new(group_input.outputs['Spacing'], separate_spacing.inputs['Vector'])

        total_x = nodes.new('ShaderNodeMath')
        total_x.operation = 'MULTIPLY'
        total_x.location = (350, -500)
        links.new(count_x_minus_one.outputs['Value'], total_x.inputs[0])
        links.new(separate_spacing.outputs['X'], total_x.inputs[1])

        # То же для Y
        count_y_minus_one = nodes.new('ShaderNodeMath')
        count_y_minus_one.operation = 'SUBTRACT'
        count_y_minus_one.inputs[1].default_value = 1.0
        count_y_minus_one.location = (200, -600)
        links.new(group_input.outputs['Count Y'], count_y_minus_one.inputs[0])

        total_y = nodes.new('ShaderNodeMath')
        total_y.operation = 'MULTIPLY'
        total_y.location = (350, -600)
        links.new(count_y_minus_one.outputs['Value'], total_y.inputs[0])
        links.new(separate_spacing.outputs['Y'], total_y.inputs[1])

        # Нормализуем позиции к диапазону [0, 1]
        # norm_x = abs(x) / (total_x / 2)
        abs_x = nodes.new('ShaderNodeMath')
        abs_x.operation = 'ABSOLUTE'
        abs_x.location = (400, -400)
        links.new(separate_xyz.outputs['X'], abs_x.inputs[0])

        half_total_x = nodes.new('ShaderNodeMath')
        half_total_x.operation = 'DIVIDE'
        half_total_x.inputs[1].default_value = 2.0
        half_total_x.location = (450, -500)
        links.new(total_x.outputs['Value'], half_total_x.inputs[0])

        norm_x = nodes.new('ShaderNodeMath')
        norm_x.operation = 'DIVIDE'
        norm_x.location = (500, -450)
        links.new(abs_x.outputs['Value'], norm_x.inputs[0])
        links.new(half_total_x.outputs['Value'], norm_x.inputs[1])

        # То же для Y
        abs_y = nodes.new('ShaderNodeMath')
        abs_y.operation = 'ABSOLUTE'
        abs_y.location = (400, -450)
        links.new(separate_xyz.outputs['Y'], abs_y.inputs[0])

        half_total_y = nodes.new('ShaderNodeMath')
        half_total_y.operation = 'DIVIDE'
        half_total_y.inputs[1].default_value = 2.0
        half_total_y.location = (450, -600)
        links.new(total_y.outputs['Value'], half_total_y.inputs[0])

        norm_y = nodes.new('ShaderNodeMath')
        norm_y.operation = 'DIVIDE'
        norm_y.location = (500, -550)
        links.new(abs_y.outputs['Value'], norm_y.inputs[0])
        links.new(half_total_y.outputs['Value'], norm_y.inputs[1])

        # Вычисляем максимальное расстояние от центра
        max_distance = nodes.new('ShaderNodeMath')
        max_distance.operation = 'MAXIMUM'
        max_distance.location = (600, -500)
        links.new(norm_x.outputs['Value'], max_distance.inputs[0])
        links.new(norm_y.outputs['Value'], max_distance.inputs[1])

        # Применяем falloff: scale = 1.0 - (distance * falloff_strength)
        falloff_mult = nodes.new('ShaderNodeMath')
        falloff_mult.operation = 'MULTIPLY'
        falloff_mult.location = (700, -500)
        links.new(max_distance.outputs['Value'], falloff_mult.inputs[0])
        links.new(group_input.outputs['Scale Falloff'], falloff_mult.inputs[1])

        scale_factor = nodes.new('ShaderNodeMath')
        scale_factor.operation = 'SUBTRACT'
        scale_factor.inputs[0].default_value = 1.0
        scale_factor.location = (800, -500)
        links.new(falloff_mult.outputs['Value'], scale_factor.inputs[1])

        # Ограничиваем минимальный масштаб
        clamp_scale = nodes.new('ShaderNodeMath')
        clamp_scale.operation = 'MAXIMUM'
        clamp_scale.inputs[1].default_value = 0.01  # Минимальный масштаб
        clamp_scale.location = (900, -500)
        links.new(scale_factor.outputs['Value'], clamp_scale.inputs[0])

        # Применяем масштабирование
        scale_instances = nodes.new('GeometryNodeScaleInstances')
        scale_instances.name = "Apply Scale Falloff"
        scale_instances.location = (1000, -400)
        links.new(instances_input, scale_instances.inputs['Instances'])
        links.new(clamp_scale.outputs['Value'], scale_instances.inputs['Scale'])

        return scale_instances.outputs['Instances']

    def _apply_rotation_variation(self, base_nodes, instances_input):
        """
        Применение Rotation Variation - случайная вариация поворота для каждого элемента

        Args:
            base_nodes: Словарь с базовыми нодами
            instances_input: Входной сокет с инстансами

        Returns:
            NodeSocket: Выход с примененной вариацией поворота
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']

        # Получаем индекс для уникального seed каждого элемента
        index_node = nodes.new('GeometryNodeInputIndex')
        index_node.location = (200, -700)

        # Создаем случайные значения для каждой оси поворота
        # Random X
        random_x = nodes.new('FunctionNodeRandomValue')
        random_x.data_type = 'FLOAT'
        random_x.location = (300, -650)

        # Random Y
        random_y = nodes.new('FunctionNodeRandomValue')
        random_y.data_type = 'FLOAT'
        random_y.location = (300, -700)

        # Random Z
        random_z = nodes.new('FunctionNodeRandomValue')
        random_z.data_type = 'FLOAT'
        random_z.location = (300, -750)

        # Разделяем Rotation Variation по компонентам
        separate_rotation_var = nodes.new('ShaderNodeSeparateXYZ')
        separate_rotation_var.location = (200, -800)
        links.new(group_input.outputs['Rotation Variation'], separate_rotation_var.inputs['Vector'])

        # Настраиваем диапазоны для каждой оси
        # X axis: -variation_x to +variation_x
        negate_x = nodes.new('ShaderNodeMath')
        negate_x.operation = 'MULTIPLY'
        negate_x.inputs[1].default_value = -1.0
        negate_x.location = (400, -620)
        links.new(separate_rotation_var.outputs['X'], negate_x.inputs[0])

        links.new(negate_x.outputs['Value'], random_x.inputs['Min'])
        links.new(separate_rotation_var.outputs['X'], random_x.inputs['Max'])

        # Уникальный seed для X = index + 1000
        seed_x = nodes.new('ShaderNodeMath')
        seed_x.operation = 'ADD'
        seed_x.inputs[1].default_value = 1000.0
        seed_x.location = (250, -620)
        links.new(index_node.outputs['Index'], seed_x.inputs[0])
        links.new(seed_x.outputs['Value'], random_x.inputs['Seed'])

        # Y axis: -variation_y to +variation_y
        negate_y = nodes.new('ShaderNodeMath')
        negate_y.operation = 'MULTIPLY'
        negate_y.inputs[1].default_value = -1.0
        negate_y.location = (400, -670)
        links.new(separate_rotation_var.outputs['Y'], negate_y.inputs[0])

        links.new(negate_y.outputs['Value'], random_y.inputs['Min'])
        links.new(separate_rotation_var.outputs['Y'], random_y.inputs['Max'])

        # Уникальный seed для Y = index + 2000
        seed_y = nodes.new('ShaderNodeMath')
        seed_y.operation = 'ADD'
        seed_y.inputs[1].default_value = 2000.0
        seed_y.location = (250, -670)
        links.new(index_node.outputs['Index'], seed_y.inputs[0])
        links.new(seed_y.outputs['Value'], random_y.inputs['Seed'])

        # Z axis: -variation_z to +variation_z
        negate_z = nodes.new('ShaderNodeMath')
        negate_z.operation = 'MULTIPLY'
        negate_z.inputs[1].default_value = -1.0
        negate_z.location = (400, -720)
        links.new(separate_rotation_var.outputs['Z'], negate_z.inputs[0])

        links.new(negate_z.outputs['Value'], random_z.inputs['Min'])
        links.new(separate_rotation_var.outputs['Z'], random_z.inputs['Max'])

        # Уникальный seed для Z = index + 3000
        seed_z = nodes.new('ShaderNodeMath')
        seed_z.operation = 'ADD'
        seed_z.inputs[1].default_value = 3000.0
        seed_z.location = (250, -720)
        links.new(index_node.outputs['Index'], seed_z.inputs[0])
        links.new(seed_z.outputs['Value'], random_z.inputs['Seed'])

        # Объединяем случайные значения в вектор поворота
        combine_rotation = nodes.new('ShaderNodeCombineXYZ')
        combine_rotation.location = (500, -700)
        links.new(random_x.outputs['Value'], combine_rotation.inputs['X'])
        links.new(random_y.outputs['Value'], combine_rotation.inputs['Y'])
        links.new(random_z.outputs['Value'], combine_rotation.inputs['Z'])

        # Применяем поворот к инстансам
        rotate_instances = nodes.new('GeometryNodeRotateInstances')
        rotate_instances.name = "Apply Rotation Variation"
        rotate_instances.location = (600, -650)
        links.new(instances_input, rotate_instances.inputs['Instances'])
        links.new(combine_rotation.outputs['Vector'], rotate_instances.inputs['Rotation'])

        return rotate_instances.outputs['Instances']
    
    def draw_ui(self, layout, props_owner, modifier=None):
        """
        Отрисовка UI для Grid Cloner

        Args:
            layout: UI layout
            props_owner: Объект со свойствами
            modifier: Модификатор (если есть)
        """
        if modifier:
            # Если есть модификатор - используем сокеты модификатора
            self._draw_modifier_ui(layout, modifier)
        else:
            # Если нет модификатора - используем настройки из props_owner
            self._draw_settings_ui(layout, props_owner)

    def _draw_modifier_ui(self, layout, modifier):
        """Отрисовка UI через сокеты модификатора"""
        from ...ui.utils.ui_helpers import display_socket_prop

        # Grid Settings
        box = layout.box()
        box.label(text="Grid Settings", icon='GRID')

        row = box.row(align=True)
        display_socket_prop(row, modifier, "Count X", text="X")
        display_socket_prop(row, modifier, "Count Y", text="Y")
        display_socket_prop(row, modifier, "Count Z", text="Z")

        display_socket_prop(box, modifier, "Spacing", text="Spacing")

        # 🆕 Advanced Grid Settings
        advanced_box = layout.box()
        advanced_box.label(text="Advanced Grid", icon='SETTINGS')

        display_socket_prop(advanced_box, modifier, "Offset Pattern", text="Offset Pattern")
        display_socket_prop(advanced_box, modifier, "Scale Falloff", text="Scale Falloff")
        display_socket_prop(advanced_box, modifier, "Rotation Variation", text="Rotation Variation")

        # Базовые группы параметров из BaseCloner
        self._draw_base_cloner_ui(layout, None, modifier)

    def _draw_settings_ui(self, layout, props_owner):
        """Отрисовка UI через настройки props_owner"""
        # Grid Settings
        box = layout.box()
        box.label(text="Grid Settings", icon='GRID')

        row = box.row(align=True)
        row.prop(props_owner, "grid_count_x", text="X")
        row.prop(props_owner, "grid_count_y", text="Y")
        row.prop(props_owner, "grid_count_z", text="Z")

        box.prop(props_owner, "grid_spacing", text="Spacing")

        # 🆕 Advanced Grid Settings
        advanced_box = layout.box()
        advanced_box.label(text="Advanced Grid", icon='SETTINGS')

        advanced_box.prop(props_owner, "grid_offset_pattern", text="Offset Pattern")
        advanced_box.prop(props_owner, "grid_scale_falloff", text="Scale Falloff")
        advanced_box.prop(props_owner, "grid_rotation_variation", text="Rotation Variation")

        # Базовые группы параметров из BaseCloner
        self._draw_base_cloner_ui(layout, props_owner, None)
    
    def _draw_base_cloner_ui(self, layout, props_owner, modifier):
        """Отрисовка базовых UI групп клонера"""
        from ...ui.utils.ui_helpers import display_socket_prop

        if modifier:
            # Instance Transform через сокеты модификатора
            box = layout.box()
            box.label(text="Instance Transform", icon='CON_TRANSFORM')

            display_socket_prop(box, modifier, "Instance Scale", text="Scale")
            display_socket_prop(box, modifier, "Instance Rotation", text="Rotation")

            # Randomization через сокеты модификатора
            box = layout.box()
            box.label(text="Randomization", icon='RNDCURVE')

            display_socket_prop(box, modifier, "Random Position", text="Position")
            display_socket_prop(box, modifier, "Random Rotation", text="Rotation")
            display_socket_prop(box, modifier, "Random Scale", text="Scale")
            display_socket_prop(box, modifier, "Random Seed", text="Seed")

            # Global Transform через сокеты модификатора
            box = layout.box()
            box.label(text="Global Transform", icon='WORLD_DATA')

            display_socket_prop(box, modifier, "Global Position", text="Position")
            display_socket_prop(box, modifier, "Global Rotation", text="Rotation")

        else:
            # Instance Transform через props_owner (если нет модификатора)
            box = layout.box()
            box.label(text="Instance Transform", icon='CON_TRANSFORM')

            if hasattr(props_owner, 'instance_scale'):
                box.prop(props_owner, "instance_scale", text="Scale")
            if hasattr(props_owner, 'instance_rotation'):
                box.prop(props_owner, "instance_rotation", text="Rotation")

            # Randomization через props_owner
            box = layout.box()
            box.label(text="Randomization", icon='RNDCURVE')

            if hasattr(props_owner, 'random_position'):
                box.prop(props_owner, "random_position", text="Position")
            if hasattr(props_owner, 'random_rotation'):
                box.prop(props_owner, "random_rotation", text="Rotation")
            if hasattr(props_owner, 'random_scale'):
                box.prop(props_owner, "random_scale", text="Scale")
            if hasattr(props_owner, 'random_seed'):
                box.prop(props_owner, "random_seed", text="Seed")
        
        if modifier:
            for socket in modifier.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name.startswith('Random') or socket.name == 'Random Seed':
                        box.prop(modifier, f'["{socket.identifier}"]', text=socket.name)
        
        # Global Transform
        box = layout.box()
        box.label(text="Global Transform", icon='OBJECT_ORIGIN')
        
        if modifier:
            for socket in modifier.node_group.interface.items_tree:
                if socket.item_type == 'SOCKET' and socket.in_out == 'INPUT':
                    if socket.name.startswith('Global'):
                        box.prop(modifier, f'["{socket.identifier}"]', text=socket.name)
    
    def get_default_parameters(self):
        """Параметры по умолчанию для Grid Cloner"""
        base_defaults = super().get_default_parameters()
        grid_defaults = {
            "count_x": 3,
            "count_y": 3,
            "count_z": 1,
            "spacing": (3.0, 3.0, 3.0),
            # 🆕 НОВЫЕ ПАРАМЕТРЫ:
            "offset_pattern": (0.0, 0.0, 0.0),
            "scale_falloff": 0.0,
            "rotation_variation": (0.0, 0.0, 0.0)
        }
        return {**base_defaults, **grid_defaults}
    
    def get_parameter_groups(self):
        """Группировка параметров для UI"""
        base_groups = super().get_cloner_parameter_groups()
        grid_groups = {
            "Grid Settings": ["Count X", "Count Y", "Count Z", "Spacing"],
            "Advanced Grid": ["Offset Pattern", "Scale Falloff", "Rotation Variation"]  # 🆕 Новая группа
        }
        return {**grid_groups, **base_groups}


# Экземпляр класса для использования в других модулях
grid_cloner = GridCloner()


# Функции register/unregister для совместимости с __init__.py
def register():
    """Регистрация Grid Cloner (в новой архитектуре не требуется)"""
    print("✅ Grid Cloner: Using class-based architecture, no registration needed")
    pass


def unregister():
    """Отмена регистрации Grid Cloner (в новой архитектуре не требуется)"""
    print("✅ Grid Cloner: Using class-based architecture, no unregistration needed") 
    pass


