# ClonerPro Developer Guide

Руководство по разработке и расширению ClonerPro addon для Blender.

## 📋 Содержание

- [Архитектура](#архитектура)
- [Добавление нового клонера](#добавление-нового-клонера)
- [Добавление нового эффектора](#добавление-нового-эффектора)
- [Добавление нового поля](#добавление-нового-поля)
- [Общие принципы](#общие-принципы)
- [Тестирование](#тестирование)
- [Отладка](#отладка)

## 🏗️ Архитектура

ClonerPro построен на модульной архитектуре с четким разделением компонентов:

```
ClonerPro/
├── components/          # Основные компоненты
│   ├── cloners/        # Клонеры (Grid, Linear, Circle, etc.)
│   ├── effectors/      # Эффекторы (Random, Noise, Step, etc.)
│   ├── fields/         # Поля (Sphere, Box, etc.)
│   ├── base_cloner.py  # Базовый класс клонеров
│   ├── base_effector.py # Базовый класс эффекторов
│   └── base_field.py   # Базовый класс полей
├── core/               # Ядро системы
│   ├── registry/       # Реестры компонентов
│   │   ├── cloner_registry.py
│   │   ├── effector_registry.py
│   │   └── field_registry.py
│   └── services/       # Сервисы (синхронизация, etc.)
├── ui/                 # Пользовательский интерфейс
│   ├── panels/         # Панели UI
│   ├── operators/      # Операторы Blender
│   └── generators/     # Генераторы UI
└── utils/              # Утилиты
```

### Базовые классы

- **BaseCloner** - базовый класс для всех клонеров
- **BaseEffector** - базовый класс для всех эффекторов  
- **BaseField** - базовый класс для всех полей

### Система реестров

Система реестров обеспечивает автоматическую регистрацию и управление компонентами:

- `effector_registry.py` - реестр эффекторов
- `field_registry.py` - реестр полей
- `cloner_registry.py` - реестр клонеров

**Ключевые особенности:**
- Автоматическое создание кнопок в UI
- Динамическая генерация операторов
- Унифицированная система создания компонентов
- Автоматическая регистрация UI свойств

## 🔧 Добавление нового клонера

### 1. Создание класса клонера

Создайте файл `components/cloners/my_cloner.py`:

```python
"""
My Custom Cloner - описание вашего клонера
"""

import bpy
from ..base_cloner import BaseCloner

class MyCloner(BaseCloner):
    """
    My Custom Cloner - краткое описание
    """
    
    bl_idname = "MY_CLONER"
    bl_label = "My Cloner"
    
    def __init__(self):
        super().__init__()
    
    def register_properties(self, props_owner):
        """Регистрация свойств клонера"""
        super().register_properties(props_owner)
        
        # Добавьте специфичные свойства
        props_owner.my_parameter = bpy.props.FloatProperty(
            name="My Parameter",
            description="Description of my parameter",
            default=1.0,
            min=0.0,
            max=10.0
        )
    
    def get_specific_sockets(self):
        """Специфичные сокеты для клонера"""
        return [
            ("My Parameter", "NodeSocketFloat", "INPUT", 1.0, 0.0, 10.0),
            # Добавьте другие сокеты
        ]
    
    def _create_cloner_logic(self, base_nodes):
        """Основная логика клонера"""
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Реализуйте логику клонирования
        # Верните выходной сокет с геометрией
        
        return output_socket
    
    def get_parameter_groups(self):
        """Группировка параметров для UI"""
        return {
            "Cloner Settings": [
                {"name": "My Parameter", "socket_name": "My Parameter"}
            ]
        }
    
    def draw_ui(self, layout, context, modifier):
        """Отрисовка UI клонера"""
        from ...ui.utils.ui_helpers import display_socket_prop
        
        # Получаем группы параметров
        parameter_groups = self.get_parameter_groups()
        
        # Отображаем каждую группу
        for group_name, params in parameter_groups.items():
            if params:
                box = layout.box()
                col = box.column(align=True)
                col.label(text=f"{group_name}:", icon='SETTINGS')
                col.separator()
                
                for param in params:
                    param_name = param.get('name', param.get('socket_name', ''))
                    socket_name = param.get('socket_name', param_name)
                    display_socket_prop(col, modifier, socket_name, text=param_name)

# Функция совместимости
def create_my_cloner(name_suffix=""):
    """Create My Cloner node group"""
    cloner = MyCloner()
    return cloner.create_cloner_node_group(name_suffix)
```

### 2. Регистрация клонера

Добавьте в `core/registry/cloner_registry.py`:

```python
from ...components.cloners.my_cloner import MyCloner

CLONER_REGISTRY = {
    # ... существующие клонеры
    "MY_CLONER": {
        'class': MyCloner,
        'creator': None,
        'display_name': 'My Cloner',
        'description': 'My custom cloner',
        'architecture': 'CLASS'
    }
}
```

### 3. Обновление импортов

Добавьте в `components/cloners/__init__.py`:

```python
from .my_cloner import MyCloner, create_my_cloner
```

**ВСЁ!** Кнопка появится автоматически в UI, создание будет работать через реестр.

## ⚡ Добавление нового эффектора

### 1. Создание класса эффектора

Создайте файл `components/effectors/my_effector.py`:

```python
"""
My Custom Effector - описание вашего эффектора
"""

import bpy
from ..base_effector import BaseEffector

class MyEffector(BaseEffector):
    """
    My Custom Effector - краткое описание
    """
    
    bl_idname = "MY_EFFECTOR"
    bl_label = "My Effector"
    
    def __init__(self):
        super().__init__()
    
    def register_properties(self, props_owner):
        """Регистрация свойств эффектора"""
        super().register_properties(props_owner)
        
        # Добавьте специфичные свойства
        props_owner.my_strength = bpy.props.FloatProperty(
            name="Strength",
            description="Effect strength",
            default=1.0,
            min=0.0,
            max=2.0
        )
    
    def get_specific_sockets(self):
        """Специфичные сокеты для эффектора"""
        return [
            ("Strength", "NodeSocketFloat", "INPUT", 1.0, 0.0, 2.0),
            # Добавьте другие сокеты
        ]
    
    def _create_effector_logic(self, base_nodes):
        """Основная логика эффектора"""
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Реализуйте логику эффектора
        # Верните выходные сокеты трансформаций
        
        return {
            'position': position_output,
            'rotation': rotation_output,
            'scale': scale_output
        }
    
    def get_parameter_groups(self):
        """Группировка параметров для UI"""
        return {
            "Effect Settings": [
                "Strength"
            ]
        }
    
    def draw_ui(self, layout, context, modifier):
        """Отрисовка UI эффектора"""
        from ...ui.utils.ui_helpers import display_socket_prop
        
        # Получаем группы параметров
        parameter_groups = self.get_parameter_groups()
        
        # Отображаем группы параметров
        for group_name, param_names in parameter_groups.items():
            if param_names:
                box = layout.box()
                col = box.column(align=True)
                
                # Заголовок группы с иконкой
                icon_map = {
                    "Effect Settings": "SETTINGS",
                    "Transform": "ORIENTATION_GIMBAL"
                }
                icon = icon_map.get(group_name, "DOT")
                col.label(text=f"{group_name}:", icon=icon)
                
                col.separator()
                for param_name in param_names:
                    display_socket_prop(col, modifier, param_name, text=param_name)

# Функция совместимости
def create_my_effector(name_suffix=""):
    """Create My Effector node group"""
    effector = MyEffector()
    return effector.create_effector_node_group(name_suffix)
```

### 2. Регистрация эффектора

Добавьте в `core/registry/effector_registry.py`:

```python
from ...components.effectors.my_effector import MyEffector

EFFECTOR_REGISTRY = {
    # ... существующие эффекторы
    "MY_EFFECTOR": {
        'class': MyEffector,
        'creator': None,
        'display_name': 'My Effector',
        'description': 'My custom effector',
        'affects': ['position', 'rotation', 'scale'],
        'architecture': 'CLASS'
    }
}
```

### 3. Обновление импортов

Добавьте в `components/effectors/__init__.py`:

```python
from .my_effector import MyEffector, create_my_effector
```

**ВСЁ!** Кнопка появится автоматически в UI, создание будет работать через реестр.

## 🌸 Добавление нового поля

### 1. Создание класса поля

Создайте файл `components/fields/my_field.py`:

```python
"""
My Custom Field - описание вашего поля
"""

import bpy
from ..base_field import BaseField

class MyField(BaseField):
    """
    My Custom Field - краткое описание
    """

    bl_idname = "MY_FIELD"
    bl_label = "My Field"

    def __init__(self):
        super().__init__()

    def register_properties(self, props_owner):
        """Регистрация свойств поля"""
        super().register_properties(props_owner)

        # Добавьте специфичные свойства
        props_owner.my_radius = bpy.props.FloatProperty(
            name="Radius",
            description="Field radius",
            default=5.0,
            min=0.1,
            max=100.0
        )

    def get_specific_sockets(self):
        """Специфичные сокеты для поля"""
        return [
            ("Center", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),
            ("Radius", "NodeSocketFloat", "INPUT", 5.0, 0.1, 100.0),
            ("Falloff", "NodeSocketFloat", "INPUT", 0.0, 0.0, 1.0),
        ]

    def _create_field_logic(self, base_nodes):
        """Основная логика поля (маски для эффекторов)"""
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']

        # Реализуйте логику поля (маски)
        # Поле должно возвращать силу от 0.0 до 1.0
        # 1.0 = полная сила эффектора, 0.0 = нет силы

        # Пример: простая сферическая маска
        position = nodes.new('GeometryNodeInputPosition')

        # Вычисляем расстояние от центра
        center_offset = nodes.new('ShaderNodeVectorMath')
        center_offset.operation = 'SUBTRACT'
        links.new(position.outputs[0], center_offset.inputs[0])
        links.new(group_input.outputs['Center'], center_offset.inputs[1])

        distance = nodes.new('ShaderNodeVectorMath')
        distance.operation = 'LENGTH'
        links.new(center_offset.outputs['Vector'], distance.inputs[0])

        # Map Range для создания маски
        map_range = nodes.new('ShaderNodeMapRange')
        map_range.clamp = True
        links.new(distance.outputs['Value'], map_range.inputs['Value'])
        links.new(group_input.outputs['Radius'], map_range.inputs['From Max'])
        map_range.inputs['From Min'].default_value = 0.0
        map_range.inputs['To Min'].default_value = 1.0  # Полная сила в центре
        map_range.inputs['To Max'].default_value = 0.0  # Нет силы на краю

        # Применяем Inner/Outer Strength
        map_range_final = nodes.new('ShaderNodeMapRange')
        map_range_final.clamp = True
        links.new(map_range.outputs['Result'], map_range_final.inputs['Value'])
        map_range_final.inputs['From Min'].default_value = 0.0
        map_range_final.inputs['From Max'].default_value = 1.0
        links.new(group_input.outputs['Outer Strength'], map_range_final.inputs['To Min'])
        links.new(group_input.outputs['Inner Strength'], map_range_final.inputs['To Max'])

        # Enable multiply
        enable_multiply = nodes.new('ShaderNodeMath')
        enable_multiply.operation = 'MULTIPLY'
        links.new(map_range_final.outputs['Result'], enable_multiply.inputs[0])
        links.new(group_input.outputs['Enable'], enable_multiply.inputs[1])

        return enable_multiply.outputs['Value']

    def create_field_node_group_for_effectors(self, name_suffix=""):
        """Создание node group для использования в эффекторах"""
        # Создаем упрощенную версию поля для встраивания в эффекторы
        group_name = f"MyField{name_suffix}"
        node_group = bpy.data.node_groups.new(type='GeometryNodeTree', name=group_name)

        # Настройка интерфейса
        node_group.interface.new_socket(name="Enable", in_out='INPUT', socket_type='NodeSocketBool')
        node_group.interface.new_socket(name="Center", in_out='INPUT', socket_type='NodeSocketVector')
        node_group.interface.new_socket(name="Radius", in_out='INPUT', socket_type='NodeSocketFloat')
        node_group.interface.new_socket(name="Falloff", in_out='INPUT', socket_type='NodeSocketFloat')
        node_group.interface.new_socket(name="Inner Strength", in_out='INPUT', socket_type='NodeSocketFloat')
        node_group.interface.new_socket(name="Outer Strength", in_out='INPUT', socket_type='NodeSocketFloat')
        node_group.interface.new_socket(name="Field", in_out='OUTPUT', socket_type='NodeSocketFloat')

        # Создаем базовые ноды
        nodes = node_group.nodes
        links = node_group.links

        group_input = nodes.new('NodeGroupInput')
        group_output = nodes.new('NodeGroupOutput')

        # Используем ту же логику что и в _create_field_logic
        base_nodes = {
            'nodes': nodes,
            'links': links,
            'group_input': group_input
        }

        field_output = self._create_field_logic(base_nodes)
        links.new(field_output, group_output.inputs['Field'])

        return node_group

    def get_parameter_groups(self):
        """Группировка параметров для UI"""
        return {
            "Field Settings": [
                "Enable",
                "Inner Strength"
            ],
            "Spatial Parameters": [
                "Center",
                "Radius"
            ],
            "Falloff Controls": [
                "Falloff",
                "Outer Strength"
            ]
        }

    def draw_ui(self, layout, context, modifier):
        """Отрисовка UI поля"""
        if not modifier:
            layout.label(text="No modifier found", icon='ERROR')
            return

        from ...ui.utils.ui_helpers import display_socket_prop

        # Получаем группы параметров
        parameter_groups = self.get_parameter_groups()

        # Отображаем каждую группу параметров
        for group_name, param_names in parameter_groups.items():
            if param_names:
                box = layout.box()
                col = box.column(align=True)
                col.label(text=f"{group_name}:", icon='SETTINGS')
                col.separator()

                for param_name in param_names:
                    display_socket_prop(col, modifier, param_name, text=param_name)

        # Добавляем секцию управления подключениями к эффекторам
        self.draw_field_connections(layout, modifier)

# Функция совместимости
def create_my_field(name_suffix=""):
    """Create My Field node group"""
    field = MyField()
    return field.create_field_node_group(name_suffix)
```

### 2. Регистрация поля

Добавьте в `core/registry/field_registry.py`:

```python
from ...components.fields.my_field import MyField

FIELD_REGISTRY = {
    # ... существующие поля
    "MY_FIELD": {
        'class': MyField,
        'creator': None,
        'display_name': 'My Field',
        'description': 'My custom field',
        'shape': 'custom',
        'architecture': 'CLASS'
    }
}
```

### 3. Обновление определения типов

Добавьте в `core/utils/field_detection.py`:

```python
def detect_field_type_from_name(name: str) -> str:
    if not name:
        return "SPHERE"

    name_lower = name.lower()

    # Проверяем по ключевым словам в имени
    if 'sphere' in name_lower:
        return "SPHERE"
    elif 'box' in name_lower:
        return "BOX"
    elif 'myfield' in name_lower:  # Добавить для нового поля
        return "MY_FIELD"
    # ... остальные типы

    return "SPHERE"  # Fallback
```

### 4. Обновление импортов

Добавьте в `components/fields/__init__.py`:

```python
from .my_field import MyField, create_my_field
```

**ВСЁ!** Система автоматически:
- Создаст кнопку в UI
- Определит тип поля по имени
- Создаст field node group для эффекторов
- Настроит синхронизацию параметров
- Добавит кнопки подключения к эффекторам

## 📋 Общие принципы

### Базовые классы

- **BaseCloner** - базовый класс для всех клонеров
- **BaseEffector** - базовый класс для всех эффекторов
- **BaseField** - базовый класс для всех полей

### Обязательные методы

Каждый компонент должен реализовать:

- `register_properties()` - регистрация свойств
- `get_specific_sockets()` - определение сокетов
- `_create_*_logic()` - основная логика компонента
- `get_parameter_groups()` - группировка параметров для UI
- `draw_ui()` - отрисовка пользовательского интерфейса

### Дополнительные методы для полей

Поля должны также реализовать:

- `create_field_node_group_for_effectors()` - создание упрощенной версии для встраивания в эффекторы
- `draw_field_connections()` - автоматически наследуется от BaseField

### Соглашения по именованию

- Классы: `MyComponentName` (PascalCase)
- Файлы: `my_component.py` (snake_case)
- Типы в реестре: `"MY_COMPONENT"` (UPPER_CASE)
- Свойства: `my_property` (snake_case)
- Display names: без слов "Effector", "Field", "Cloner" (они разделены по категориям)

### Структура node groups

Все компоненты создают node groups с единообразной структурой:

1. **Group Input** - входные параметры
2. **Logic nodes** - основная логика
3. **Group Output** - выходные данные

### UI Guidelines

- Используйте группировку параметров через `get_parameter_groups()`
- Добавляйте иконки для групп параметров
- Обеспечьте консистентность с существующими компонентами
- Используйте `display_socket_prop()` для отображения параметров
- Поля автоматически получают кнопки подключения к эффекторам

### Система реестров

Система реестров обеспечивает автоматическую интеграцию компонентов:

**Автоматические возможности:**
- Кнопки создания генерируются автоматически
- UI свойства регистрируются автоматически
- Операторы создания работают через реестр
- Синхронизация параметров работает автоматически
- Определение типов полей происходит автоматически

**Для добавления нового компонента:**
1. Создать класс компонента
2. Добавить в соответствующий реестр
3. Обновить импорты
4. Для полей: добавить определение типа в утилиту

**Система автоматически обеспечивает:**
- Появление кнопки в UI
- Создание компонента через оператор
- Регистрация UI свойств
- Подключение к эффекторам (для полей)
- Синхронизация параметров между полями и эффекторами

## 🧪 Тестирование

### Базовое тестирование

1. **Создание компонента** - убедитесь, что компонент создается без ошибок
2. **UI отображение** - проверьте корректность отображения параметров
3. **Функциональность** - протестируйте основную логику компонента
4. **Интеграция** - проверьте взаимодействие с другими компонентами

### Тестовые сценарии

```python
# Пример тестирования эффектора
def test_my_effector():
    # Создание эффектора
    effector = MyEffector()

    # Проверка создания node group
    node_group = effector.create_effector_node_group("Test")
    assert node_group is not None

    # Проверка параметров
    params = effector.get_parameter_groups()
    assert "Effect Settings" in params
```

### Тестирование полей

```python
# Пример тестирования поля
def test_my_field():
    # Создание поля
    field = MyField()

    # Проверка создания основного node group
    node_group = field.create_field_node_group("Test")
    assert node_group is not None

    # Проверка создания node group для эффекторов
    effector_node_group = field.create_field_node_group_for_effectors("Test")
    assert effector_node_group is not None

    # Проверка параметров
    params = field.get_parameter_groups()
    assert "Field Settings" in params
```

## 🐛 Отладка

### Логирование

Используйте print() для отладочной информации:

```python
print(f"✅ Created {component_type} successfully")
print(f"❌ Error creating {component_type}: {error}")
print(f"🔧 [REGISTRY] Creating {component_type} via registry")
```

### Общие проблемы

1. **Node group не создается** - проверьте правильность логики в `_create_*_logic()`
2. **UI не отображается** - убедитесь в корректности `get_parameter_groups()`
3. **Параметры не синхронизируются** - проверьте соответствие имен сокетов
4. **Кнопка не появляется** - проверьте регистрацию в реестре и импорты
5. **Поле не подключается к эффекторам** - убедитесь в реализации `create_field_node_group_for_effectors()`
6. **Новое поле не определяется** - добавьте определение типа в `core/utils/field_detection.py`

### Инструменты отладки

- **Blender Console** - для просмотра логов
- **Node Editor** - для проверки созданных node groups
- **Properties Panel** - для проверки UI компонентов
- **Modifier Stack** - для проверки созданных модификаторов

### Отладка полей

Для полей дополнительно проверьте:
- Создание двух типов node groups (основной и для эффекторов)
- Корректность логики маски (0.0 = нет силы, 1.0 = полная сила)
- Работу кнопок подключения к эффекторам
- Синхронизацию параметров между полем и эффекторами

## 📚 Дополнительные ресурсы

- [Blender Python API](https://docs.blender.org/api/current/)
- [Geometry Nodes Documentation](https://docs.blender.org/manual/en/latest/modeling/geometry_nodes/)
- [ClonerPro Examples](./examples/) - примеры реализации компонентов

---

**Примечание**: ClonerPro использует унифицированную классовую архитектуру с автоматической системой реестров. Добавление новых компонентов требует минимального количества кода благодаря централизованным утилитам и автоматической интеграции.
