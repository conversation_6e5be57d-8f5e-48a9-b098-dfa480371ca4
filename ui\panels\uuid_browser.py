"""
UUID-based Universal Cloner Browser для Blender UI
Предоставляет расширенное управление клонерами через UUID систему
"""

import bpy
from bpy.types import Panel, Operator
from bpy.props import StringProperty, BoolProperty

# Импорт UUID системы
try:
    from ...core.uuid.manager import BlenderClonerUUIDManager
    from ...core.uuid.chain_management import BlenderUUIDChainManager
    UUID_SYSTEM_AVAILABLE = True
except ImportError:
    UUID_SYSTEM_AVAILABLE = False
    print("⚠️ UUID UI System: Not available")

from ...core.core import get_cloner_modifier, get_cloner_info

# Глобальное хранилище состояний expand/collapse для цепочек
# Безопасно для использования во время отрисовки UI
_chain_expand_states = {}


class CLONERPRO_PT_UUID_Browser(Panel):
    """UUID-based Universal Cloner Browser для Blender"""
    bl_label = "Universal Cloner Browser (UUID)"
    bl_idname = "CLONERPRO_PT_uuid_browser"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'ClonerPro'
    bl_options = {'DEFAULT_CLOSED'}
    
    @classmethod
    def poll(cls, context):
        # Показываем панель только если UUID система доступна
        return UUID_SYSTEM_AVAILABLE
    
    def draw_header(self, context):
        layout = self.layout
        layout.label(text="", icon='LINKED')
    
    def draw(self, context):
        layout = self.layout
        
        if not UUID_SYSTEM_AVAILABLE:
            layout.label(text="UUID System not available", icon='ERROR')
            return
        
        # Сканируем клонеры с UUID поддержкой в Blender
        scan_result = BlenderClonerUUIDManager.scan_all_cloners_with_uuid()
        
        # Заголовок с статистикой
        self._draw_statistics_header(layout, scan_result)
        
        # Кнопки управления
        self._draw_management_buttons(layout)
        
        layout.separator()
        
        # Отображение цепочек
        if scan_result["chains_by_uuid"]:
            layout.label(text="Cloner Chains:", icon='LINKED')
            
            for chain_uuid, chain_cloners in scan_result["chains_by_uuid"].items():
                self._draw_chain_group(layout, chain_uuid, chain_cloners)
        
        # Одиночные клонеры
        single_cloners = self._get_single_cloners(scan_result)
        if single_cloners:
            layout.separator()
            layout.label(text="Single Cloners:", icon='DUPLICATE')
            
            for obj, modifier in single_cloners:
                self._draw_single_cloner(layout, obj, modifier)
        
        # Legacy клонеры (требуют миграции)
        if scan_result["legacy_cloners"]:
            self._draw_legacy_cloners_section(layout, scan_result["legacy_cloners"])
        
        # Orphaned клонеры (битые связи)
        if scan_result["orphaned_cloners"]:
            self._draw_orphaned_cloners_section(layout, scan_result["orphaned_cloners"])
    
    def _draw_statistics_header(self, layout, scan_result):
        """Отображение заголовка со статистикой"""
        row = layout.row()
        row.label(text=f"Chains: {len(scan_result['chains_by_uuid'])}")
        row.label(text=f"Total: {len(scan_result['cloners_by_uuid'])}")
        
        # Blender-specific информация
        if scan_result["session_uid_cloners"]:
            row = layout.row()
            row.label(text=f"Session UIDs: {len(scan_result['session_uid_cloners'])}")
    
    def _draw_management_buttons(self, layout):
        """Отображение кнопок управления"""
        row = layout.row()
        
        # Проверяем существование операторов перед вызовом
        try:
            row.operator("clonerpro.migrate_legacy_blender_cloners", text="Migrate Legacy")
        except:
            row.label(text="Migrate Legacy (N/A)")
            
        try:
            row.operator("clonerpro.repair_blender_uuid_chains", text="Repair Chains")
        except:
            row.label(text="Repair Chains (N/A)")
        
        # Дополнительные операторы
        row = layout.row()
        try:
            row.operator("clonerpro.scan_and_validate_uuids", text="Validate UUIDs")
        except:
            row.label(text="Validate UUIDs (N/A)")
            
        try:
            row.operator("clonerpro.clean_orphaned_uuids", text="Clean Orphaned")
        except:
            row.label(text="Clean Orphaned (N/A)")
    
    def _get_single_cloners(self, scan_result):
        """Получить одиночные клонеры"""
        return [
            (obj, mod) for uuid, (obj, mod) in scan_result["cloners_by_uuid"].items()
            if not mod.get("chain_uuid") or len(scan_result["chains_by_uuid"].get(mod.get("chain_uuid", ""), [])) <= 1
        ]
    
    def _draw_chain_group(self, layout, chain_uuid: str, chain_cloners):
        """Отображение группы цепочки клонеров в Blender"""
        global _chain_expand_states
        
        box = layout.box()
        row = box.row(align=True)
        
        # Информация о цепочке
        chain_name = self._get_chain_name(chain_cloners)
        
        # Кнопка expand/collapse
        expand_key = f"chain_{chain_uuid[:8]}"
        
        # Используем глобальное хранилище состояний
        is_expanded = _chain_expand_states.get(expand_key, True)
        icon = 'DOWNARROW_HLT' if is_expanded else 'RIGHTARROW'
        
        op = row.operator("clonerpro.toggle_chain_expanded", text="", icon=icon)
        op.chain_uuid = chain_uuid
        op.expand_key = expand_key
        
        # Название цепочки
        row.label(text=f"🔗 {chain_name}", icon='LINKED')
        row.label(text=f"({len(chain_cloners)})")
        
        # Кнопки управления цепочкой
        self._draw_chain_management_buttons(box, chain_uuid)
        
        # Отображение клонеров в цепочке (если развернуто)
        if is_expanded:
            for i, (obj, modifier) in enumerate(chain_cloners):
                self._draw_chain_cloner(box, obj, modifier, i)
    
    def _draw_chain_management_buttons(self, layout, chain_uuid):
        """Отображение кнопок управления цепочкой"""
        row = layout.row(align=True)
        
        # Выбрать всю цепочку
        try:
            op = row.operator("clonerpro.select_chain", text="", icon='RESTRICT_SELECT_OFF')
            if op:  # Проверяем, что оператор существует
                op.chain_uuid = chain_uuid
        except:
            # Оператор не зарегистрирован - игнорируем
            row.label(text="", icon='RESTRICT_SELECT_OFF')
        
        # Удалить цепочку
        try:
            op = row.operator("clonerpro.delete_chain", text="", icon='TRASH')
            if op:
                op.chain_uuid = chain_uuid
        except:
            row.label(text="", icon='TRASH')
        
        # Экспорт цепочки
        try:
            op = row.operator("clonerpro.export_chain", text="", icon='EXPORT')
            if op:
                op.chain_uuid = chain_uuid
        except:
            row.label(text="", icon='EXPORT')
        
        # Дублировать цепочку
        try:
            op = row.operator("clonerpro.duplicate_chain", text="", icon='DUPLICATE')
            if op:
                op.chain_uuid = chain_uuid
        except:
            row.label(text="", icon='DUPLICATE')
    
    def _draw_chain_cloner(self, layout, obj, modifier, sequence: int):
        """Отображение отдельного клонера в цепочке в Blender"""
        row = layout.row(align=True)
        
        # Отступ и индикатор позиции в цепочке
        row.label(text="  " + "→ " if sequence > 0 else "  ")
        
        # Иконка типа клонера
        cloner_type = modifier.get("cloner_type", "UNKNOWN")
        type_icons = {
            "GRID": "MESH_GRID",
            "LINEAR": "MESH_PLANE", 
            "CIRCLE": "MESH_CIRCLE",
            "OBJECT": "OBJECT_DATA",
            "SPIRAL": "FORCE_VORTEX"
        }
        icon = type_icons.get(cloner_type, "DUPLICATE")
        
        # Название клонера
        display_name = modifier.get("display_name", obj.name)
        row.label(text=display_name, icon=icon)
        
        # UUID информация (если есть место)
        cloner_uuid = modifier.get("cloner_uuid", "")
        if cloner_uuid:
            row.label(text=f"[{cloner_uuid[:8]}...]", icon='TIME')
        
        # Кнопки управления клонером
        sub_row = layout.row(align=True)
        sub_row.label(text="    ")  # Отступ
        
        # Выбрать объект в Blender
        op = sub_row.operator("clonerpro.select_object", text="", icon='RESTRICT_SELECT_OFF')
        op.object_name = obj.name
        
        # Показать свойства UUID
        op = sub_row.operator("clonerpro.show_uuid_properties", text="", icon='PROPERTIES')
        op.cloner_uuid = cloner_uuid
        
        # Удалить
        try:
            op = sub_row.operator("clonerpro.delete_cloner_uuid", text="", icon='X')
            if op:
                op.cloner_uuid = cloner_uuid
        except:
            sub_row.label(text="", icon='X')
        
        # Пользовательские заметки (если есть)
        user_notes = modifier.get("user_notes", "")
        if user_notes:
            note_row = layout.row()
            note_row.label(text=f"      💬 {user_notes}")
    
    def _draw_single_cloner(self, layout, obj, modifier):
        """Отображение одиночного клонера"""
        row = layout.row(align=True)
        
        cloner_type = modifier.get("cloner_type", "UNKNOWN")
        type_icons = {
            "GRID": "MESH_GRID",
            "LINEAR": "MESH_PLANE", 
            "CIRCLE": "MESH_CIRCLE",
            "OBJECT": "OBJECT_DATA",
            "SPIRAL": "FORCE_VORTEX"
        }
        icon = type_icons.get(cloner_type, "DUPLICATE")
        
        display_name = modifier.get("display_name", obj.name)
        row.label(text=display_name, icon=icon)
        
        # UUID информация
        cloner_uuid = modifier.get("cloner_uuid", "")
        if cloner_uuid:
            row.label(text=f"[{cloner_uuid[:8]}...]")
        
        # Кнопки управления
        op = row.operator("clonerpro.select_object", text="", icon='RESTRICT_SELECT_OFF')
        op.object_name = obj.name
        
        # Безопасный вызов оператора удаления
        try:
            op = row.operator("clonerpro.delete_cloner_uuid", text="", icon='X')
            if op:
                op.cloner_uuid = cloner_uuid
        except:
            row.label(text="", icon='X')
    
    def _draw_legacy_cloners_section(self, layout, legacy_cloners):
        """Отображение legacy клонеров"""
        layout.separator()
        box = layout.box()
        box.label(text="⚠️ Legacy Cloners (Need Migration)", icon='ERROR')
        
        for obj, modifier in legacy_cloners:
            row = box.row()
            row.label(text=f"  {obj.name}", icon='OBJECT_DATA')
            
            # Безопасный вызов оператора миграции
            try:
                op = row.operator("clonerpro.migrate_single_cloner", text="Migrate")
                if op:
                    op.object_name = obj.name
                    op.modifier_name = modifier.name
            except:
                row.label(text="Migrate (N/A)")
    
    def _draw_orphaned_cloners_section(self, layout, orphaned_cloners):
        """Отображение orphaned клонеров"""
        layout.separator()
        box = layout.box()
        box.label(text="🔧 Orphaned Cloners (Broken Links)", icon='UNLINKED')
        
        for obj, modifier in orphaned_cloners:
            row = box.row()
            row.label(text=f"  {obj.name}", icon='OBJECT_DATA')
            
            # Безопасный вызов оператора восстановления
            try:
                op = row.operator("clonerpro.repair_cloner_links", text="Repair")
                if op:
                    op.cloner_uuid = modifier["cloner_uuid"]
            except:
                row.label(text="Repair (N/A)")
    
    def _get_chain_name(self, chain_cloners) -> str:
        """Получить имя цепочки для Blender"""
        if not chain_cloners:
            return "Empty Chain"
        
        first_modifier = chain_cloners[0][1]
        chain_name = first_modifier.get("display_name", "")
        
        if not chain_name:
            # Генерируем имя на основе типов клонеров
            types = [mod.get("cloner_type", "?") for obj, mod in chain_cloners]
            if len(types) > 1:
                chain_name = f"{types[0]} → ... → {types[-1]}"
            else:
                chain_name = types[0] if types else "Unknown"
        
        return chain_name


# === ОПЕРАТОРЫ ===

class CLONERPRO_OT_Toggle_Chain_Expanded(Operator):
    """Переключить развернутость цепочки"""
    bl_idname = "clonerpro.toggle_chain_expanded"
    bl_label = "Toggle Chain Expanded"
    bl_description = "Toggle chain expansion in browser"
    bl_options = {'REGISTER'}
    
    chain_uuid: StringProperty(name="Chain UUID")
    expand_key: StringProperty(name="Expand Key")
    
    def execute(self, context):
        global _chain_expand_states
        
        # Переключаем состояние в глобальном словаре
        current_state = _chain_expand_states.get(self.expand_key, True)
        _chain_expand_states[self.expand_key] = not current_state
        
        # Обновляем UI
        for area in context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
        
        return {'FINISHED'}


class CLONERPRO_OT_Select_Object(Operator):
    """Выбрать объект в Blender"""
    bl_idname = "clonerpro.select_object"
    bl_label = "Select Object"
    bl_description = "Select object in Blender viewport"
    bl_options = {'REGISTER', 'UNDO'}
    
    object_name: StringProperty(name="Object Name")
    
    def execute(self, context):
        if self.object_name in bpy.data.objects:
            obj = bpy.data.objects[self.object_name]
            
            # Очищаем текущий выбор
            bpy.ops.object.select_all(action='DESELECT')
            
            # Выбираем объект
            obj.select_set(True)
            context.view_layer.objects.active = obj
            
            self.report({'INFO'}, f"Selected {self.object_name}")
        else:
            self.report({'ERROR'}, f"Object {self.object_name} not found")
        
        return {'FINISHED'}


class CLONERPRO_OT_Show_UUID_Properties(Operator):
    """Показать UUID свойства клонера"""
    bl_idname = "clonerpro.show_uuid_properties"
    bl_label = "Show UUID Properties"
    bl_description = "Show UUID properties of the cloner"
    bl_options = {'REGISTER'}
    
    cloner_uuid: StringProperty(name="Cloner UUID")
    
    def execute(self, context):
        if not UUID_SYSTEM_AVAILABLE:
            self.report({'ERROR'}, "UUID system not available")
            return {'CANCELLED'}
        
        obj, modifier = BlenderClonerUUIDManager.find_cloner_by_uuid(self.cloner_uuid)
        if not obj:
            self.report({'ERROR'}, f"Cloner with UUID {self.cloner_uuid} not found")
            return {'CANCELLED'}
        
        # Выбираем объект и показываем в Properties
        bpy.ops.object.select_all(action='DESELECT')
        obj.select_set(True)
        context.view_layer.objects.active = obj
        
        # Переключаемся на Properties панель (если возможно)
        for area in context.screen.areas:
            if area.type == 'PROPERTIES':
                for space in area.spaces:
                    if space.type == 'PROPERTIES':
                        space.context = 'MODIFIER'
                        break
                break
        
        self.report({'INFO'}, f"Showing properties for UUID cloner: {obj.name}")
        return {'FINISHED'}


# Регистрация классов
classes = [
    CLONERPRO_PT_UUID_Browser,
    CLONERPRO_OT_Toggle_Chain_Expanded,
    CLONERPRO_OT_Select_Object,
    CLONERPRO_OT_Show_UUID_Properties,
]

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    # Очищаем глобальное хранилище состояний
    global _chain_expand_states
    _chain_expand_states.clear()
    
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)