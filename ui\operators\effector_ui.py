"""
Effector UI Operators для ClonerPro
Операторы для управления эффекторами в UI
"""

import bpy
from bpy.types import Operator
from bpy.props import StringProperty
from ...ui.utils.ui_state import is_effector_expanded, set_effector_expanded


class CLONERPRO_OT_toggle_effector_expanded(Operator):
    """Переключить состояние expand/collapse эффектора"""
    bl_idname = "clonerpro.toggle_effector_expanded"
    bl_label = "Toggle Effector Expanded"
    bl_description = "Expand or collapse effector parameters"
    bl_options = {'REGISTER'}
    
    object_name: StringProperty(name="Object Name")
    modifier_name: StringProperty(name="Modifier Name")
    
    def execute(self, context):
        current_state = is_effector_expanded(context, self.object_name, self.modifier_name)
        new_state = not current_state
        
        set_effector_expanded(context, self.object_name, self.modifier_name, new_state)
        
        return {'FINISHED'}


class CLONERPRO_OT_toggle_effector_visibility(Operator):
    """Переключить видимость эффектора"""
    bl_idname = "clonerpro.toggle_effector_visibility"
    bl_label = "Toggle Effector Visibility"
    bl_description = "Toggle effector visibility in viewport"
    bl_options = {'REGISTER', 'UNDO'}
    
    object_name: StringProperty(name="Object Name")
    modifier_name: StringProperty(name="Modifier Name")
    
    def execute(self, context):
        obj = bpy.data.objects.get(self.object_name)
        if not obj:
            self.report({'ERROR'}, f"Object {self.object_name} not found")
            return {'CANCELLED'}
        
        modifier = obj.modifiers.get(self.modifier_name)
        if not modifier:
            self.report({'ERROR'}, f"Modifier {self.modifier_name} not found")
            return {'CANCELLED'}
        
        try:
            # Простое переключение видимости модификатора
            modifier.show_viewport = not modifier.show_viewport
            status = "visible" if modifier.show_viewport else "hidden"
            self.report({'INFO'}, f"Effector {self.modifier_name} is now {status}")
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Failed to toggle effector visibility: {e}")
            return {'CANCELLED'}


class CLONERPRO_OT_delete_effector(Operator):
    """Удалить эффектор"""
    bl_idname = "clonerpro.delete_effector"
    bl_label = "Delete Effector"
    bl_description = "Delete effector modifier"
    bl_options = {'REGISTER', 'UNDO'}
    
    object_name: StringProperty(name="Object Name")
    modifier_name: StringProperty(name="Modifier Name")
    
    def execute(self, context):
        obj = bpy.data.objects.get(self.object_name)
        if not obj:
            self.report({'ERROR'}, f"Object {self.object_name} not found")
            return {'CANCELLED'}
        
        modifier = obj.modifiers.get(self.modifier_name)
        if not modifier:
            self.report({'ERROR'}, f"Modifier {self.modifier_name} not found")
            return {'CANCELLED'}
        
        try:
            # Простое удаление модификатора - теперь это обычный Geometry Nodes modifier
            obj.modifiers.remove(modifier)
            self.report({'INFO'}, f"Deleted effector {self.modifier_name}")
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Failed to delete effector: {e}")
            return {'CANCELLED'}




class CLONERPRO_OT_cleanup_orphaned_effectors(Operator):
    """Cleanup orphaned effectors from all cloner chains"""
    bl_idname = "clonerpro.cleanup_orphaned_effectors"
    bl_label = "Cleanup Orphaned Effectors"
    bl_description = "Remove deleted effectors from all cloner chains"
    bl_options = {'REGISTER', 'UNDO'}

    @classmethod
    def poll(cls, context):
        return True

    def execute(self, context):
        try:
            from ...core.cleanup.effector_cleanup import cleanup_unused_effectors
            cleaned_count = cleanup_unused_effectors()
            
            if cleaned_count > 0:
                self.report({'INFO'}, f"Cleaned up {cleaned_count} orphaned effectors")
            else:
                self.report({'INFO'}, "No orphaned effectors found")
                
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Failed to cleanup orphaned effectors: {e}")
            return {'CANCELLED'}






def register():
    """Регистрация операторов эффекторов"""
    bpy.utils.register_class(CLONERPRO_OT_toggle_effector_expanded)
    bpy.utils.register_class(CLONERPRO_OT_toggle_effector_visibility)
    bpy.utils.register_class(CLONERPRO_OT_delete_effector)
    bpy.utils.register_class(CLONERPRO_OT_cleanup_orphaned_effectors)


def unregister():
    """Отмена регистрации операторов эффекторов"""
    bpy.utils.unregister_class(CLONERPRO_OT_cleanup_orphaned_effectors)
    bpy.utils.unregister_class(CLONERPRO_OT_delete_effector)
    bpy.utils.unregister_class(CLONERPRO_OT_toggle_effector_visibility)
    bpy.utils.unregister_class(CLONERPRO_OT_toggle_effector_expanded)