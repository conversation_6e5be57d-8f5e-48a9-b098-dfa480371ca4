"""
Anti-Recursion Operators для ClonerPro
Операторы для управления отдельными модификаторами Realize Instances
"""

import bpy
from bpy.types import Operator
from bpy.props import StringProperty


class CLONERPRO_OT_toggle_anti_recursion(Operator):
    """Переключить Anti-Recursion для активного объекта"""
    bl_idname = "clonerpro.toggle_anti_recursion"
    bl_label = "Toggle Anti-Recursion"
    bl_description = "Add or remove Realize Instances modifier for anti-recursion"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        obj = context.active_object
        if not obj:
            self.report({'ERROR'}, "No active object selected")
            return {'CANCELLED'}
        
        # Проверяем текущее состояние анти-рекурсии
        from ...core.templates.realize_instances_modifier import (
            has_realize_instances_modifier, 
            add_realize_instances_modifier_after_cloners,
            remove_realize_instances_modifier
        )
        
        has_anti_recursion = has_realize_instances_modifier(obj)
        
        if has_anti_recursion:
            # Удаляем модификатор Realize Instances
            success = remove_realize_instances_modifier(obj)
            if success:
                self.report({'INFO'}, f"Removed Anti-Recursion from {obj.name}")
                # Обновляем глобальную настройку
                context.scene.use_anti_recursion = False
            else:
                self.report({'ERROR'}, "Failed to remove Anti-Recursion modifier")
                return {'CANCELLED'}
        else:
            # Добавляем модификатор Realize Instances
            modifier = add_realize_instances_modifier_after_cloners(obj)
            if modifier:
                self.report({'INFO'}, f"Added Anti-Recursion to {obj.name}")
                # Обновляем глобальную настройку
                context.scene.use_anti_recursion = True
            else:
                self.report({'ERROR'}, "Failed to add Anti-Recursion modifier")
                return {'CANCELLED'}
        
        return {'FINISHED'}


class CLONERPRO_OT_add_anti_recursion_to_all(Operator):
    """Добавить Anti-Recursion ко всем клонерам в сцене"""
    bl_idname = "clonerpro.add_anti_recursion_to_all"
    bl_label = "Add Anti-Recursion to All Cloners"
    bl_description = "Add Realize Instances modifiers to all cloner objects in scene"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        from ...core.templates.realize_instances_modifier import (
            has_realize_instances_modifier,
            add_realize_instances_modifier_after_cloners
        )
        from ...core.services.cloner_scanner import scan_scene_cloners
        
        # Сканируем все клонеры в сцене
        all_cloners = scan_scene_cloners(context)
        
        added_count = 0
        
        for cloner_info in all_cloners:
            obj = cloner_info.get('object')
            if obj and not has_realize_instances_modifier(obj):
                modifier = add_realize_instances_modifier_after_cloners(obj)
                if modifier:
                    added_count += 1
        
        if added_count > 0:
            self.report({'INFO'}, f"Added Anti-Recursion to {added_count} cloner objects")
            context.scene.use_anti_recursion = True
        else:
            self.report({'INFO'}, "All cloners already have Anti-Recursion or no cloners found")
        
        return {'FINISHED'}


class CLONERPRO_OT_remove_anti_recursion_from_all(Operator):
    """Удалить Anti-Recursion со всех клонеров в сцене"""
    bl_idname = "clonerpro.remove_anti_recursion_from_all"
    bl_label = "Remove Anti-Recursion from All Cloners"
    bl_description = "Remove Realize Instances modifiers from all cloner objects in scene"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        from ...core.templates.realize_instances_modifier import (
            has_realize_instances_modifier,
            remove_realize_instances_modifier
        )
        from ...core.services.cloner_scanner import scan_scene_cloners
        
        # Сканируем все клонеры в сцене
        all_cloners = scan_scene_cloners(context)
        
        removed_count = 0
        
        for cloner_info in all_cloners:
            obj = cloner_info.get('object')
            if obj and has_realize_instances_modifier(obj):
                success = remove_realize_instances_modifier(obj)
                if success:
                    removed_count += 1
        
        if removed_count > 0:
            self.report({'INFO'}, f"Removed Anti-Recursion from {removed_count} cloner objects")
            context.scene.use_anti_recursion = False
        else:
            self.report({'INFO'}, "No cloners with Anti-Recursion found")
        
        return {'FINISHED'}


class CLONERPRO_OT_update_anti_recursion_for_scene(Operator):
    """Обновить состояние Anti-Recursion для всей сцены на основе глобальной настройки"""
    bl_idname = "clonerpro.update_anti_recursion_for_scene"
    bl_label = "Update Anti-Recursion for Scene"
    bl_description = "Update all cloners in scene to match global Anti-Recursion setting"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        use_anti_recursion = context.scene.use_anti_recursion
        
        if use_anti_recursion:
            # Включаем анти-рекурсию для всех клонеров
            bpy.ops.clonerpro.add_anti_recursion_to_all()
        else:
            # Отключаем анти-рекурсию для всех клонеров
            bpy.ops.clonerpro.remove_anti_recursion_from_all()
        
        return {'FINISHED'}


def register():
    """Регистрация операторов анти-рекурсии"""
    bpy.utils.register_class(CLONERPRO_OT_toggle_anti_recursion)
    bpy.utils.register_class(CLONERPRO_OT_add_anti_recursion_to_all)
    bpy.utils.register_class(CLONERPRO_OT_remove_anti_recursion_from_all)
    bpy.utils.register_class(CLONERPRO_OT_update_anti_recursion_for_scene)


def unregister():
    """Отмена регистрации операторов анти-рекурсии"""
    bpy.utils.unregister_class(CLONERPRO_OT_update_anti_recursion_for_scene)
    bpy.utils.unregister_class(CLONERPRO_OT_remove_anti_recursion_from_all)
    bpy.utils.unregister_class(CLONERPRO_OT_add_anti_recursion_to_all)
    bpy.utils.unregister_class(CLONERPRO_OT_toggle_anti_recursion)