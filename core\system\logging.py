"""
ClonerPro Logging System
Centralized logging to replace debug prints
"""

import logging
import sys
from pathlib import Path


class ClonerProLogger:
    """Centralized logger for ClonerPro addon"""
    
    _instance = None
    _logger = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._logger is None:
            self._setup_logger()
    
    def _setup_logger(self):
        """Setup the logger with appropriate handlers"""
        self._logger = logging.getLogger('clonerpro')
        self._logger.setLevel(logging.DEBUG)
        
        # Prevent duplicate handlers
        if self._logger.handlers:
            return
        
        # Console handler for development
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '[ClonerPro] %(levelname)s: %(message)s'
        )
        console_handler.setFormatter(formatter)
        
        self._logger.addHandler(console_handler)
    
    @property
    def logger(self):
        return self._logger


# Global logger instance
_cloner_logger = ClonerProLogger()
logger = _cloner_logger.logger


# Convenience functions for common logging patterns
def log_info(message, component=""):
    """Log info message with optional component prefix"""
    prefix = f"[{component}] " if component else ""
    logger.info(f"{prefix}{message}")


def log_debug(message, component=""):
    """Log debug message with optional component prefix"""
    prefix = f"[{component}] " if component else ""
    logger.debug(f"{prefix}{message}")


def log_warning(message, component=""):
    """Log warning message with optional component prefix"""
    prefix = f"[{component}] " if component else ""
    logger.warning(f"{prefix}{message}")


def log_error(message, component="", exception=None):
    """Log error message with optional component prefix and exception"""
    prefix = f"[{component}] " if component else ""
    full_message = f"{prefix}{message}"
    if exception:
        full_message += f" - {str(exception)}"
    logger.error(full_message)


def log_success(message, component=""):
    """Log success message (as info with checkmark)"""
    prefix = f"[{component}] " if component else ""
    logger.info(f"{prefix}✅ {message}")


def log_operation_start(operation, component=""):
    """Log start of operation"""
    prefix = f"[{component}] " if component else ""
    logger.info(f"{prefix}🔄 Starting: {operation}")


def log_operation_complete(operation, component=""):
    """Log completion of operation"""
    prefix = f"[{component}] " if component else ""
    logger.info(f"{prefix}✅ Completed: {operation}")


# Set logging level based on development mode
def set_development_mode(enabled=True):
    """Enable/disable development mode with more verbose logging"""
    level = logging.DEBUG if enabled else logging.INFO
    logger.setLevel(level)
    for handler in logger.handlers:
        handler.setLevel(level)


# Initialize with production settings by default
set_development_mode(False)