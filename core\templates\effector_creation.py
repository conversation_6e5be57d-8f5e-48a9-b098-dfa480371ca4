"""
Effector Creation Template для ClonerPro
Единая система создания эффекторов через классовую архитектуру
"""

import bpy
from ..registry.effector_registry import (
    create_effector_via_registry,
    get_effector_class,
    is_class_based_effector,
    get_effector_info
)
from ..system.error_handling import component_creation_safe
from ..system.validation import validate_operation_context


def create_effector_via_class_system(effector_type, name_suffix="", **kwargs):
    """
    Создание эффектора через новую классовую систему
    
    Args:
        effector_type: Тип эффектора (например, "RANDOM_CLASS")
        name_suffix: Суффикс для имени
        **kwargs: Дополнительные параметры
        
    Returns:
        bpy.types.NodeGroup: Созданная группа нодов или None
    """
    print(f"🎨 [EFFECTOR] Creating {effector_type} effector via class system")
    
    # Проверяем, что это классовый эффектор
    if not is_class_based_effector(effector_type):
        print(f"[ERROR] {effector_type} is not a class-based effector")
        return None
    
    # Получаем класс эффектора
    effector_class = get_effector_class(effector_type)
    if not effector_class:
        print(f"[ERROR] No class found for effector type: {effector_type}")
        return None
    
    try:
        # Создаем экземпляр эффектора
        effector_instance = effector_class()
        
        # Создаем node group
        node_group = effector_instance.create_node_group(name_suffix)
        
        if node_group:
            print(f"✅ [EFFECTOR] Successfully created {effector_type} effector: {node_group.name}")
        else:
            print(f"[ERROR] Failed to create node group for {effector_type}")
        
        return node_group
        
    except Exception as e:
        print(f"[ERROR] Exception creating {effector_type} effector: {e}")
        return None


def create_effector_unified(effector_type, name_suffix="", use_class_system=True, **kwargs):
    """
    Единая функция создания эффекторов (поддерживает и классы, и функции)
    
    Args:
        effector_type: Тип эффектора
        name_suffix: Суффикс для имени
        use_class_system: Предпочитать классовую систему если доступна
        **kwargs: Дополнительные параметры
        
    Returns:
        bpy.types.NodeGroup: Созданная группа нодов или None
    """
    print(f"🔧 [EFFECTOR] Creating {effector_type} effector (unified system)")
    
    # Если запрошена классовая система и эффектор поддерживает её
    if use_class_system and is_class_based_effector(effector_type):
        return create_effector_via_class_system(effector_type, name_suffix, **kwargs)
    
    # Иначе используем реестр (поддерживает и классы, и функции)
    return create_effector_via_registry(effector_type, name_suffix)


@component_creation_safe
def safe_create_effector(effector_type, name_suffix="", **kwargs):
    """
    Безопасное создание эффектора с обработкой ошибок
    
    Args:
        effector_type: Тип эффектора
        name_suffix: Суффикс для имени
        **kwargs: Дополнительные параметры
        
    Returns:
        bpy.types.NodeGroup: Созданная группа нодов или None
    """
    # Валидация контекста
    if not validate_operation_context():
        return None
    
    return create_effector_unified(effector_type, name_suffix, **kwargs)


def get_available_effector_types(class_based_only=False):
    """
    Получить доступные типы эффекторов
    
    Args:
        class_based_only: Только классовые эффекторы
        
    Returns:
        list: Список доступных типов эффекторов
    """
    from ..registry.effector_registry import (
        get_class_based_effector_types,
        get_all_effector_types
    )
    
    if class_based_only:
        return get_class_based_effector_types()
    else:
        return get_all_effector_types()


def create_random_effector_class_based(name_suffix=""):
    """
    Создание Random Effector через классовую систему
    Convenience function для быстрого доступа
    """
    return create_effector_via_class_system("RANDOM_CLASS", name_suffix)


def compare_effector_implementations(effector_base_type, name_suffix=""):
    """
    Сравнение классовой и функциональной реализации эффектора
    Полезно для тестирования и отладки
    
    Args:
        effector_base_type: Базовый тип эффектора (например, "RANDOM")
        name_suffix: Суффикс для имени
        
    Returns:
        dict: Результаты создания обеих реализаций
    """
    results = {
        'function_based': None,
        'class_based': None,
        'comparison': {}
    }
    
    # Создаем функциональную версию
    function_type = effector_base_type
    if function_type in get_available_effector_types():
        results['function_based'] = create_effector_via_registry(
            function_type, f"{name_suffix}_func"
        )
    
    # Создаем классовую версию
    class_type = f"{effector_base_type}_CLASS"
    if class_type in get_available_effector_types():
        results['class_based'] = create_effector_via_class_system(
            class_type, f"{name_suffix}_class"
        )
    
    # Базовое сравнение
    results['comparison'] = {
        'both_created': results['function_based'] is not None and results['class_based'] is not None,
        'function_name': results['function_based'].name if results['function_based'] else None,
        'class_name': results['class_based'].name if results['class_based'] else None,
    }
    
    return results


def register_effector_properties_for_class(effector_class, props_owner):
    """
    Регистрация свойств эффектора через класс
    
    Args:
        effector_class: Класс эффектора
        props_owner: Объект для регистрации свойств
    """
    if effector_class:
        try:
            effector_instance = effector_class()
            effector_instance.register_properties(props_owner)
            print(f"✅ [EFFECTOR] Registered properties for {effector_class.__name__}")
        except Exception as e:
            print(f"[ERROR] Failed to register properties for {effector_class.__name__}: {e}")


def get_effector_parameter_groups_for_class(effector_class):
    """
    Получить группы параметров для эффектора через класс
    
    Args:
        effector_class: Класс эффектора
        
    Returns:
        dict: Группы параметров
    """
    if effector_class:
        try:
            effector_instance = effector_class()
            return effector_instance.get_parameter_groups()
        except Exception as e:
            print(f"[ERROR] Failed to get parameter groups for {effector_class.__name__}: {e}")
    
    return {}


def validate_effector_class(effector_class):
    """
    Валидация класса эффектора
    
    Args:
        effector_class: Класс эффектора для проверки
        
    Returns:
        tuple: (is_valid: bool, error_message: str)
    """
    if not effector_class:
        return False, "Effector class is None"
    
    try:
        # Проверяем, что это подкласс BaseEffector
        from ...components.base_effector import BaseEffector
        if not issubclass(effector_class, BaseEffector):
            return False, f"{effector_class.__name__} is not a subclass of BaseEffector"
        
        # Проверяем обязательные атрибуты
        required_attrs = ['bl_idname', 'bl_label', 'component_type']
        for attr in required_attrs:
            if not hasattr(effector_class, attr):
                return False, f"Missing required attribute: {attr}"
        
        # Проверяем, что можно создать экземпляр
        effector_instance = effector_class()
        
        # Проверяем обязательные методы
        required_methods = ['_create_effector_logic', 'get_specific_sockets']
        for method in required_methods:
            if not hasattr(effector_instance, method):
                return False, f"Missing required method: {method}"
        
        return True, ""
        
    except Exception as e:
        return False, f"Exception during validation: {e}"
