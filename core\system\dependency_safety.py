"""
Система защиты от крашей dependency graph для ClonerPro
Современная система без старых механизмов линковки эффекторов
"""

import bpy
import traceback
from typing import Optional, List, Dict, Set, Tuple, Any


# ======================================================================
# ОСНОВНЫЕ ФУНКЦИИ БЕЗОПАСНОСТИ
# ======================================================================

def is_cloner_modifier(mod: bpy.types.Modifier) -> bool:
    """Определяет, является ли модификатор клонером."""
    if mod.type != 'NODES' or not mod.node_group:
        return False
    
    # Проверяем метаданные
    component_type = mod.node_group.get("component_type")
    if component_type == 'CLONER':
        return True
    
    # Альтернативная проверка по имени
    node_group_name = mod.node_group.name.lower()
    cloner_keywords = ['cloner', 'grid', 'linear', 'circle', 'spiral']
    return any(keyword in node_group_name for keyword in cloner_keywords)


def is_effector_modifier(mod: bpy.types.Modifier) -> bool:
    """Определяет, является ли модификатор эффектором."""
    if mod.type != 'NODES' or not mod.node_group:
        return False
    
    # Проверяем метаданные
    component_type = mod.node_group.get("component_type")
    if component_type == 'EFFECTOR':
        return True
    
    # Альтернативная проверка по имени
    node_group_name = mod.node_group.name.lower()
    effector_keywords = ['effector', 'random', 'noise']
    return any(keyword in node_group_name for keyword in effector_keywords)


def is_safe_to_perform_effector_operations() -> bool:
    """
    Проверяет, безопасно ли выполнять операции с эффекторами
    """
    try:
        # Проверяем основные условия безопасности
        if not bpy.context:
            return False
        
        if not bpy.context.scene:
            return False
        
        # Проверяем, что не происходит операция undo/redo
        if bpy.context.window_manager.get("clonerpro_undo_in_progress", False):
            print("[SAFETY] Операции заблокированы - происходит undo/redo")
            return False
        
        return True
        
    except Exception as e:
        print(f"[SAFETY ERROR] Ошибка проверки безопасности: {e}")
        return False


def safe_execute_effector_operation(operation_func, *args, **kwargs):
    """
    Безопасно выполняет операцию с эффектором
    """
    try:
        if not is_safe_to_perform_effector_operations():
            return False, "Операции с эффекторами заблокированы для безопасности"
        
        # Выполняем операцию
        result = operation_func(*args, **kwargs)
        return True, result
        
    except Exception as e:
        error_msg = f"Ошибка выполнения операции с эффектором: {e}"
        print(f"[SAFETY ERROR] {error_msg}")
        traceback.print_exc()
        return False, error_msg


def complete_effector_memory_cleanup(effector_name: str):
    """
    Полная очистка памяти после удаления эффектора
    """
    try:
        print(f"[MEMORY CLEANUP] Очистка памяти для {effector_name}")
        
        # Принудительное обновление dependency graph
        if bpy.context.view_layer:
            bpy.context.view_layer.update()
        
        # Очистка кэшей
        if hasattr(bpy.context, 'evaluated_depsgraph_get'):
            try:
                depsgraph = bpy.context.evaluated_depsgraph_get()
                depsgraph.update()
            except Exception as e:
                print(f"[MEMORY CLEANUP] Ошибка обновления depsgraph: {e}")
        
        print(f"[MEMORY CLEANUP] ✅ Память очищена для {effector_name}")
        
    except Exception as e:
        print(f"[MEMORY CLEANUP] ❌ Ошибка очистки памяти: {e}")


def emergency_system_reset():
    """
    Экстренный сброс системы безопасности
    """
    try:
        print("[EMERGENCY] Экстренный сброс системы безопасности")
        
        # Сбрасываем флаги блокировки
        if bpy.context.window_manager:
            wm = bpy.context.window_manager
            wm["clonerpro_undo_in_progress"] = False
            wm["clonerpro_operations_blocked"] = False
        
        # Принудительное обновление
        if bpy.context.view_layer:
            bpy.context.view_layer.update()
        
        print("[EMERGENCY] ✅ Экстренный сброс завершен")
        
    except Exception as e:
        print(f"[EMERGENCY] ❌ Ошибка экстренного сброса: {e}")


def emergency_reset_all_locks():
    """
    Экстренный сброс всех блокировок безопасности
    """
    try:
        print("[EMERGENCY RESET] Сброс всех блокировок")
        
        if bpy.context.window_manager:
            wm = bpy.context.window_manager
            # Очищаем все флаги блокировки
            for key in list(wm.keys()):
                if key.startswith("clonerpro_"):
                    del wm[key]
        
        print("[EMERGENCY RESET] ✅ Все блокировки сброшены")
        
    except Exception as e:
        print(f"[EMERGENCY RESET] ❌ Ошибка сброса блокировок: {e}")


def diagnose_effector_cloner_relationships():
    """
    Диагностика современной системы эффекторов и клонеров
    """
    try:
        print("=== Диагностика системы эффекторов и клонеров ===")
        
        relationships_found = 0
        problems_detected = 0
        
        for obj in bpy.context.scene.objects:
            if not obj.modifiers:
                continue
            
            cloners_count = 0
            effectors_count = 0
            
            for mod in obj.modifiers:
                if mod.type == 'NODES' and mod.node_group:
                    if is_cloner_modifier(mod):
                        cloners_count += 1
                    elif is_effector_modifier(mod):
                        effectors_count += 1
            
            if cloners_count > 0 or effectors_count > 0:
                print(f"Объект {obj.name}: {cloners_count} клонеров, {effectors_count} эффекторов")
                relationships_found += cloners_count + effectors_count
        
        return {
            "relationships_found": relationships_found,
            "problems_detected": problems_detected
        }
        
    except Exception as e:
        print(f"[DIAGNOSIS ERROR] Ошибка диагностики: {e}")
        return {"relationships_found": 0, "problems_detected": 1}


def repair_all_effector_cloner_relationships():
    """
    Восстановление современной системы эффекторов и клонеров
    """
    try:
        print("=== Восстановление системы эффекторов и клонеров ===")
        
        # Современная система не требует восстановления связей
        # Эффекторы работают независимо через модификаторы
        
        for obj in bpy.context.scene.objects:
            if not obj.modifiers:
                continue
            
            for mod in obj.modifiers:
                if mod.type == 'NODES' and mod.node_group:
                    if is_effector_modifier(mod):
                        # Проверяем, что эффектор корректно настроен
                        if not mod.show_viewport:
                            print(f"Найден отключенный эффектор: {mod.name}")
        
        print("✅ Система работает корректно")
        return True
        
    except Exception as e:
        print(f"[REPAIR ERROR] Ошибка восстановления: {e}")
        return False


# ======================================================================
# ОБРАБОТЧИКИ СОБЫТИЙ
# ======================================================================

def undo_pre_safety_handler(scene):
    """Обработчик перед операцией undo"""
    try:
        if bpy.context.window_manager:
            bpy.context.window_manager["clonerpro_undo_in_progress"] = True
    except Exception as e:
        print(f"[UNDO PRE] Ошибка: {e}")


def undo_post_safety_handler(scene):
    """Обработчик после операции undo"""
    try:
        if bpy.context.window_manager:
            bpy.context.window_manager["clonerpro_undo_in_progress"] = False
    except Exception as e:
        print(f"[UNDO POST] Ошибка: {e}")


def depsgraph_update_post_handler(scene, depsgraph):
    """Обработчик обновления dependency graph"""
    try:
        # Минимальная обработка для предотвращения крашей
        pass
    except Exception as e:
        print(f"[DEPSGRAPH] Ошибка: {e}")


def on_load_post_handler(dummy):
    """Обработчик после загрузки файла"""
    try:
        # Сбрасываем все блокировки после загрузки
        emergency_reset_all_locks()
    except Exception as e:
        print(f"[LOAD POST] Ошибка: {e}")


def register_dependency_safety_handlers():
    """Регистрация обработчиков безопасности"""
    try:
        # Удаляем старые обработчики
        unregister_dependency_safety_handlers()
        
        # Регистрируем новые
        bpy.app.handlers.undo_pre.append(undo_pre_safety_handler)
        bpy.app.handlers.undo_post.append(undo_post_safety_handler)
        bpy.app.handlers.depsgraph_update_post.append(depsgraph_update_post_handler)
        bpy.app.handlers.load_post.append(on_load_post_handler)
        
        print("✅ Обработчики безопасности зарегистрированы")
        
    except Exception as e:
        print(f"[HANDLERS] Ошибка регистрации: {e}")


def unregister_dependency_safety_handlers():
    """Отмена регистрации обработчиков безопасности"""
    try:
        # Удаляем все наши обработчики
        handlers_to_remove = [
            (bpy.app.handlers.undo_pre, undo_pre_safety_handler),
            (bpy.app.handlers.undo_post, undo_post_safety_handler),
            (bpy.app.handlers.depsgraph_update_post, depsgraph_update_post_handler),
            (bpy.app.handlers.load_post, on_load_post_handler)
        ]
        
        for handler_list, handler_func in handlers_to_remove:
            while handler_func in handler_list:
                handler_list.remove(handler_func)
        
        print("✅ Обработчики безопасности удалены")
        
    except Exception as e:
        print(f"[HANDLERS] Ошибка удаления: {e}")


# ======================================================================
# ЭКСПОРТ ФУНКЦИЙ
# ======================================================================

__all__ = [
    # Основные функции безопасности
    'is_cloner_modifier',
    'is_effector_modifier',
    'is_safe_to_perform_effector_operations',
    'safe_execute_effector_operation',
    'complete_effector_memory_cleanup',
    
    # Экстренные функции
    'emergency_system_reset',
    'emergency_reset_all_locks',
    
    # Диагностика и восстановление
    'diagnose_effector_cloner_relationships',
    'repair_all_effector_cloner_relationships',
    
    # Обработчики событий
    'undo_pre_safety_handler',
    'undo_post_safety_handler',
    'depsgraph_update_post_handler',
    'on_load_post_handler',
    'register_dependency_safety_handlers',
    'unregister_dependency_safety_handlers'
]