"""
Unified Field Registry для ClonerPro
Единый реестр всех полей в новой классовой архитектуре
"""

# Импорты классов полей
from ...components.fields.sphere import SphereField
from ...components.fields.box import BoxField

# Единый реестр полей - поддерживает как классы, так и функции
FIELD_REGISTRY = {
    # Классовые поля (новая архитектура)
    "SPHERE": {
        'class': SphereField,
        'creator': None,  # Будет создан через класс
        'display_name': 'Sphere',
        'description': 'Creates spherical field for spatial control',
        'shape': 'sphere',
        'architecture': 'CLASS'
    },
    "BOX": {
        'class': BoxField,
        'creator': None,  # Будет создан через класс
        'display_name': 'Box',
        'description': 'Creates box-shaped field for spatial control',
        'shape': 'box',
        'architecture': 'CLASS'
    }
}


def get_field_class(field_type):
    """
    Получить класс поля по типу
    
    Args:
        field_type: Тип поля (например, "SPHERE")
        
    Returns:
        BaseField: Класс поля или None
    """
    if field_type in FIELD_REGISTRY:
        return FIELD_REGISTRY[field_type].get('class')
    return None


def get_field_creator(field_type):
    """
    Получить функцию создания поля по типу
    
    Args:
        field_type: Тип поля (например, "SPHERE")
        
    Returns:
        function: Функция создания поля или None
    """
    if field_type in FIELD_REGISTRY:
        return FIELD_REGISTRY[field_type].get('creator')
    return None


def create_field_via_registry(field_type, name_suffix=""):
    """
    Создать поле через реестр (только классовая архитектура)

    Args:
        field_type: Тип поля
        name_suffix: Суффикс для имени

    Returns:
        bpy.types.NodeGroup: Созданная группа нодов
    """
    if field_type not in FIELD_REGISTRY:
        print(f"[ERROR] Unknown field type: {field_type}")
        return None

    field_info = FIELD_REGISTRY[field_type]
    field_class = field_info.get('class')

    if not field_class:
        print(f"[ERROR] No class found for field type: {field_type}")
        return None

    try:
        field_instance = field_class()
        return field_instance.create_node_group(name_suffix)
    except Exception as e:
        print(f"[ERROR] Failed to create field {field_type}: {e}")
        return None


def list_field_types():
    """
    Получить список всех зарегистрированных типов полей

    Returns:
        list: Список типов полей
    """
    return list(FIELD_REGISTRY.keys())


# Дублирующая функция удалена - используйте list_field_types()


# Устаревшие методы удалены - все поля теперь классовые


def get_field_info(field_type):
    """
    Получить информацию о поле
    
    Args:
        field_type: Тип поля
        
    Returns:
        dict: Информация о поле
    """
    return FIELD_REGISTRY.get(field_type, {})


def register_field(field_type, field_class, display_name, description, shape=None):
    """
    Регистрация нового поля (только классовая архитектура)

    Args:
        field_type: Тип поля (например, "CUSTOM")
        field_class: Класс поля
        display_name: Отображаемое имя
        description: Описание
        shape: Форма поля
    """
    FIELD_REGISTRY[field_type] = {
        'class': field_class,
        'creator': None,
        'display_name': display_name,
        'description': description,
        'shape': shape or 'unknown',
        'architecture': 'CLASS'
    }
    print(f"✅ Registered field: {field_type} -> {field_class.__name__}")
