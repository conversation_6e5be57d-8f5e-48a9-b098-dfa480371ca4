"""
ClonerPro Constants
Centralized constants to replace magic numbers throughout the codebase
"""

# =============================================================================
# Node Positioning Constants
# =============================================================================

# Basic node spacing
NODE_SPACING_X = 200
NODE_SPACING_Y = 100
NODE_SPACING_LARGE = 300

# Node starting positions
NODES_START_X = -800
NODES_START_Y = 0
NODE_BASE_Y = 100

# Node group layout
GROUP_INPUT_X = -800
GROUP_OUTPUT_X = 800
ANTI_RECURSION_OFFSET_X = 900
ANTI_RECURSION_OFFSET_Y = 0

# =============================================================================
# Cloner Limits and Defaults
# =============================================================================

# Instance limits
DEFAULT_INSTANCE_LIMIT = 1000
MAX_INSTANCE_COUNT = 10000
MIN_INSTANCE_COUNT = 1

# Grid cloner defaults
GRID_DEFAULT_COUNT_X = 3
GRID_DEFAULT_COUNT_Y = 3
GRID_DEFAULT_COUNT_Z = 1
GRID_MAX_COUNT = 100
GRID_MIN_COUNT = 1
GRID_DEFAULT_SPACING = (3.0, 3.0, 3.0)

# Linear cloner defaults
LINEAR_DEFAULT_COUNT = 5
LINEAR_MAX_COUNT = 1000
LINEAR_DEFAULT_OFFSET = (3.0, 0.0, 0.0)

# Circle cloner defaults
CIRCLE_DEFAULT_COUNT = 8
CIRCLE_MAX_COUNT = 100
CIRCLE_DEFAULT_RADIUS = 2.0
CIRCLE_MIN_RADIUS = 0.1

# Object/Spline cloner defaults
MESH_CLONER_DEFAULT_COUNT = 50
MESH_CLONER_DEFAULT_DENSITY = 1.0

# =============================================================================
# Transform Defaults
# =============================================================================

# Default scale values
DEFAULT_SCALE = (1.0, 1.0, 1.0)
DEFAULT_ROTATION = (0.0, 0.0, 0.0)
DEFAULT_POSITION = (0.0, 0.0, 0.0)

# Random limits
RANDOM_SCALE_MIN = 0.0
RANDOM_SCALE_MAX = 1.0
RANDOM_SEED_MIN = 0
RANDOM_SEED_MAX = 10000

# =============================================================================
# UI Constants
# =============================================================================

# Panel dimensions
UI_PANEL_WIDTH = 300
UI_BUTTON_HEIGHT = 25
UI_SPACING = 5

# Error message lengths
MAX_ERROR_MESSAGE_LENGTH = 200
MAX_WARNING_MESSAGE_LENGTH = 150

# =============================================================================
# System Constants
# =============================================================================

# Collection naming
CLONERS_COLLECTION_PREFIX = "CLONERS"
CLONER_TO_COLLECTION_NAME = "ClonerTo"
CLONER_OBJECT_PREFIX = "cloner"

# Safety system timeouts (in seconds)
SAFETY_OPERATION_TIMEOUT = 30
DEPENDENCY_CHECK_TIMEOUT = 5
CLEANUP_OPERATION_TIMEOUT = 10

# Memory management
MAX_CACHED_NODE_GROUPS = 50
CACHE_CLEANUP_INTERVAL = 300  # 5 minutes

# =============================================================================
# File System Constants
# =============================================================================

# Temporary file handling
TEMP_FILE_CLEANUP_DELAY = 60  # seconds
MAX_TEMP_FILES = 100

# =============================================================================
# Validation Constants
# =============================================================================

# Object validation
MIN_OBJECT_NAME_LENGTH = 1
MAX_OBJECT_NAME_LENGTH = 63  # Blender limit

# Collection validation
MIN_COLLECTION_NAME_LENGTH = 1
MAX_COLLECTION_NAME_LENGTH = 63

# Modifier validation
MIN_MODIFIER_NAME_LENGTH = 1
MAX_MODIFIER_NAME_LENGTH = 63

# =============================================================================
# Performance Constants
# =============================================================================

# Batch operation limits
MAX_BATCH_OPERATIONS = 100
BATCH_OPERATION_DELAY = 0.1  # seconds between operations

# Scanner performance
MAX_SCAN_ITERATIONS = 1000
SCAN_TIMEOUT = 10  # seconds

# =============================================================================
# Geometry Nodes Constants
# =============================================================================

# Socket types
SOCKET_TYPE_GEOMETRY = 'NodeSocketGeometry'
SOCKET_TYPE_OBJECT = 'NodeSocketObject'
SOCKET_TYPE_COLLECTION = 'NodeSocketCollection'
SOCKET_TYPE_VECTOR = 'NodeSocketVector'
SOCKET_TYPE_FLOAT = 'NodeSocketFloat'
SOCKET_TYPE_INT = 'NodeSocketInt'
SOCKET_TYPE_BOOL = 'NodeSocketBool'

# Node types
NODE_TYPE_GROUP_INPUT = 'NodeGroupInput'
NODE_TYPE_GROUP_OUTPUT = 'NodeGroupOutput'
NODE_TYPE_OBJECT_INFO = 'GeometryNodeObjectInfo'
NODE_TYPE_INSTANCE_ON_POINTS = 'GeometryNodeInstanceOnPoints'
NODE_TYPE_GRID_POINTS = 'FunctionNodeInputSpecialCharacters'  # Placeholder
NODE_TYPE_REALIZE_INSTANCES = 'GeometryNodeRealizeInstances'

# =============================================================================
# Error Codes
# =============================================================================

# Error code ranges
ERROR_CODE_GENERAL = 1000
ERROR_CODE_VALIDATION = 2000
ERROR_CODE_CREATION = 3000
ERROR_CODE_DELETION = 4000
ERROR_CODE_MODIFICATION = 5000
ERROR_CODE_SAFETY = 6000

# Specific error codes
ERROR_NO_ACTIVE_OBJECT = ERROR_CODE_VALIDATION + 1
ERROR_INVALID_OBJECT_TYPE = ERROR_CODE_VALIDATION + 2
ERROR_MISSING_COLLECTION = ERROR_CODE_VALIDATION + 3
ERROR_CLONER_CREATION_FAILED = ERROR_CODE_CREATION + 1
ERROR_NODE_GROUP_CREATION_FAILED = ERROR_CODE_CREATION + 2
ERROR_MODIFIER_CREATION_FAILED = ERROR_CODE_CREATION + 3

# =============================================================================
# Version Information
# =============================================================================

# API version for compatibility checks
CLONERPRO_API_VERSION = (1, 0, 0)
MIN_BLENDER_VERSION = (4, 0, 0)
RECOMMENDED_BLENDER_VERSION = (4, 2, 0)