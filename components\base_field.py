"""
Base Field Class для ClonerPro
Базовый класс для всех полей с общей функциональностью
"""

import bpy
from .base import BaseComponent


class BaseField(BaseComponent):
    """
    Базовый класс для всех полей ClonerPro

    Предоставляет общую функциональность:
    - Создание базовых сокетов поля
    - Создание Named Attribute для силы поля
    - Система включения/выключения поля
    - Работа с falloff и strength
    """

    component_type = "FIELD"

    def __init__(self):
        super().__init__()

    def get_base_field_sockets(self):
        """
        Базовые сокеты, общие для всех полей

        Returns:
            list: Список базовых сокетов (name, socket_type, in_out, default, min, max)
        """
        return [
            # Геометрия
            ("Geometry", "NodeSocketGeometry", "INPUT", None),
            ("Geometry", "NodeSocketGeometry", "OUTPUT", None),

            # Основные настройки поля
            ("Enable", "NodeSocketBool", "INPUT", True),
            ("Inner Strength", "NodeSocketFloat", "INPUT", 1.0, 0.0, 1.0),
            ("Outer Strength", "NodeSocketFloat", "INPUT", 0.0, 0.0, 1.0),
        ]

    def create_field_interface(self, node_group, specific_sockets=None):
        """
        Создание интерфейса поля (базовые + специфичные сокеты)

        Args:
            node_group: Node group для создания интерфейса
            specific_sockets: Список специфичных сокетов для данного поля

        Returns:
            dict: Маппинг имен сокетов к их объектам
        """
        socket_mapping = {}

        # Создаем базовые сокеты
        base_sockets = self.get_base_field_sockets()
        for socket_info in base_sockets:
            name, socket_type, in_out = socket_info[:3]
            default = socket_info[3] if len(socket_info) > 3 else None
            min_val = socket_info[4] if len(socket_info) > 4 else None
            max_val = socket_info[5] if len(socket_info) > 5 else None

            socket = node_group.interface.new_socket(
                name=name,
                in_out=in_out,
                socket_type=socket_type
            )

            # Устанавливаем значения по умолчанию и ограничения
            if default is not None and hasattr(socket, 'default_value'):
                socket.default_value = default
            if min_val is not None and hasattr(socket, 'min_value'):
                socket.min_value = min_val
            if max_val is not None and hasattr(socket, 'max_value'):
                socket.max_value = max_val

            # Специальные настройки для геометрии
            if name == "Geometry" and hasattr(socket, 'attribute_domain'):
                socket.attribute_domain = 'POINT'

            socket_mapping[name] = socket

        # Создаем специфичные сокеты
        if specific_sockets:
            for socket_info in specific_sockets:
                name, socket_type, in_out = socket_info[:3]
                default = socket_info[3] if len(socket_info) > 3 else None
                min_val = socket_info[4] if len(socket_info) > 4 else None
                max_val = socket_info[5] if len(socket_info) > 5 else None

                socket = node_group.interface.new_socket(
                    name=name,
                    in_out=in_out,
                    socket_type=socket_type
                )

                if default is not None and hasattr(socket, 'default_value'):
                    socket.default_value = default
                if min_val is not None and hasattr(socket, 'min_value'):
                    socket.min_value = min_val
                if max_val is not None and hasattr(socket, 'max_value'):
                    socket.max_value = max_val

                socket_mapping[name] = socket

        return socket_mapping

    def create_base_nodes(self, node_group):
        """
        Создание базовых нодов, общих для всех полей

        Args:
            node_group: Node group

        Returns:
            dict: Словарь с созданными нодами
        """
        nodes = node_group.nodes
        links = node_group.links

        # Очищаем существующие ноды
        nodes.clear()

        # Группа input/output
        group_input = nodes.new('NodeGroupInput')
        group_input.location = (-600, 0)

        group_output = nodes.new('NodeGroupOutput')
        group_output.location = (600, 0)

        # Position для получения координат точек
        position = nodes.new('GeometryNodeInputPosition')
        position.location = (-400, -200)

        # Store Named Attribute для сохранения силы поля
        store_attribute = nodes.new('GeometryNodeStoreNamedAttribute')
        store_attribute.data_type = 'FLOAT'
        store_attribute.domain = 'POINT'
        store_attribute.location = (400, 0)
        store_attribute.inputs['Name'].default_value = "field_strength"  # Имя атрибута

        # Enable multiply для включения/выключения поля
        enable_multiply = nodes.new('ShaderNodeMath')
        enable_multiply.operation = 'MULTIPLY'
        enable_multiply.location = (200, -100)

        # Подключаем базовые связи
        links.new(group_input.outputs['Geometry'], store_attribute.inputs['Geometry'])
        links.new(group_input.outputs['Enable'], enable_multiply.inputs[1])
        links.new(store_attribute.outputs['Geometry'], group_output.inputs['Geometry'])

        base_nodes = {
            'group_input': group_input,
            'group_output': group_output,
            'position': position,
            'store_attribute': store_attribute,
            'enable_multiply': enable_multiply,
            'nodes': nodes,
            'links': links
        }

        return base_nodes

    def create_field_node_group(self, name_suffix=""):
        """
        Создание node group поля с полной логикой

        Args:
            name_suffix: Суффикс для имени группы

        Returns:
            bpy.types.NodeGroup: Созданная группа нодов
        """
        # НОВАЯ ЛОГИКА: Создаём wrapper-обёртку вместо прямого размещения нодов
        from ..core.wrappers import WrapperNodeGroup

        wrapper = WrapperNodeGroup(self)
        return wrapper.create_wrapper_node_group(name_suffix)

    def create_field_logic_group(self, name_suffix=""):
        """
        Создание node group с логикой поля (без wrapper-обёртки)
        Используется внутри wrapper-системы

        Args:
            name_suffix: Суффикс для имени группы

        Returns:
            bpy.types.NodeGroup: Созданная группа нодов с логикой
        """
        # Создаем новую группу узлов
        group_name = f"{self.bl_label.replace(' ', '')}{name_suffix}"
        node_group = bpy.data.node_groups.new(name=group_name, type='GeometryNodeTree')

        # Настройки группы
        node_group.color_tag = 'NONE'
        node_group.description = f"{self.bl_label} - создает атрибут с силой поля"
        node_group.default_group_node_width = 140
        node_group.is_modifier = True  # Это ОТДЕЛЬНЫЙ модификатор

        # Создаем интерфейс (базовые + специфичные сокеты)
        specific_sockets = self.get_specific_sockets()
        socket_mapping = self.create_field_interface(node_group, specific_sockets)

        # Создаем базовые ноды
        base_nodes = self.create_base_nodes(node_group)

        # Реализуем логику поля (переопределяется в дочерних классах)
        field_strength = self._create_field_logic(base_nodes)

        # Подключаем к enable multiply и store attribute
        base_nodes['links'].new(field_strength, base_nodes['enable_multiply'].inputs[0])
        base_nodes['links'].new(base_nodes['enable_multiply'].outputs[0], base_nodes['store_attribute'].inputs['Value'])

        return node_group

    def get_specific_sockets(self):
        """
        Специфичные сокеты для конкретного поля
        Переопределяется в дочерних классах

        Returns:
            list: Список специфичных сокетов
        """
        return []

    def _create_field_logic(self, base_nodes):
        """
        Основная логика поля - переопределяется в дочерних классах

        Args:
            base_nodes: Словарь с базовыми нодами

        Returns:
            NodeSocket: Выход силы поля
        """
        # Базовая реализация - возвращаем константу 1.0
        return base_nodes['group_input'].outputs['Inner Strength']

    def create_node_group(self, name_suffix=""):
        """
        Публичный метод для создания node group
        Алиас для create_field_node_group для совместимости
        """
        return self.create_field_node_group(name_suffix)
    
    def get_socket_definitions(self):
        """Определения сокетов для базового поля"""
        return {
            "inputs": [
                ("Geometry", "NodeSocketGeometry"),
                ("Enable", "NodeSocketBool"),
            ],
            "outputs": [
                ("Geometry", "NodeSocketGeometry"),
                ("Field", "NodeSocketFloat"),
            ]
        }
    
    def get_default_parameters(self):
        """Параметры по умолчанию для базового поля"""
        return {
            "enable": True,
            "strength": 1.0,
        }
    
    def get_parameter_groups(self):
        """Группировка параметров для UI"""
        return {
            "Field Settings": ["Enable", "Inner Strength", "Outer Strength"],
            "Spatial Parameters": [],
            "Falloff Controls": []
        }
    
    def create_visual_representation(self, name="Field_Visual"):
        """
        Создание визуального представления поля
        Должно быть переопределено в наследниках
        """
        pass
    
    def calculate_field_strength(self, position, parameters):
        """
        Вычисление силы поля в заданной точке
        Должно быть переопределено в наследниках
        
        Args:
            position: Позиция точки в пространстве
            parameters: Параметры поля
            
        Returns:
            float: Сила поля (0.0 - 1.0)
        """
        return 1.0
    
    def get_field_bounds(self, parameters):
        """
        Получение границ поля
        
        Args:
            parameters: Параметры поля
            
        Returns:
            tuple: (min_bound, max_bound) как Vector
        """
        return ((-1, -1, -1), (1, 1, 1))
    
    def validate_field_parameters(self, parameters):
        """
        Валидация параметров поля
        
        Args:
            parameters: Словарь параметров
            
        Returns:
            tuple: (is_valid, error_message)
        """
        base_valid, base_error = super().validate_parameters(parameters)
        if not base_valid:
            return False, base_error
            
        # Дополнительная валидация для полей
        if "strength" in parameters:
            if not (0.0 <= parameters["strength"] <= 1.0):
                return False, "Field strength must be between 0.0 and 1.0"
        
        return True, ""