"""
Modifier Management Operators для ClonerPro
Умные операторы для управления порядком клонеров и эффекторов
"""

import bpy
from bpy.types import Operator
from bpy.props import StringProperty


class CLONERPRO_OT_move_effector_up(Operator):
    """Переместить эффектор вверх с умной логикой"""
    bl_idname = "clonerpro.move_effector_up"
    bl_label = "Move Effector Up"
    bl_description = "Move effector up in modifier stack with smart logic"
    bl_options = {'REGISTER', 'UNDO'}
    
    object_name: StringProperty(
        name="Object Name",
        description="Name of the object containing the modifier",
        default=""
    )
    modifier_name: StringProperty(
        name="Modifier Name", 
        description="Name of the modifier to move",
        default=""
    )
    
    def execute(self, context):
        obj = bpy.data.objects.get(self.object_name)
        if not obj:
            self.report({'ERROR'}, f"Object {self.object_name} not found")
            return {'CANCELLED'}
        
        modifier = obj.modifiers.get(self.modifier_name)
        if not modifier:
            self.report({'ERROR'}, f"Modifier {self.modifier_name} not found")
            return {'CANCELLED'}
        
        success = self._smart_move_effector_up(obj, modifier)
        if success:
            self.report({'INFO'}, f"Moved {self.modifier_name} up")
        else:
            self.report({'WARNING'}, f"Cannot move {self.modifier_name} up further")
        
        return {'FINISHED'}
    
    def _smart_move_effector_up(self, obj, effector_modifier):
        """
        Простое перемещение эффектора вверх с перескакиванием через Realize Instances:
        - Перемещаем на одну позицию вверх
        - Если попали на Realize Instances, перескакиваем его
        - НЕ перемещаемся выше первого модификатора (позиция 0)
        """
        try:
            effector_index = obj.modifiers.find(effector_modifier.name)
            print(f"[SMART MOVE] UP: Current effector index: {effector_index}, total modifiers: {len(obj.modifiers)}")
            
            if effector_index <= 1:  # НЕ можем перемещаться на позицию 0 или если уже на позиции 1
                print(f"[SMART MOVE] UP: Cannot move - would reach position 0 (current: {effector_index})")
                return False
            
            bpy.context.view_layer.objects.active = obj
            
            # Простое перемещение вверх
            bpy.ops.object.modifier_move_up(modifier=effector_modifier.name)
            
            # Проверяем новую позицию - если попали на Realize Instances, перескакиваем его
            new_index = obj.modifiers.find(effector_modifier.name)
            if new_index > 0:  # Можем проверить позицию выше
                modifier_above = obj.modifiers[new_index - 1]
                if self._is_realize_instances_modifier(modifier_above):
                    # КРИТИЧЕСКАЯ ПРОВЕРКА: НЕ перескакиваем если попадем на позицию 0
                    target_position = new_index - 1
                    if target_position > 0:  # Позиция должна быть больше 0 (не равна!)
                        bpy.ops.object.modifier_move_up(modifier=effector_modifier.name)
                        print(f"[SMART MOVE] Jumped over Realize Instances {modifier_above.name}")
                    else:
                        print(f"[SMART MOVE] Cannot jump over Realize Instances - would reach forbidden position {target_position}")
            
            print(f"[SMART MOVE] Moved effector {effector_modifier.name} up")
            return True
            
        except Exception as e:
            print(f"[SMART MOVE] Error moving effector up: {e}")
            return False
    
    def _is_realize_instances_modifier(self, modifier):
        """Проверяет, является ли модификатор Realize Instances"""
        return (modifier.type == 'NODES' and 
                modifier.get("modifier_type") == "REALIZE_INSTANCES")
    
    def _is_cloner_modifier(self, modifier):
        """Проверяет, является ли модификатор клонером"""
        if modifier.type != 'NODES' or not modifier.node_group:
            return False
        
        # Проверяем по метаданным
        if modifier.get("architecture_type") in ["standard", "class_based"]:
            effector_type = modifier.get("effector_type")
            if not effector_type:  # Клонеры не имеют effector_type
                return True
        
        # Проверяем по имени node group
        node_group_name = modifier.node_group.name.lower()
        cloner_keywords = ['cloner', 'grid', 'linear', 'circle', 'spiral', 'object', 'spline']
        return any(keyword in node_group_name for keyword in cloner_keywords)


class CLONERPRO_OT_move_effector_down(Operator):
    """Переместить эффектор вниз с умной логикой"""
    bl_idname = "clonerpro.move_effector_down"
    bl_label = "Move Effector Down"
    bl_description = "Move effector down in modifier stack with smart logic"
    bl_options = {'REGISTER', 'UNDO'}
    
    object_name: StringProperty(
        name="Object Name",
        description="Name of the object containing the modifier",
        default=""
    )
    modifier_name: StringProperty(
        name="Modifier Name", 
        description="Name of the modifier to move",
        default=""
    )
    
    def execute(self, context):
        obj = bpy.data.objects.get(self.object_name)
        if not obj:
            self.report({'ERROR'}, f"Object {self.object_name} not found")
            return {'CANCELLED'}
        
        modifier = obj.modifiers.get(self.modifier_name)
        if not modifier:
            self.report({'ERROR'}, f"Modifier {self.modifier_name} not found")
            return {'CANCELLED'}
        
        success = self._smart_move_effector_down(obj, modifier)
        if success:
            self.report({'INFO'}, f"Moved {self.modifier_name} down")
        else:
            self.report({'WARNING'}, f"Cannot move {self.modifier_name} down further")
        
        return {'FINISHED'}
    
    def _smart_move_effector_down(self, obj, effector_modifier):
        """
        Умное перемещение эффектора вниз с перескакиванием паттерна Realize+Cloner:
        - Если ниже есть Realize Instances, перескакиваем его И следующий клонер
        - Встаем между следующим клонером и его Realize Instances
        - НЕ перемещаемся ниже последнего модификатора в стеке
        """
        try:
            effector_index = obj.modifiers.find(effector_modifier.name)
            if effector_index >= len(obj.modifiers) - 1:
                return False  # Уже в самом низу
            
            bpy.context.view_layer.objects.active = obj
            
            # Проверяем, что ниже - если Realize Instances, то нужно перескочить ДВЕ позиции
            modifier_below = obj.modifiers[effector_index + 1]
            
            if self._is_realize_instances_modifier(modifier_below):
                # ПРОВЕРЯЕМ: достаточно ли места для двойного перескока
                if effector_index + 2 < len(obj.modifiers):  # Есть место для двух шагов
                    # Перескакиваем Realize Instances
                    bpy.ops.object.modifier_move_down(modifier=effector_modifier.name)
                    print(f"[SMART MOVE] Jumped over Realize Instances {modifier_below.name}")
                    
                    # Перескакиваем следующий клонер
                    new_index = obj.modifiers.find(effector_modifier.name)
                    next_modifier = obj.modifiers[new_index + 1]
                    bpy.ops.object.modifier_move_down(modifier=effector_modifier.name)
                    print(f"[SMART MOVE] Jumped over cloner {next_modifier.name}")
                else:
                    print(f"[SMART MOVE] Cannot perform double jump - not enough space at bottom of stack")
                    return False
            else:
                # Простое перемещение вниз если ниже НЕ Realize Instances
                bpy.ops.object.modifier_move_down(modifier=effector_modifier.name)
            
            print(f"[SMART MOVE] Moved effector {effector_modifier.name} down")
            return True
            
        except Exception as e:
            print(f"[SMART MOVE] Error moving effector down: {e}")
            return False
    
    def _is_realize_instances_modifier(self, modifier):
        """Проверяет, является ли модификатор Realize Instances"""
        return (modifier.type == 'NODES' and 
                modifier.get("modifier_type") == "REALIZE_INSTANCES")


class CLONERPRO_OT_move_to_effector_group(Operator):
    """Переместить модификатор в группу эффекторов"""
    bl_idname = "clonerpro.move_to_effector_group"
    bl_label = "Move to Effector Group"
    bl_description = "Move modifier to effector group (after cloners, before Realize Instances)"
    bl_options = {'REGISTER', 'UNDO'}
    
    object_name: StringProperty(
        name="Object Name",
        description="Name of the object containing the modifier",
        default=""
    )
    modifier_name: StringProperty(
        name="Modifier Name", 
        description="Name of the modifier to move",
        default=""
    )
    
    def execute(self, context):
        obj = bpy.data.objects.get(self.object_name)
        if not obj:
            self.report({'ERROR'}, f"Object {self.object_name} not found")
            return {'CANCELLED'}
        
        modifier = obj.modifiers.get(self.modifier_name)
        if not modifier:
            self.report({'ERROR'}, f"Modifier {self.modifier_name} not found")
            return {'CANCELLED'}
        
        success = self._move_to_effector_group(obj, modifier)
        if success:
            self.report({'INFO'}, f"Moved {self.modifier_name} to effector group")
        else:
            self.report({'WARNING'}, f"Failed to move {self.modifier_name}")
        
        return {'FINISHED'}
    
    def _move_to_effector_group(self, obj, modifier):
        """
        Перемещает модификатор в оптимальную позицию для эффектора:
        - Находит самую нижнюю позицию перед всеми Realize Instances
        - Перескакивает через Realize Instances как будто их нет
        """
        try:
            # Находим оптимальную позицию для эффектора
            target_index = self._find_optimal_effector_position(obj)
            
            if target_index is not None:
                self._move_to_specific_position(obj, modifier, target_index)
                print(f"[SMART MOVE] Moved {modifier.name} to optimal effector position (index {target_index})")
            else:
                # Fallback - в конец стека
                self._move_to_end_of_stack(obj, modifier)
                print(f"[SMART MOVE] Moved {modifier.name} to end of stack (fallback)")
            
            print(f"[SMART MOVE] Moved {modifier.name} to effector group")
            return True
            
        except Exception as e:
            print(f"[SMART MOVE] Error moving to effector group: {e}")
            return False
    
    
    def _move_to_end_of_stack(self, obj, modifier):
        """Перемещает модификатор в конец стека"""
        try:
            current_index = obj.modifiers.find(modifier.name)
            bpy.context.view_layer.objects.active = obj
            
            # Перемещаем в самый конец
            while current_index < len(obj.modifiers) - 1:
                bpy.ops.object.modifier_move_down(modifier=modifier.name)
                current_index = obj.modifiers.find(modifier.name)
                
        except Exception as e:
            print(f"[SMART MOVE] Error moving to end: {e}")
    
    
    def _find_optimal_effector_position(self, obj):
        """
        Находит оптимальную позицию для эффектора:
        - Самая нижняя позиция, но перед всеми Realize Instances
        """
        # Ищем первый Realize Instances снизу
        first_realize_index = None
        for i in range(len(obj.modifiers) - 1, -1, -1):
            if self._is_realize_instances_modifier(obj.modifiers[i]):
                first_realize_index = i
        
        if first_realize_index is not None:
            # Есть Realize Instances - размещаем перед самым первым (верхним)
            return first_realize_index
        else:
            # Нет Realize Instances - размещаем в самый конец
            return len(obj.modifiers) - 1
    
    def _move_to_specific_position(self, obj, modifier, target_index):
        """
        Перемещает модификатор в конкретную позицию
        """
        try:
            current_index = obj.modifiers.find(modifier.name)
            bpy.context.view_layer.objects.active = obj
            
            # Перемещаем к нужной позиции
            while current_index > target_index:
                bpy.ops.object.modifier_move_up(modifier=modifier.name)
                current_index = obj.modifiers.find(modifier.name)
                
            while current_index < target_index:
                bpy.ops.object.modifier_move_down(modifier=modifier.name)
                current_index = obj.modifiers.find(modifier.name)
                
        except Exception as e:
            print(f"[SMART MOVE] Error moving to specific position: {e}")
    
    def _is_cloner_modifier(self, modifier):
        """Проверяет, является ли модификатор клонером"""
        if modifier.type != 'NODES' or not modifier.node_group:
            return False
        
        # Проверяем по метаданным
        if modifier.get("architecture_type") in ["standard", "class_based"]:
            effector_type = modifier.get("effector_type")
            if not effector_type:  # Клонеры не имеют effector_type
                return True
        
        # Проверяем по имени node group
        node_group_name = modifier.node_group.name.lower()
        cloner_keywords = ['cloner', 'grid', 'linear', 'circle', 'spiral', 'object', 'spline']
        return any(keyword in node_group_name for keyword in cloner_keywords)


def register():
    """Регистрация операторов управления модификаторами"""
    try:
        bpy.utils.register_class(CLONERPRO_OT_move_effector_up)
    except ValueError as e:
        if "already registered" in str(e):
            print(f"[WARNING] CLONERPRO_OT_move_effector_up already registered, skipping")
        else:
            raise e
    
    try:
        bpy.utils.register_class(CLONERPRO_OT_move_effector_down)
    except ValueError as e:
        if "already registered" in str(e):
            print(f"[WARNING] CLONERPRO_OT_move_effector_down already registered, skipping")
        else:
            raise e
    
    try:
        bpy.utils.register_class(CLONERPRO_OT_move_to_effector_group)
    except ValueError as e:
        if "already registered" in str(e):
            print(f"[WARNING] CLONERPRO_OT_move_to_effector_group already registered, skipping")
        else:
            raise e


def unregister():
    """Отмена регистрации операторов управления модификаторами"""
    try:
        bpy.utils.unregister_class(CLONERPRO_OT_move_to_effector_group)
    except ValueError as e:
        if "not registered" in str(e):
            print(f"[WARNING] CLONERPRO_OT_move_to_effector_group not registered, skipping")
        else:
            print(f"[ERROR] Failed to unregister CLONERPRO_OT_move_to_effector_group: {e}")
    except RuntimeError as e:
        if "missing bl_rna attribute" in str(e):
            print(f"[WARNING] CLONERPRO_OT_move_to_effector_group not properly registered, skipping")
        else:
            print(f"[ERROR] RuntimeError unregistering CLONERPRO_OT_move_to_effector_group: {e}")
    
    try:
        bpy.utils.unregister_class(CLONERPRO_OT_move_effector_down)
    except ValueError as e:
        if "not registered" in str(e):
            print(f"[WARNING] CLONERPRO_OT_move_effector_down not registered, skipping")
        else:
            print(f"[ERROR] Failed to unregister CLONERPRO_OT_move_effector_down: {e}")
    except RuntimeError as e:
        if "missing bl_rna attribute" in str(e):
            print(f"[WARNING] CLONERPRO_OT_move_effector_down not properly registered, skipping")
        else:
            print(f"[ERROR] RuntimeError unregistering CLONERPRO_OT_move_effector_down: {e}")
    
    try:
        bpy.utils.unregister_class(CLONERPRO_OT_move_effector_up)
    except ValueError as e:
        if "not registered" in str(e):
            print(f"[WARNING] CLONERPRO_OT_move_effector_up not registered, skipping")
        else:
            print(f"[ERROR] Failed to unregister CLONERPRO_OT_move_effector_up: {e}")
    except RuntimeError as e:
        if "missing bl_rna attribute" in str(e):
            print(f"[WARNING] CLONERPRO_OT_move_effector_up not properly registered, skipping")
        else:
            print(f"[ERROR] RuntimeError unregistering CLONERPRO_OT_move_effector_up: {e}")