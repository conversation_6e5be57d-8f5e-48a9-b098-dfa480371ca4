"""
Linear Cloner - Class-based implementation
Объединяет логику и конфигурацию в один класс
"""

import bpy
from ..base_cloner import BaseCloner


class LinearCloner(BaseCloner):
    """
    Linear Cloner - создает линейное распределение клонов
    Объединяет всю логику, конфигурацию и UI в одном классе
    """
    
    bl_idname = "LINEAR"
    bl_label = "Linear Cloner"
    
    def __init__(self):
        super().__init__()
    
    def register_properties(self, props_owner):
        """
        Регистрация всех свойств Linear Cloner
        Заменяет старый linear_config.py
        """
        # Basic Linear Settings
        props_owner.linear_count = bpy.props.IntProperty(
            name="Count",
            description="Number of instances along the line",
            default=5,
            min=1,
            max=1000
        )
        props_owner.linear_offset = bpy.props.FloatVectorProperty(
            name="Offset",
            description="Offset between instances",
            default=(1.0, 0.0, 0.0),  # Базовое значение
            size=3
        )
        
        # Gradient Settings
        props_owner.linear_scale_start = bpy.props.FloatVectorProperty(
            name="Scale Start",
            description="Scale at the start of the line",
            default=(1.0, 1.0, 1.0),
            size=3
        )
        props_owner.linear_scale_end = bpy.props.FloatVectorProperty(
            name="Scale End", 
            description="Scale at the end of the line",
            default=(1.0, 1.0, 1.0),
            size=3
        )
        props_owner.linear_rotation_start = bpy.props.FloatVectorProperty(
            name="Rotation Start",
            description="Rotation at the start of the line",
            default=(0.0, 0.0, 0.0),
            subtype='EULER',
            size=3
        )
        props_owner.linear_rotation_end = bpy.props.FloatVectorProperty(
            name="Rotation End",
            description="Rotation at the end of the line",
            default=(0.0, 0.0, 0.0),
            subtype='EULER',
            size=3
        )

        # 🆕 НОВЫЕ ПАРАМЕТРЫ из ROADMAP:
        props_owner.linear_distribution_curve = bpy.props.FloatProperty(
            name="Distribution Curve",
            description="Curve factor for non-uniform distribution (1.0 = linear, >1.0 = exponential, <1.0 = logarithmic)",
            default=1.0,
            min=0.1,
            max=5.0
        )
        props_owner.linear_path_object = bpy.props.PointerProperty(
            name="Path Object",
            description="Object to follow as path (curve, mesh edge, etc.)",
            type=bpy.types.Object
        )
        props_owner.linear_path_mode = bpy.props.BoolProperty(
            name="Path Mode",
            description="Enable path following mode",
            default=False
        )
    
    def get_specific_sockets(self):
        """
        Специфичные сокеты для Linear Cloner

        Returns:
            list: Список специфичных сокетов (name, socket_type, in_out, default)
        """
        return [
            # Основные параметры линии
            ("Count", "NodeSocketInt", "INPUT", 5),
            ("Offset", "NodeSocketVector", "INPUT", (1.0, 0.0, 0.0)),  # Базовое значение

            # Градиентные настройки
            ("Scale Start", "NodeSocketVector", "INPUT", (1.0, 1.0, 1.0)),
            ("Scale End", "NodeSocketVector", "INPUT", (1.0, 1.0, 1.0)),
            ("Rotation Start", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),
            ("Rotation End", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),

            # 🆕 НОВЫЕ ВОЗМОЖНОСТИ из ROADMAP:
            ("Distribution Curve", "NodeSocketFloat", "INPUT", 1.0),     # Кривая распределения (степень)
            ("Path Object", "NodeSocketObject", "INPUT", None),          # Объект пути для следования
            ("Path Mode", "NodeSocketBool", "INPUT", False),             # Режим следования по пути
        ]
    
    
    def _create_cloner_logic(self, base_nodes, mode):
        """
        Основная логика Linear Cloner
        
        Args:
            base_nodes: Словарь с базовыми нодами
            mode: Режим клонера
            
        Returns:
            NodeSocket: Финальный выход геометрии
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Получаем input геометрию в зависимости от режима
        geometry_input = self.get_geometry_input(base_nodes, mode)
        
        # Создаем линейное распределение точек
        linear_points = self._create_linear_distribution(base_nodes)
        
        # Инстансируем геометрию на точки
        instance_node = nodes.new('GeometryNodeInstanceOnPoints')
        instance_node.name = "Instance on Line"
        instance_node.location = (400, 0)
        links.new(linear_points, instance_node.inputs['Points'])
        links.new(geometry_input, instance_node.inputs['Instance'])

        # Применяем Collection Random для unified клонеров в Collection режиме
        self.apply_collection_randomization(base_nodes, instance_node, mode)
        
        # Получаем индекс для градиента и рандомизации
        index_node = nodes.new('GeometryNodeInputIndex')
        index_node.location = (200, -200)
        
        # Применяем градиентные трансформации
        instances_with_gradient = self._apply_gradient_transforms(base_nodes, instance_node.outputs['Instances'], index_node.outputs['Index'])
        
        # Применяем instance трансформации
        instances_with_transforms = self.apply_instance_transforms(base_nodes, instances_with_gradient)
        
        # Применяем рандомизацию
        randomized_instances = self.apply_random_transforms(base_nodes, instances_with_transforms, index_node.outputs['Index'])
        
        # Применяем глобальные трансформации
        final_geometry = self.apply_global_transforms(base_nodes, randomized_instances)
        
        return final_geometry
    
    def _create_linear_distribution(self, base_nodes):
        """
        Создание линейного распределения точек с поддержкой новых возможностей

        Args:
            base_nodes: Словарь с базовыми нодами

        Returns:
            NodeSocket: Выход с линейными точками
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']

        # 🆕 Переключаемся между обычным линейным режимом и режимом следования по пути
        path_mode_switch = nodes.new('GeometryNodeSwitch')
        path_mode_switch.input_type = 'GEOMETRY'
        path_mode_switch.location = (200, 200)
        path_mode_switch.name = "Path Mode Switch"
        links.new(group_input.outputs['Path Mode'], path_mode_switch.inputs['Switch'])

        # Обычный линейный режим
        linear_points = self._create_standard_linear_points(base_nodes)

        # 🆕 Режим следования по пути
        path_points = self._create_path_following_points(base_nodes)

        # Подключаем к переключателю
        links.new(linear_points, path_mode_switch.inputs['False'])  # Обычный режим
        links.new(path_points, path_mode_switch.inputs['True'])     # Режим пути

        return path_mode_switch.outputs['Output']

    def _create_standard_linear_points(self, base_nodes):
        """
        Создание стандартных линейных точек с поддержкой Distribution Curve

        Args:
            base_nodes: Словарь с базовыми нодами

        Returns:
            NodeSocket: Выход с линейными точками
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']

        # 🆕 Создаем точки вручную для поддержки Distribution Curve
        points_geometry = nodes.new('GeometryNodePoints')
        points_geometry.location = (-400, 200)
        links.new(group_input.outputs['Count'], points_geometry.inputs['Count'])

        # Получаем индекс для каждой точки
        index_node = nodes.new('GeometryNodeInputIndex')
        index_node.location = (-500, 100)

        # 🎯 ПРАВИЛЬНАЯ ЛОГИКА: "Наружное" расширение + Distribution Curve

        # Для "наружного" расширения просто используем прямой индекс как базу
        # Но применяем Distribution Curve правильно

        # Нормализуем индекс для Distribution Curve (0-1)
        count_minus_one = nodes.new('ShaderNodeMath')
        count_minus_one.operation = 'SUBTRACT'
        count_minus_one.inputs[1].default_value = 1.0
        count_minus_one.location = (-500, 50)
        links.new(group_input.outputs['Count'], count_minus_one.inputs[0])

        # Защита от деления на ноль
        safe_count = nodes.new('ShaderNodeMath')
        safe_count.operation = 'MAXIMUM'
        safe_count.inputs[1].default_value = 1.0
        safe_count.location = (-450, 50)
        links.new(count_minus_one.outputs['Value'], safe_count.inputs[0])

        normalized_index = nodes.new('ShaderNodeMath')
        normalized_index.operation = 'DIVIDE'
        normalized_index.location = (-400, 75)
        links.new(index_node.outputs['Index'], normalized_index.inputs[0])
        links.new(safe_count.outputs['Value'], normalized_index.inputs[1])

        # Применяем Distribution Curve к нормализованному индексу
        curved_normalized = nodes.new('ShaderNodeMath')
        curved_normalized.operation = 'POWER'
        curved_normalized.location = (-300, 75)
        links.new(normalized_index.outputs['Value'], curved_normalized.inputs[0])
        links.new(group_input.outputs['Distribution Curve'], curved_normalized.inputs[1])

        # Масштабируем обратно к диапазону индексов: curved_normalized * (count-1)
        final_index = nodes.new('ShaderNodeMath')
        final_index.operation = 'MULTIPLY'
        final_index.location = (-200, 75)
        links.new(curved_normalized.outputs['Value'], final_index.inputs[0])
        links.new(safe_count.outputs['Value'], final_index.inputs[1])

        # 🎯 Масштабируем offset для хорошего визуального разделения
        scale_offset = nodes.new('ShaderNodeVectorMath')
        scale_offset.operation = 'MULTIPLY'
        scale_offset.inputs[1].default_value = (2.5, 2.5, 2.5)  # Небольшой коэффициент против сжатия
        scale_offset.location = (-300, 125)
        links.new(group_input.outputs['Offset'], scale_offset.inputs[0])

        # Вычисляем позицию: curved_t * scaled_offset
        position_offset = nodes.new('ShaderNodeVectorMath')
        position_offset.operation = 'MULTIPLY'
        position_offset.location = (-200, 100)
        links.new(scale_offset.outputs['Vector'], position_offset.inputs[0])

        # Преобразуем final_index в вектор
        combine_factor = nodes.new('ShaderNodeCombineXYZ')
        combine_factor.location = (-100, 50)
        links.new(final_index.outputs['Value'], combine_factor.inputs['X'])
        links.new(final_index.outputs['Value'], combine_factor.inputs['Y'])
        links.new(final_index.outputs['Value'], combine_factor.inputs['Z'])

        links.new(combine_factor.outputs['Vector'], position_offset.inputs[1])

        # Устанавливаем позиции точек
        set_position = nodes.new('GeometryNodeSetPosition')
        set_position.name = "Set Linear Positions"
        set_position.location = (-100, 150)
        links.new(points_geometry.outputs['Geometry'], set_position.inputs['Geometry'])
        links.new(position_offset.outputs['Vector'], set_position.inputs['Position'])

        return set_position.outputs['Geometry']

    def _create_path_following_points(self, base_nodes):
        """
        Создание точек, следующих по пути объекта (как в Spline Cloner)

        Args:
            base_nodes: Словарь с базовыми нодами

        Returns:
            NodeSocket: Выход с точками, следующими по пути
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']

        # Получаем геометрию объекта пути
        object_info = nodes.new('GeometryNodeObjectInfo')
        object_info.location = (-500, -100)
        object_info.name = "Path Object Info"
        links.new(group_input.outputs['Path Object'], object_info.inputs['Object'])

        # 🎯 ИСПРАВЛЕНИЕ: Создаем точки прямо на геометрии (как в Spline Cloner)
        # Если это кривая - точки будут на кривой, если меш - на рёбрах
        curve_to_points = nodes.new('GeometryNodeCurveToPoints')
        curve_to_points.location = (-300, -100)
        curve_to_points.mode = 'COUNT'
        curve_to_points.name = "Path Points"
        links.new(object_info.outputs['Geometry'], curve_to_points.inputs['Curve'])
        links.new(group_input.outputs['Count'], curve_to_points.inputs['Count'])

        # Fallback: если нет объекта пути, создаем простую линию с масштабированием
        scale_fallback_offset = nodes.new('ShaderNodeVectorMath')
        scale_fallback_offset.operation = 'MULTIPLY'
        scale_fallback_offset.inputs[1].default_value = (4.0, 4.0, 4.0)  # Тот же коэффициент
        scale_fallback_offset.location = (-400, -175)
        links.new(group_input.outputs['Offset'], scale_fallback_offset.inputs[0])

        fallback_line = nodes.new('GeometryNodeMeshLine')
        fallback_line.name = "Fallback Linear Points"
        fallback_line.mode = 'OFFSET'
        fallback_line.count_mode = 'TOTAL'
        fallback_line.location = (-300, -200)
        links.new(group_input.outputs['Count'], fallback_line.inputs['Count'])
        links.new(scale_fallback_offset.outputs['Vector'], fallback_line.inputs['Offset'])

        # Преобразуем fallback mesh в points
        mesh_to_points = nodes.new('GeometryNodeMeshToPoints')
        mesh_to_points.mode = 'VERTICES'
        mesh_to_points.location = (-200, -200)
        links.new(fallback_line.outputs['Mesh'], mesh_to_points.inputs['Mesh'])

        return curve_to_points.outputs['Points']
    
    def _apply_gradient_transforms(self, base_nodes, instances_input, index_input):
        """
        Применение градиентных трансформаций (Scale/Rotation Start -> End)
        
        Args:
            base_nodes: Словарь с базовыми нодами
            instances_input: Входной сокет с инстансами
            index_input: Входной сокет с индексом
            
        Returns:
            NodeSocket: Выход с градиентными трансформациями
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Нормализуем индекс для интерполяции (0.0 - 1.0)
        # Получаем максимальный индекс (Count - 1)
        count_minus_one = nodes.new('ShaderNodeMath')
        count_minus_one.operation = 'SUBTRACT'
        count_minus_one.inputs[1].default_value = 1.0
        count_minus_one.location = (0, -300)
        links.new(group_input.outputs['Count'], count_minus_one.inputs[0])
        
        # Нормализуем индекс
        normalize_index = nodes.new('ShaderNodeMath')
        normalize_index.operation = 'DIVIDE'
        normalize_index.location = (150, -300)
        links.new(index_input, normalize_index.inputs[0])
        links.new(count_minus_one.outputs['Value'], normalize_index.inputs[1])
        
        # Интерполяция масштаба
        mix_scale = nodes.new('ShaderNodeMix')
        mix_scale.data_type = 'VECTOR'
        mix_scale.blend_type = 'MIX'
        mix_scale.location = (300, -200)
        links.new(normalize_index.outputs['Value'], mix_scale.inputs['Factor'])
        links.new(group_input.outputs['Scale Start'], mix_scale.inputs['A'])
        links.new(group_input.outputs['Scale End'], mix_scale.inputs['B'])
        
        # Интерполяция поворота
        mix_rotation = nodes.new('ShaderNodeMix')
        mix_rotation.data_type = 'VECTOR'
        mix_rotation.blend_type = 'MIX'
        mix_rotation.location = (300, -400)
        links.new(normalize_index.outputs['Value'], mix_rotation.inputs['Factor'])
        links.new(group_input.outputs['Rotation Start'], mix_rotation.inputs['A'])
        links.new(group_input.outputs['Rotation End'], mix_rotation.inputs['B'])
        
        # Применяем градиентное вращение
        rotate_instances = nodes.new('GeometryNodeRotateInstances')
        rotate_instances.name = "Apply Gradient Rotation"
        rotate_instances.location = (500, 0)
        links.new(instances_input, rotate_instances.inputs['Instances'])
        links.new(mix_rotation.outputs['Result'], rotate_instances.inputs['Rotation'])
        
        # Применяем градиентный масштаб
        scale_instances = nodes.new('GeometryNodeScaleInstances')
        scale_instances.name = "Apply Gradient Scale"
        scale_instances.location = (650, 0)
        links.new(rotate_instances.outputs['Instances'], scale_instances.inputs['Instances'])
        links.new(mix_scale.outputs['Result'], scale_instances.inputs['Scale'])
        
        return scale_instances.outputs['Instances']
    
    def draw_ui(self, layout, props_owner, modifier=None):
        """
        Отрисовка UI для Linear Cloner

        Args:
            layout: UI layout
            props_owner: Объект со свойствами
            modifier: Модификатор (если есть)
        """
        if modifier:
            # Если есть модификатор - используем сокеты модификатора
            self._draw_modifier_ui(layout, modifier)
        else:
            # Если нет модификатора - используем настройки из props_owner
            self._draw_settings_ui(layout, props_owner)

    def _draw_modifier_ui(self, layout, modifier):
        """Отрисовка UI через сокеты модификатора"""
        from ...ui.utils.ui_helpers import display_socket_prop

        # Linear Settings
        box = layout.box()
        box.label(text="Linear Settings", icon='IPO_LINEAR')

        display_socket_prop(box, modifier, "Count", text="Count")
        display_socket_prop(box, modifier, "Offset", text="Offset")

        # Gradient Settings
        box = layout.box()
        box.label(text="Gradient", icon='IPO_EASE_IN_OUT')

        col = box.column(align=True)
        col.label(text="Scale:")
        display_socket_prop(col, modifier, "Scale Start", text="Start")
        display_socket_prop(col, modifier, "Scale End", text="End")

        col.separator()
        col.label(text="Rotation:")
        display_socket_prop(col, modifier, "Rotation Start", text="Start")
        display_socket_prop(col, modifier, "Rotation End", text="End")

        # 🆕 Advanced Linear Settings
        advanced_box = layout.box()
        advanced_box.label(text="Advanced Linear", icon='SETTINGS')

        display_socket_prop(advanced_box, modifier, "Distribution Curve", text="Distribution Curve")
        display_socket_prop(advanced_box, modifier, "Path Object", text="Path Object")
        display_socket_prop(advanced_box, modifier, "Path Mode", text="Path Mode")

        # Базовые группы параметров из BaseCloner
        self._draw_base_cloner_ui(layout, None, modifier)

    def _draw_settings_ui(self, layout, props_owner):
        """Отрисовка UI через настройки props_owner"""
        # Linear Settings
        box = layout.box()
        box.label(text="Linear Settings", icon='IPO_LINEAR')

        row = box.row(align=True)
        row.prop(props_owner, "linear_count", text="Count")
        box.prop(props_owner, "linear_offset", text="Offset")

        # Gradient Settings
        box = layout.box()
        box.label(text="Gradient", icon='IPO_EASE_IN_OUT')

        col = box.column(align=True)
        col.label(text="Scale:")
        col.prop(props_owner, "linear_scale_start", text="Start")
        col.prop(props_owner, "linear_scale_end", text="End")

        col.separator()
        col.label(text="Rotation:")
        col.prop(props_owner, "linear_rotation_start", text="Start")
        col.prop(props_owner, "linear_rotation_end", text="End")

        # 🆕 Advanced Linear Settings
        advanced_box = layout.box()
        advanced_box.label(text="Advanced Linear", icon='SETTINGS')

        advanced_box.prop(props_owner, "linear_distribution_curve", text="Distribution Curve")
        advanced_box.prop(props_owner, "linear_path_object", text="Path Object")
        advanced_box.prop(props_owner, "linear_path_mode", text="Path Mode")

        # Базовые группы параметров из BaseCloner
        self._draw_base_cloner_ui(layout, props_owner, None)
    
    def _draw_base_cloner_ui(self, layout, props_owner, modifier):
        """Отрисовка базовых UI групп клонера"""
        from ...ui.utils.ui_helpers import display_socket_prop

        if modifier:
            # Instance Transform через сокеты модификатора
            box = layout.box()
            box.label(text="Instance Transform", icon='CON_TRANSFORM')

            display_socket_prop(box, modifier, "Instance Scale", text="Scale")
            display_socket_prop(box, modifier, "Instance Rotation", text="Rotation")

            # Randomization через сокеты модификатора
            box = layout.box()
            box.label(text="Randomization", icon='RNDCURVE')

            display_socket_prop(box, modifier, "Random Position", text="Position")
            display_socket_prop(box, modifier, "Random Rotation", text="Rotation")
            display_socket_prop(box, modifier, "Random Scale", text="Scale")
            display_socket_prop(box, modifier, "Random Seed", text="Seed")

            # Global Transform через сокеты модификатора
            box = layout.box()
            box.label(text="Global Transform", icon='WORLD_DATA')

            display_socket_prop(box, modifier, "Global Position", text="Position")
            display_socket_prop(box, modifier, "Global Rotation", text="Rotation")

        else:
            # Instance Transform через props_owner (если нет модификатора)
            box = layout.box()
            box.label(text="Instance Transform", icon='CON_TRANSFORM')

            if hasattr(props_owner, 'instance_scale'):
                box.prop(props_owner, "instance_scale", text="Scale")
            if hasattr(props_owner, 'instance_rotation'):
                box.prop(props_owner, "instance_rotation", text="Rotation")

            # Randomization через props_owner
            box = layout.box()
            box.label(text="Randomization", icon='RNDCURVE')

            if hasattr(props_owner, 'random_position'):
                box.prop(props_owner, "random_position", text="Position")
            if hasattr(props_owner, 'random_rotation'):
                box.prop(props_owner, "random_rotation", text="Rotation")
            if hasattr(props_owner, 'random_scale'):
                box.prop(props_owner, "random_scale", text="Scale")
            if hasattr(props_owner, 'random_seed'):
                box.prop(props_owner, "random_seed", text="Seed")
    
    def get_default_parameters(self):
        """Параметры по умолчанию для Linear Cloner"""
        base_defaults = super().get_default_parameters()
        linear_defaults = {
            "count": 5,
            "offset": (1.0, 0.0, 0.0),  # Базовое значение
            "scale_start": (1.0, 1.0, 1.0),
            "scale_end": (1.0, 1.0, 1.0),
            "rotation_start": (0.0, 0.0, 0.0),
            "rotation_end": (0.0, 0.0, 0.0),
            # 🆕 НОВЫЕ ПАРАМЕТРЫ:
            "distribution_curve": 1.0,
            "path_object": None,
            "path_mode": False
        }
        return {**base_defaults, **linear_defaults}
    
    def get_parameter_groups(self):
        """Группировка параметров для UI"""
        base_groups = super().get_cloner_parameter_groups()
        linear_groups = {
            "Linear Settings": ["Count", "Offset"],
            "Gradient": ["Scale Start", "Scale End", "Rotation Start", "Rotation End"],
            "Advanced Linear": ["Distribution Curve", "Path Object", "Path Mode"]  # 🆕 Новая группа
        }
        return {**linear_groups, **base_groups}


# Экземпляр класса для использования в других модулях
linear_cloner = LinearCloner()


# Функции register/unregister для совместимости с __init__.py
def register():
    """Регистрация Linear Cloner (в новой архитектуре не требуется)"""
    print("✅ Linear Cloner: Using class-based architecture, no registration needed")
    pass


def unregister():
    """Отмена регистрации Linear Cloner (в новой архитектуре не требуется)"""
    print("✅ Linear Cloner: Using class-based architecture, no unregistration needed") 
    pass