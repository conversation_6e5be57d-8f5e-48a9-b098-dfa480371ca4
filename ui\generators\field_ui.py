"""
UI Generator для полей ClonerPro
Автоматическая генерация интерфейса для полей на основе конфигурации
"""

import bpy


# Кастомные функции удалены - используем рабочие функции из ui_helpers


def display_field_socket_prop(layout, modifier, socket_name, text=None, **kwargs):
    """
    Специальная функция для отображения параметров Fields
    Классовая архитектура - синхронизация не требуется
    """
    from ...ui.utils.ui_helpers import display_socket_prop

    # Используем стандартную функцию без синхронизации
    result = display_socket_prop(layout, modifier, socket_name, text=text, **kwargs)

    return result


def draw_sphere_field_parameters(layout, modifier, expanded_state=None):
    """
    DEPRECATED: Отрисовка параметров сферического поля
    Используйте классовую архитектуру
    """
    layout.label(text="DEPRECATED: Use class-based architecture", icon='ERROR')

    # Добавляем секцию управления подключениями к эффекторам ТОЛЬКО ОДИН РАЗ
    draw_field_connections(layout, modifier)


def draw_field_connections(layout, modifier):
    """Отрисовка секции подключений филда к эффекторам"""
    if not modifier or not modifier.id_data:
        return
    
    obj = modifier.id_data
    
    # Заголовок секции
    box = layout.box()
    header = box.row()
    header.label(text="Effector Connections", icon='LINKED')
    
    col = box.column()
    
    # Найти все эффекторы на объекте
    effectors = []
    for mod in obj.modifiers:
        if mod.type == 'NODES' and mod.node_group:
            node_group_name = mod.node_group.name.lower()
            if ('effector' in node_group_name or 'random' in node_group_name or 'noise' in node_group_name):
                # Проверить, подключен ли уже филд к этому эффектору
                is_connected = False
                if mod.node_group:
                    for node in mod.node_group.nodes:
                        if node.name == "Field" and node.type == 'GROUP' and node.node_tree:
                            # Дополнительно проверяем, что это именно field node group
                            if 'field' in node.node_tree.name.lower():
                                is_connected = True
                                break
                
                effectors.append({
                    'name': mod.name,
                    'connected': is_connected
                })
    
    if not effectors:
        col.label(text="No effectors found on this object", icon='INFO')
        return
    
    # Отображение эффекторов с кнопками подключения/отключения
    for effector in effectors:
        row = col.row()
        # Показываем статус для отладки
        status_text = f"{effector['name']} ({'Connected' if effector['connected'] else 'Not Connected'})"
        row.label(text=status_text)
        
        if effector['connected']:
            # Кнопка отключения
            disconnect_op = row.operator("clonerpro.disconnect_field_from_effector", 
                                       text="Disconnect", icon='UNLINKED')
            disconnect_op.field_modifier_name = modifier.name
            disconnect_op.effector_modifier_name = effector['name']
        else:
            # Кнопка подключения
            connect_op = row.operator("clonerpro.connect_field_to_effector", 
                                    text="Connect", icon='LINKED')
            connect_op.field_modifier_name = modifier.name
            connect_op.effector_modifier_name = effector['name']


def draw_field_parameters(layout, modifier, field_type="SPHERE"):
    """
    Универсальная функция для отрисовки параметров поля через универсальную систему UI

    Args:
        layout: UI layout
        modifier: Модификатор geometry nodes
        field_type: Тип поля (SPHERE, BOX, etc.)
    """
    from .universal_ui import draw_field_ui_universal

    # Приводим тип поля к верхнему регистру для совместимости
    field_type_upper = field_type.upper() if field_type else "SPHERE"

    # Используем универсальную систему отрисовки
    draw_field_ui_universal(layout, modifier, field_type_upper)


def register_field_ui_properties():
    """Динамическая регистрация свойств для UI состояний всех полей"""
    from ...core.registry.field_registry import list_field_types, get_field_class

    # Регистрируем свойства для каждого типа поля из реестра
    field_types = list_field_types()

    for field_type in field_types:
        try:
            # Получаем класс поля для определения групп параметров
            field_class = get_field_class(field_type)
            if not field_class:
                continue

            field_instance = field_class()
            parameter_groups = field_instance.get_parameter_groups()

            # Создаем свойства для каждой группы параметров
            for group_name in parameter_groups.keys():
                prop_name = f"cloner_pro_{field_type.lower()}_field_{group_name.lower().replace(' ', '_')}"

                # Проверяем, что свойство еще не существует
                if not hasattr(bpy.types.Scene, prop_name):
                    setattr(bpy.types.Scene, prop_name, bpy.props.BoolProperty(
                        name=group_name,
                        description=f"Show/hide {group_name.lower()} for {field_type} field",
                        default=True
                    ))
                    print(f"✅ [FIELD_UI] Registered UI property: {prop_name}")

        except Exception as e:
            print(f"⚠️ [FIELD_UI] Error registering UI properties for {field_type}: {e}")
            continue


def unregister_field_ui_properties():
    """Динамическая отмена регистрации свойств UI для всех полей"""
    from ...core.registry.field_registry import list_field_types, get_field_class

    # Удаляем свойства для каждого типа поля из реестра
    field_types = list_field_types()

    for field_type in field_types:
        try:
            # Получаем класс поля для определения групп параметров
            field_class = get_field_class(field_type)
            if not field_class:
                continue

            field_instance = field_class()
            parameter_groups = field_instance.get_parameter_groups()

            # Удаляем свойства для каждой группы параметров
            for group_name in parameter_groups.keys():
                prop_name = f"cloner_pro_{field_type.lower()}_field_{group_name.lower().replace(' ', '_')}"

                if hasattr(bpy.types.Scene, prop_name):
                    delattr(bpy.types.Scene, prop_name)
                    print(f"✅ [FIELD_UI] Unregistered UI property: {prop_name}")

        except Exception as e:
            print(f"⚠️ [FIELD_UI] Error unregistering UI properties for {field_type}: {e}")
            continue