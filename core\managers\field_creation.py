"""
Field Creation Manager для ClonerPro
Унифицированная система создания полей через реестр
"""

import bpy
from typing import Tuple, Optional


def create_field_for_object(context, field_type="SPHERE") -> Tuple[Optional[bpy.types.NodesModifier], Optional[str]]:
    """
    Универсальное создание поля для активного объекта через реестр
    
    Args:
        context: Blender context
        field_type: Тип поля из реестра
        
    Returns:
        Tuple[modifier, message]: Модификатор поля и сообщение (если есть)
    """
    obj = context.active_object

    if not obj:
        return None, "No active object selected"

    # Проверяем наличие эффекторов для информации
    has_effectors = check_object_has_effectors(obj)
    warning_msg = None

    if not has_effectors:
        warning_msg = "This object has no effectors. Fields work best with effectors."

    # Создаем поле в зависимости от типа
    modifier, error = create_field_modifier_via_registry(context, obj, field_type)
    
    if error:
        return None, error
    
    if not has_effectors and modifier:
        # Предупреждение, но не ошибка
        return modifier, warning_msg
    
    return modifier, None


def check_object_has_effectors(obj) -> bool:
    """
    Проверить, есть ли эффекторы на объекте
    """
    has_effector = False

    for mod in obj.modifiers:
        if mod.type == 'NODES' and mod.node_group:
            # Проверяем метаданные node_group
            if (mod.node_group.get("component_type") == 'EFFECTOR' or
                'effector' in mod.node_group.name.lower()):
                has_effector = True
                break

            # Альтернативная проверка по имени
            node_group_name = mod.node_group.name.lower()
            if ('effector' in node_group_name or 'random' in node_group_name or 
                'noise' in node_group_name or 'step' in node_group_name):
                has_effector = True
                break

    return has_effector


def create_field_modifier_via_registry(context, obj, field_type) -> Tuple[Optional[bpy.types.NodesModifier], Optional[str]]:
    """
    Создание модификатора поля через реестр
    
    Args:
        context: Blender context
        obj: Объект для добавления модификатора
        field_type: Тип поля
        
    Returns:
        Tuple[modifier, error]: Модификатор и ошибка (если есть)
    """
    try:
        from ..registry.field_registry import create_field_via_registry, get_field_info
        
        # Получаем информацию о поле
        field_info = get_field_info(field_type)
        if not field_info:
            return None, f"Unknown field type: {field_type}"
        
        # Создаем node group через реестр
        field_group_name = f"{field_type}FieldModifier"
        if field_group_name not in bpy.data.node_groups:
            field_group = create_field_via_registry(field_type, "Modifier")
            if not field_group:
                return None, f"Failed to create {field_type} field node group"
            field_group.name = field_group_name
        else:
            field_group = bpy.data.node_groups[field_group_name]
        
        # Добавляем модификатор
        display_name = field_info.get('display_name', field_type.title())
        modifier_name = f"{display_name.replace(' ', '')}"
        
        field_modifier = obj.modifiers.new(name=modifier_name, type='NODES')
        field_modifier.node_group = field_group
        
        # Устанавливаем значения по умолчанию из класса поля
        _set_field_default_values(field_modifier, field_type)
        
        # Позиционируем модификатор правильно
        _position_field_modifier(obj, field_modifier)
        
        print(f"✅ [FIELD_CREATION] Created {field_type} field modifier: {modifier_name}")
        return field_modifier, None
        
    except Exception as e:
        print(f"❌ [FIELD_CREATION] Error creating {field_type} field: {e}")
        import traceback
        traceback.print_exc()
        return None, str(e)


def _set_field_default_values(modifier, field_type):
    """Установка значений по умолчанию для поля"""
    try:
        from ..registry.field_registry import get_field_class
        
        field_class = get_field_class(field_type)
        if field_class:
            field_instance = field_class()
            defaults = field_instance.get_default_parameters()
            
            # Базовые значения для всех полей
            base_defaults = {
                "Enable": True,
                "Inner Strength": 1.0,
                "Outer Strength": 0.0
            }
            
            # Объединяем с специфичными значениями
            all_defaults = {**base_defaults, **defaults}
            
            for param_name, default_value in all_defaults.items():
                if param_name in modifier:
                    try:
                        modifier[param_name] = default_value
                    except Exception as e:
                        print(f"Warning: Could not set {param_name}: {e}")
                        
    except Exception as e:
        print(f"Warning: Could not set default values for {field_type}: {e}")


def _position_field_modifier(obj, field_modifier):
    """
    Позиционирование модификатора поля
    Поля должны быть отдельными модификаторами, не влияющими на порядок других
    """
    try:
        # Поля могут быть в любом месте стека, так как они создают Named Attribute
        # Но для удобства размещаем их в конце
        print(f"[FIELD_POSITIONING] Field {field_modifier.name} positioned at end of stack")
        
    except Exception as e:
        print(f"[FIELD_POSITIONING] Error positioning field modifier: {e}")


# Совместимость с существующим кодом
def create_sphere_field_for_object(context):
    """Совместимость: создание сферического поля"""
    return create_field_for_object(context, "SPHERE")
