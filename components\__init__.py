"""
Components Package

Содержит все реализации клонеров, эффекторов и филдов.
"""

from . import cloners
from . import effectors  
from . import fields

__all__ = ['cloners', 'effectors', 'fields']

def register():
    """Register component implementations"""
    if hasattr(cloners, 'register'):
        cloners.register()

    if hasattr(effectors, 'register'):
        effectors.register()

    if hasattr(fields, 'register'):
        fields.register()

def unregister():
    """Unregister component implementations"""
    if hasattr(fields, 'unregister'):
        fields.unregister()

    if hasattr(effectors, 'unregister'):
        effectors.unregister()

    if hasattr(cloners, 'unregister'):
        cloners.unregister()