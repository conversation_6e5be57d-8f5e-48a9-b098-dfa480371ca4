"""
Unified Cloner Registry для ClonerPro
Единый реестр всех клонеров в новой классовой архитектуре
"""

# Импорты новых классов клонеров
from ...components.cloners.grid import GridCloner
from ...components.cloners.object import ObjectCloner
from ...components.cloners.linear import LinearCloner
from ...components.cloners.circle import CircleCloner
from ...components.cloners.spline import SplineCloner
from ...components.cloners.spiral import SpiralCloner

# Единый реестр клонеров - замещает старые CLONER_LOGIC_REGISTRY и MESH_CLONER_REGISTRY
CLONER_REGISTRY = {
    "GRID": GridCloner,
    "OBJECT": ObjectCloner,
    "LINEAR": LinearCloner,
    "CIRCLE": <PERSON>Cloner,
    "SPLINE": <PERSON><PERSON><PERSON><PERSON>loner,
    "SPIRAL": <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
}


def get_cloner_class(cloner_type):
    """
    Получить класс клонера по типу
    
    Args:
        cloner_type: Тип клонера ("GRID", "LINEAR", etc.)
        
    Returns:
        BaseCloner: Класс клонера или None если не найден
    """
    cloner_class = CLONER_REGISTRY.get(cloner_type)
    if not cloner_class:
        print(f"[ERROR] Cloner type {cloner_type} not found in registry")
        return None
    return cloner_class


def create_cloner_instance(cloner_type):
    """
    Создать экземпляр клонера по типу
    
    Args:
        cloner_type: Тип клонера
        
    Returns:
        BaseCloner: Экземпляр клонера или None
    """
    cloner_class = get_cloner_class(cloner_type)
    if not cloner_class:
        return None
    
    try:
        return cloner_class()
    except Exception as e:
        print(f"[ERROR] Failed to create {cloner_type} cloner instance: {e}")
        return None


def get_all_cloner_types():
    """
    Получить список всех доступных типов клонеров
    
    Returns:
        list: Список строк с типами клонеров
    """
    return list(CLONER_REGISTRY.keys())


def is_cloner_type_registered(cloner_type):
    """
    Проверить, зарегистрирован ли тип клонера
    
    Args:
        cloner_type: Тип клонера для проверки
        
    Returns:
        bool: True если клонер зарегистрирован
    """
    return cloner_type in CLONER_REGISTRY


def register_cloner(cloner_type, cloner_class):
    """
    Зарегистрировать новый тип клонера
    
    Args:
        cloner_type: Уникальный идентификатор типа
        cloner_class: Класс клонера (наследник BaseCloner)
    """
    if cloner_type in CLONER_REGISTRY:
        print(f"[WARNING] Cloner type {cloner_type} already registered, overwriting")
    
    CLONER_REGISTRY[cloner_type] = cloner_class
    print(f"✅ Registered cloner: {cloner_type} -> {cloner_class.__name__}")


def unregister_cloner(cloner_type):
    """
    Отменить регистрацию клонера
    
    Args:
        cloner_type: Тип клонера для удаления
    """
    if cloner_type in CLONER_REGISTRY:
        del CLONER_REGISTRY[cloner_type]
        print(f"✅ Unregistered cloner: {cloner_type}")
    else:
        print(f"[WARNING] Cloner type {cloner_type} not found for unregistration")


def list_cloner_types():
    """
    Получить список всех зарегистрированных типов клонеров

    Returns:
        list: Список типов клонеров
    """
    return list(CLONER_REGISTRY.keys())


def get_registry_info():
    """
    Получить информацию о текущем состоянии реестра

    Returns:
        dict: Информация о зарегистрированных клонерах
    """
    return {
        "total_cloners": len(CLONER_REGISTRY),
        "cloner_types": list(CLONER_REGISTRY.keys()),
        "cloner_classes": {k: v.__name__ for k, v in CLONER_REGISTRY.items()}
    }


# Функция для вывода информации о реестре (для отладки)
def print_registry_info():
    """Вывести информацию о реестре в консоль"""
    info = get_registry_info()
    print(f"=== CLONER REGISTRY INFO ===")
    print(f"Total cloners: {info['total_cloners']}")
    print(f"Registered types: {info['cloner_types']}")
    for cloner_type, class_name in info['cloner_classes'].items():
        print(f"  {cloner_type} -> {class_name}")
    print(f"============================")