"""
Операторы создания компонентов
"""

import bpy
from bpy.types import Operator
from bpy.props import StringProperty
from ...core.system.settings import get_settings
from ...core.managers.cloner_modes import create_cloner_in_mode
from ...core.managers.effector_creation import create_effector_for_object
from ...core.registry import main_registry, CLONER_REGISTRY


class CLONERPRO_OT_create_cloner(Operator):
    """Создать клонер"""
    bl_idname = "clonerpro.create_cloner"
    bl_label = "Create Cloner"
    bl_description = "Create a new cloner"
    bl_options = {'REGISTER', 'UNDO'}
    
    component_type: StringProperty(
        name="Cloner Type",
        description="Type of cloner to create",
        default="GRID"
    )

    def execute(self, context):
        settings = get_settings()
        mode = settings.creation_mode
        
        # Check scene properties for stacking and anti-recursion
        use_stacked = getattr(context.scene, 'use_stacked_modifiers', False)
        use_mesh_stacked = getattr(context.scene, 'use_mesh_stacked_modifiers', False)
        use_anti_recursion = getattr(context.scene, 'use_anti_recursion', True)
        
        # Override mode if stacking is enabled for Object mode (только для базовых клонеров)
        if mode == 'OBJECT' and use_stacked and self.component_type != "OBJECT":
            mode = 'STACKED'
        
        # Для Object клонера: Mesh Stacking НЕ меняет mode, но влияет на способ создания
        if self.component_type == "OBJECT" and mode == 'OBJECT' and use_mesh_stacked:
            print(f"🔧 [MESH_STACKING] Object Cloner OBJECT mode with Mesh Stacking enabled")
        
        # Проверка активного объекта для Object и Stacked режимов
        if mode in ['OBJECT', 'STACKED'] and not context.active_object:
            self.report({'ERROR'}, "No active object selected")
            return {'CANCELLED'}
        
        # Специальная проверка для Spline клонера - должен быть кривой/сплайном
        if self.component_type == "SPLINE":
            if not context.active_object:
                self.report({'ERROR'}, "No active object selected")
                return {'CANCELLED'}
            if context.active_object.type not in ['CURVE', 'SURFACE', 'FONT']:
                self.report({'ERROR'}, "Spline cloner can only be created on curves/splines")
                return {'CANCELLED'}
        
        # Используем новую registry систему для создания клонеров
        result = self._create_cloner_via_registry(context, self.component_type, mode, use_anti_recursion, use_mesh_stacked)
        
        if result:
            # НОВАЯ ЛОГИКА: После создания клонера добавляем отдельный модификатор Realize Instances если нужно
            if use_anti_recursion:
                self._add_realize_instances_modifier_if_needed(context, result)
            
            self.report({'INFO'}, f"Created {self.component_type} cloner in {mode} mode")
        else:
            self.report({'WARNING'}, f"Failed to create {self.component_type} cloner")
        
        return {'FINISHED'}

    def _add_realize_instances_modifier_if_needed(self, context, result):
        """
        Добавляет отдельный модификатор Realize Instances после создания клонера
        """
        try:
            from ...core.templates.realize_instances_modifier import add_realize_instances_modifier_after_cloners
            
            # Определяем объект для добавления модификатора
            target_obj = None
            
            if hasattr(result, 'type') and hasattr(result, 'name'):
                # Результат - объект клонера
                target_obj = result
            elif hasattr(result, 'id_data'):
                # Результат - модификатор, получаем объект
                target_obj = result.id_data
            elif context.active_object:
                # Fallback - активный объект
                target_obj = context.active_object
            
            if target_obj:
                modifier = add_realize_instances_modifier_after_cloners(target_obj)
                if modifier:
                    print(f"✅ Added Realize Instances modifier to {target_obj.name}")
                else:
                    print(f"⚠️ Failed to add Realize Instances modifier to {target_obj.name}")
            else:
                print(f"⚠️ No target object found for Realize Instances modifier")
                
        except Exception as e:
            print(f"❌ Error adding Realize Instances modifier: {e}")

    def _create_cloner_via_registry(self, context, component_type, mode, use_anti_recursion, use_mesh_stacked=False):
        """
        Универсальное создание клонера через registry систему
        Заменяет старую логику if/elif/else
        """
        print(f"🔧 [REGISTRY] Creating {component_type} cloner in {mode} mode")
        
        # НОВОЕ: Проверяем новую классовую архитектуру первым приоритетом
        from ...core.registry.cloner_registry import is_cloner_type_registered
        if is_cloner_type_registered(component_type):
            print(f"🚀 [REGISTRY] Using class-based architecture for {component_type}")
            return self._create_cloner_via_class_registry(context, component_type, mode, use_anti_recursion, use_mesh_stacked)
        
        # Получаем класс клонера из нового registry
        cloner_class = main_registry.get_cloner(component_type)

        if not cloner_class:
            print(f"⚠️ [REGISTRY] No cloner class found for {component_type}")
            return None
        
        # Используем новую классовую систему
        try:
            active = context.active_object
            if mode in ['OBJECT', 'STACKED'] and not active:
                print(f"⚠️ [REGISTRY] Active object required for {mode} mode")
                return None

            # Создаем экземпляр клонера
            cloner_instance = cloner_class()

            # Создаем клонер через новую систему
            if mode == 'OBJECT':
                from ...core.templates.cloner_creation import create_cloner_unified
                result = create_cloner_unified(component_type, mode, active, use_anti_recursion)
            elif mode == 'STACKED':
                from ...core.templates.cloner_creation import create_cloner_unified
                result = create_cloner_unified(component_type, mode, active, use_anti_recursion, mesh_stacked=use_mesh_stacked)
            elif mode == 'COLLECTION':
                # Для collection mode получаем название коллекции из настроек
                settings = get_settings()
                collection_name = settings.collection_name

                # Проверяем, что коллекция выбрана и существует
                if not collection_name:
                    print(f"⚠️ [REGISTRY] No collection selected for COLLECTION mode")
                    return None

                if collection_name not in bpy.data.collections:
                    print(f"⚠️ [REGISTRY] Collection '{collection_name}' not found")
                    return None

                from ...core.templates.cloner_creation import create_cloner_unified
                result = create_cloner_unified(component_type, mode, collection_name, use_anti_recursion)
            else:
                print(f"⚠️ [REGISTRY] Unsupported mode: {mode}")
                return None

            print(f"✅ [REGISTRY] Successfully created {component_type} cloner via new registry")
            return result

        except Exception as e:
            print(f"❌ [REGISTRY] Error creating {component_type} cloner: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _create_cloner_via_class_registry(self, context, component_type, mode, use_anti_recursion, use_mesh_stacked=False):
        """
        Создание клонера через новую классовую архитектуру
        """
        try:
            from ...core.templates.cloner_creation import create_cloner_unified
            
            active = context.active_object
            if mode in ['OBJECT', 'STACKED'] and not active:
                print(f"⚠️ [CLASS] Active object required for {mode} mode")
                return None
            
            # Определяем source в зависимости от режима
            if mode == 'OBJECT':
                source = active
                result = create_cloner_unified(component_type, mode, source, use_anti_recursion, mesh_stacked=use_mesh_stacked)
            elif mode == 'STACKED':
                source = active
                result = create_cloner_unified(component_type, mode, source, use_anti_recursion, mesh_stacked=use_mesh_stacked)
            elif mode == 'COLLECTION':
                # ИСПРАВЛЕНИЕ: Mesh клонеры (OBJECT/SPLINE) в COLLECTION режиме НЕ требуют предварительно выбранную коллекцию
                if component_type in ["OBJECT", "SPLINE"]:
                    # Mesh клонеры создаются без предварительной коллекции, коллекция выбирается в UI
                    # ВАЖНО: Передаем информацию о mesh stacking для правильного создания
                    result = create_cloner_unified(component_type, mode, None, use_anti_recursion, mesh_stacked=use_mesh_stacked)
                else:
                    # Для unified клонеров (GRID/LINEAR/CIRCLE) требуется коллекция из настроек
                    settings = get_settings()
                    collection_name = settings.collection_name
                    
                    if not collection_name:
                        print(f"⚠️ [CLASS] No collection selected for COLLECTION mode")
                        return None
                    
                    if collection_name not in bpy.data.collections:
                        print(f"⚠️ [CLASS] Collection '{collection_name}' not found")
                        return None
                    
                    result = create_cloner_unified(component_type, mode, collection_name, use_anti_recursion)
            else:
                print(f"⚠️ [CLASS] Unsupported mode: {mode}")
                return None
            
            if result:
                print(f"✅ [CLASS] Successfully created {component_type} cloner via class architecture")
                return True
            else:
                print(f"❌ [CLASS] Failed to create {component_type} cloner")
                return False
                
        except Exception as e:
            print(f"❌ [CLASS] Error creating {component_type} cloner: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _create_object_cloner(self, context, mode, use_mesh_stacked=False):
        """Создание ObjectCloner через mesh-based систему с правильным разделением режимов"""
        from ...core.templates.cloner_creation import create_mesh_cloner
        from ...components.cloners.object import object_cloner
        
        active = context.active_object
        if not active:
            return None
        
        try:
            if mode == 'STACKED':
                # Stacked mode - модификатор прямо на оригинальном объекте (как в ObjectCloner_Standalone)
                modifier = self._create_direct_object_cloner_modifier(active, source_obj=None)
                return modifier is not None
                
            elif mode == 'OBJECT':
                if use_mesh_stacked:
                    # Mesh Stacking mode - модификатор прямо на оригинальном объекте (как STACKED)
                    modifier = self._create_direct_object_cloner_modifier(active, source_obj=None)
                    return modifier is not None
                else:
                    # Object mode - создаем через mesh-based систему (ClonerTo с копией)
                    modifier = create_mesh_cloner(
                        cloner_type="OBJECT", 
                        mode="STACKED",  # Внутри mesh система использует STACKED
                        target_obj=active
                    )
                    return modifier is not None
                
            elif mode == 'COLLECTION':
                if use_mesh_stacked:
                    # Mesh Stacking + Collection mode - модификатор на оригинальном объекте в Collection режиме
                    modifier = self._create_direct_collection_cloner_modifier(active, source_collection=None)
                    return modifier is not None
                else:
                    # Collection mode - через mesh-based систему без предустановленной коллекции
                    modifier = create_mesh_cloner(
                        cloner_type="OBJECT",
                        mode="COLLECTION", 
                        target_obj=active,
                        collection_name=None  # Коллекцию пользователь выберет позже в UI
                    )
                    return modifier is not None
            
            return False
        except Exception as e:
            print(f"[ERROR] Failed to create ObjectCloner: {e}")
            return False

    def _create_direct_object_cloner_modifier(self, target_obj, source_obj=None):
        """Создает Object Cloner модификатор прямо на target_obj БЕЗ копирования - точная копия из BACKUP"""
        from ...components.cloners.object import object_cloner
        
        if not target_obj:
            return None
        
        try:
            # Создаем node group с вычислением edge normals
            node_group, socket_mapping = object_cloner.create_node_group(mode="STACKED", target_obj=target_obj)
            if not node_group:
                print(f"[ERROR] Failed to create Object Cloner node group")
                return None
            
            # Добавляем модификатор прямо на target_obj
            modifier = target_obj.modifiers.new(name="ObjectCloner", type='NODES')
            modifier.node_group = node_group
            
            # Устанавливаем метаданные
            modifier["cloner_type"] = "OBJECT"
            modifier["cloner_mode"] = "OBJECT"
            modifier["is_objectcloner"] = True
            modifier["source_type"] = "OBJECT"
            
            # Настраиваем Source Mode = Object
            if "Source Mode" in socket_mapping:
                modifier[socket_mapping["Source Mode"]] = 0  # Object mode
            
            # Устанавливаем source object если есть
            if source_obj and "Source Object" in socket_mapping:
                modifier[socket_mapping["Source Object"]] = source_obj
            
            # Устанавливаем дефолтные параметры
            self._set_cloner_default_parameters(modifier, socket_mapping, mode="OBJECT")
            
            return modifier
            
        except Exception as e:
            print(f"[ERROR] Failed to create direct Object Cloner modifier: {e}")
            return None

    def _create_direct_collection_cloner_modifier(self, target_obj, source_collection=None):
        """Создает Collection Cloner модификатор прямо на target_obj БЕЗ копирования"""
        from ...components.cloners.object import object_cloner
        
        if not target_obj:
            return None
        
        try:
            # Создаем node group с вычислением edge normals
            node_group, socket_mapping = object_cloner.create_node_group(mode="STACKED", target_obj=target_obj)
            if not node_group:
                print(f"[ERROR] Failed to create Collection Cloner node group")
                return None
            
            # Добавляем модификатор прямо на target_obj
            modifier = target_obj.modifiers.new(name="CollectionCloner", type='NODES')
            modifier.node_group = node_group
            
            # Устанавливаем метаданные
            modifier["cloner_type"] = "OBJECT"
            modifier["cloner_mode"] = "COLLECTION"
            modifier["is_objectcloner"] = True
            modifier["source_type"] = "COLLECTION"
            
            # Настраиваем Source Mode = Collection
            if "Source Mode" in socket_mapping:
                modifier[socket_mapping["Source Mode"]] = 1  # Collection mode
            
            # Устанавливаем source collection если есть
            if source_collection and "Source Collection" in socket_mapping:
                modifier[socket_mapping["Source Collection"]] = source_collection
                
                # Автоматически устанавливаем количество объектов в коллекции
                if hasattr(source_collection, 'objects'):
                    object_count = len(source_collection.objects)
                    if "Collection Object Count" in socket_mapping:
                        modifier[socket_mapping["Collection Object Count"]] = object_count
            
            # Устанавливаем дефолтные параметры
            self._set_cloner_default_parameters(modifier, socket_mapping, mode="COLLECTION")
            
            return modifier
            
        except Exception as e:
            print(f"[ERROR] Failed to create direct Collection Cloner modifier: {e}")
            return None

    def _set_cloner_default_parameters(self, modifier, socket_mapping, mode="OBJECT"):
        """Устанавливает дефолтные параметры клонера через socket mapping"""
        
        # Определяем Source Mode в зависимости от режима
        source_mode = 1 if mode == "COLLECTION" else 0
        
        defaults = {
            "Source Mode": source_mode,  # 0=Object, 1=Collection
            "Distribution Mode": 2,  # Faces by default
            "Instance Count": 50,
            "Density": 1.0,
            "Hide Original": False,
            "Instance Scale": (1.0, 1.0, 1.0),
            "Instance Rotation": (0.0, 0.0, 0.0),
            "Random Position": (0.0, 0.0, 0.0),
            "Random Rotation": (0.0, 0.0, 0.0),
            "Random Scale": 0.0,
            "Uniform Scale": False,
            "Random Seed": 0,
            "Collection Random Seed": 2,
            "Offset": 0.0,
            "Face Center Mode": False,
            "Align to Normal": False,
            "Collection Pick Instance": True,
            "Collection Instance Index": 0,
            "Collection Object Count": 3
        }
        
        # Устанавливаем параметры через socket mapping
        for param_name, default_value in defaults.items():
            if param_name in socket_mapping:
                modifier[socket_mapping[param_name]] = default_value

    def _create_spline_cloner(self, context, mode, use_mesh_stacked=False):
        """Создание SplineCloner через mesh-based систему"""
        active_spline = context.active_object
        if not active_spline:
            return None
        
        try:
            # Spline Cloner теперь использует mesh-based creation систему
            from ...core.templates.cloner_creation import create_mesh_cloner
            
            if mode == 'COLLECTION':
                # Collection mode
                modifier = create_mesh_cloner(
                    cloner_type="SPLINE",
                    mode="COLLECTION",
                    target_obj=active_spline
                )
            else:
                # Object/Stacked modes
                modifier = create_mesh_cloner(
                    cloner_type="SPLINE",
                    mode="STACKED",
                    target_obj=active_spline
                )
            
            return modifier is not None
            
        except Exception as e:
            print(f"[ERROR] Failed to create SplineCloner: {e}")
            return False


class CLONERPRO_OT_create_effector(Operator):
    """Создать эффектор"""
    bl_idname = "clonerpro.create_effector"
    bl_label = "Create Effector"
    bl_description = "Create a new effector"
    bl_options = {'REGISTER', 'UNDO'}
    
    component_type: StringProperty(
        name="Effector Type",
        description="Type of effector to create",
        default="NOISE"
    )

    def execute(self, context):
        # Проверяем активный объект
        if not context.active_object:
            self.report({'ERROR'}, "No active object selected")
            return {'CANCELLED'}
        
        try:
            # Используем новую registry систему для создания эффектора
            result = self._create_effector_via_registry(context, self.component_type)

            if result:
                self.report({'INFO'}, f"Created {self.component_type} effector")
                return {'FINISHED'}
            else:
                self.report({'WARNING'}, f"Failed to create {self.component_type} effector")
                return {'CANCELLED'}
                
        except Exception as e:
            self.report({'ERROR'}, f"Failed to create effector: {str(e)}")
            return {'CANCELLED'}

    def _create_effector_via_registry(self, context, component_type):
        """
        Универсальное создание эффектора через registry систему
        """
        print(f"🔧 [REGISTRY] Creating {component_type} effector")

        try:
            # Используем правильную функцию создания эффектора
            from ...core.managers.effector_creation import create_effector_for_object

            # Создаем эффектор для активного объекта
            modifier, message = create_effector_for_object(context, component_type)

            if modifier:
                if message:
                    # Это предупреждение, а не ошибка
                    print(f"⚠️ [REGISTRY] Warning for {component_type} effector: {message}")
                print(f"✅ [REGISTRY] Successfully created {component_type} effector via registry")
                return True
            else:
                # Это настоящая ошибка
                error_msg = message or "Failed to create effector - no modifier returned"
                print(f"❌ [REGISTRY] Error creating {component_type} effector: {error_msg}")
                return None

        except Exception as e:
            print(f"❌ [REGISTRY] Error creating {component_type} effector: {e}")
            import traceback
            traceback.print_exc()
            return None


# CLONERPRO_OT_create_field moved to field_creation.py


class CLONERPRO_OT_create_component(Operator):
    """Универсальный оператор создания компонентов"""
    bl_idname = "clonerpro.create_component"
    bl_label = "Create Component"
    bl_description = "Create a new component"
    bl_options = {'REGISTER', 'UNDO'}
    
    component_type: StringProperty(
        name="Component Type",
        description="Type of component to create",
        default=""
    )

    def execute(self, context):
        self.report({'INFO'}, f"Created {self.component_type} component (Generic)")
        return {'FINISHED'}


# Оператор переключения режима создания
class CLONERPRO_OT_set_creation_mode(bpy.types.Operator):
    """Установить режим создания"""
    bl_idname = "clonerpro.set_creation_mode"
    bl_label = "Set Creation Mode"
    bl_description = "Set cloner creation mode"
    bl_options = {'REGISTER'}
    
    mode: bpy.props.StringProperty(
        name="Mode",
        description="Creation mode",
        default="OBJECT"
    )

    def execute(self, context):
        settings = get_settings()
        settings.creation_mode = self.mode
        self.report({'INFO'}, f"Creation mode set to {self.mode}")
        return {'FINISHED'}


def register():
    """Регистрация операторов создания"""
    bpy.utils.register_class(CLONERPRO_OT_create_cloner)
    bpy.utils.register_class(CLONERPRO_OT_create_effector)
    # CLONERPRO_OT_create_field registered in field_creation.py
    bpy.utils.register_class(CLONERPRO_OT_create_component)
    bpy.utils.register_class(CLONERPRO_OT_set_creation_mode)


def unregister():
    """Отмена регистрации операторов создания"""
    bpy.utils.unregister_class(CLONERPRO_OT_set_creation_mode)
    bpy.utils.unregister_class(CLONERPRO_OT_create_component)
    # CLONERPRO_OT_create_field unregistered in field_creation.py
    bpy.utils.unregister_class(CLONERPRO_OT_create_effector)
    bpy.utils.unregister_class(CLONERPRO_OT_create_cloner)