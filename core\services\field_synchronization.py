"""
Field Synchronization Service for ClonerPro
Links field modifier parameters to embedded field node groups in effectors
"""

import bpy
from mathutils import Vector
from typing import Dict, List, Optional, Tuple


class FieldSynchronizationService:
    """
    Service for synchronizing field parameters between modifiers and embedded node groups

    Responsibilities:
    - Register connections between field modifiers and effector field nodes
    - Create parameter drivers for real-time synchronization
    - Sync field parameters without drivers (direct updates)
    - Detect field types and create field node groups for effectors
    """

    def __init__(self):
        self.field_connections: Dict[str, List[Dict]] = {}
        self.update_handlers = []

    # =============================================================================
    # CONNECTION MANAGEMENT
    # =============================================================================
    
    def register_field_connection(self, field_modifier_id: str, effector_object: bpy.types.Object, 
                                 effector_modifier: bpy.types.NodesModifier, field_node_name: str):
        """Register a connection between field modifier and embedded field node group"""
        if field_modifier_id not in self.field_connections:
            self.field_connections[field_modifier_id] = []
        
        connection = {
            'effector_object': effector_object,
            'effector_modifier': effector_modifier,
            'field_node_name': field_node_name
        }
        
        self.field_connections[field_modifier_id].append(connection)
        print(f"Registered field connection: {field_modifier_id} -> {effector_object.name}.{effector_modifier.name}")

    # =============================================================================
    # DRIVER CREATION AND MANAGEMENT
    # =============================================================================

    def create_field_parameter_drivers(self, field_object: bpy.types.Object,
                                     field_modifier: bpy.types.NodesModifier,
                                     field_type: str = "SPHERE"):
        """Create drivers to link field modifier parameters to embedded field node groups"""
        
        print(f"[DRIVER] Starting driver creation for field {field_modifier.name}")
        
        if not field_object or not field_modifier:
            print(f"[DRIVER] Missing field object or modifier")
            return False
        
        field_modifier_id = self.get_field_modifier_id(field_object, field_modifier)
        print(f"[DRIVER] Field modifier ID: {field_modifier_id}")
        
        if field_modifier_id not in self.field_connections:
            print(f"[DRIVER] No connections found for field {field_modifier_id}")
            print(f"[DRIVER] Available connections: {list(self.field_connections.keys())}")
            return False
        
        # Get field parameter configuration
        field_params = self.get_field_parameter_config(field_type)
        print(f"[DRIVER] Field parameters to sync: {list(field_params.keys())}")
        
        # Create drivers for each connected effector
        connection_count = len(self.field_connections[field_modifier_id])
        print(f"[DRIVER] Creating drivers for {connection_count} connections")
        
        for i, connection in enumerate(self.field_connections[field_modifier_id]):
            print(f"[DRIVER] Processing connection {i+1}/{connection_count}")
            self.create_drivers_for_connection(field_object, field_modifier, connection, field_params)
        
        return True
    
    def create_drivers_for_connection(self, field_object: bpy.types.Object, 
                                    field_modifier: bpy.types.NodesModifier,
                                    connection: Dict, field_params: Dict):
        """Create drivers for a specific field-effector connection"""
        
        effector_object = connection['effector_object']
        effector_modifier = connection['effector_modifier']
        field_node_name = connection['field_node_name']
        
        # Find the field node group within the effector
        if not effector_modifier.node_group:
            return
        
        field_node = None
        for node in effector_modifier.node_group.nodes:
            if node.name == field_node_name and node.type == 'GROUP':
                field_node = node
                break
        
        if not field_node:
            print(f"Field node {field_node_name} not found in effector {effector_modifier.name}")
            return
        
        # Create drivers for each parameter
        for param_name, param_info in field_params.items():
            socket_name = param_info.get('socket_name', param_name)
            
            # Check if parameter exists in both field modifier and field node
            if socket_name in field_modifier and hasattr(field_node, 'inputs'):
                field_node_input = None
                for input_socket in field_node.inputs:
                    if input_socket.name == socket_name:
                        field_node_input = input_socket
                        break
                
                if field_node_input:
                    self.create_parameter_driver(field_object, field_modifier, socket_name,
                                               field_node_input, param_info)
    
    def create_parameter_driver(self, field_object: bpy.types.Object, 
                              field_modifier: bpy.types.NodesModifier,
                              socket_name: str, target_socket, param_info: Dict):
        """Create a driver for a specific parameter"""
        
        try:
            # Find the actual socket identifier in the field modifier
            actual_socket_id = self.find_actual_socket_id(field_modifier, socket_name)
            
            if not actual_socket_id:
                print(f"Could not find socket ID for {socket_name} in field modifier")
                return
            
            print(f"Creating driver: {socket_name} -> {actual_socket_id}")
            
            # Clear existing drivers first
            self.clear_socket_drivers(target_socket)
            
            # Determine the target property path
            if param_info.get('type') == 'VECTOR':
                # Vector properties need special handling
                for i in range(3):
                    if hasattr(target_socket, 'default_value') and len(target_socket.default_value) > i:
                        driver = target_socket.driver_add("default_value", i)
                        driver.driver.type = 'SCRIPTED'
                        
                        # Add variable for source property
                        var = driver.driver.variables.new()
                        var.name = "field_param"
                        var.type = 'SINGLE_PROP'
                        var.targets[0].id = field_object
                        var.targets[0].data_path = f'modifiers["{field_modifier.name}"]["{actual_socket_id}"][{i}]'
                        
                        # Set driver expression
                        driver.driver.expression = "field_param"
                        
                        print(f"Created vector driver for {socket_name}[{i}]")
                    
            else:
                # Scalar properties
                if hasattr(target_socket, 'default_value'):
                    driver = target_socket.driver_add("default_value")
                    driver.driver.type = 'SCRIPTED'
                    
                    # Add variable for source property
                    var = driver.driver.variables.new()
                    var.name = "field_param"
                    var.type = 'SINGLE_PROP'
                    var.targets[0].id = field_object
                    var.targets[0].data_path = f'modifiers["{field_modifier.name}"]["{actual_socket_id}"]'
                    
                    # Set driver expression
                    driver.driver.expression = "field_param"
                    
                    print(f"Created scalar driver for {socket_name}")
                else:
                    print(f"Socket {socket_name} has no default_value property")
            
            print(f"Created driver for parameter: {socket_name} (using {actual_socket_id})")
            
        except Exception as e:
            print(f"Failed to create driver for {socket_name}: {e}")
            import traceback
            traceback.print_exc()
    
    def clear_socket_drivers(self, socket):
        """Clear all existing drivers from a socket"""
        try:
            if hasattr(socket, 'default_value'):
                # Try to remove vector drivers (if it's a vector)
                if hasattr(socket.default_value, '__len__') and len(socket.default_value) > 1:
                    for i in range(len(socket.default_value)):
                        try:
                            socket.driver_remove("default_value", i)
                        except:
                            pass
                else:
                    # Try to remove scalar driver
                    try:
                        socket.driver_remove("default_value")
                    except:
                        pass
        except:
            pass

    # =============================================================================
    # UTILITY FUNCTIONS
    # =============================================================================

    def find_actual_socket_id(self, field_modifier: bpy.types.NodesModifier, socket_name: str) -> str:
        """Find the actual socket identifier for a named socket in field modifier"""
        
        if not field_modifier or not field_modifier.node_group:
            return None
        
        # Try exact name match first
        if socket_name in field_modifier:
            return socket_name
        
        # Search through node group interface for socket by name
        try:
            for socket in field_modifier.node_group.interface.items_tree:
                if (socket.item_type == 'SOCKET' and 
                    socket.in_out == 'INPUT' and 
                    socket.name == socket_name):
                    return socket.identifier
        except Exception as e:
            print(f"Error searching for socket {socket_name}: {e}")
        
        # Fallback: search through modifier keys for Socket_X pattern
        # Based on actual debug output from field creation
        socket_mapping = {
            "Enable": "Socket_1",     # Actually seems to be missing or Socket_1
            "Center": "Socket_2",     # From debug: Center -> Socket_2
            "Radius": "Socket_3",     # From debug: Radius -> Socket_3
            "Falloff": "Socket_4",    # From debug: Falloff -> Socket_4
            "Inner Strength": "Socket_5",  # Extrapolated
            "Outer Strength": "Socket_6"   # From debug: Outer Strength -> Socket_6
        }
        
        fallback_id = socket_mapping.get(socket_name)
        if fallback_id and fallback_id in field_modifier:
            print(f"Using fallback mapping {socket_name} -> {fallback_id}")
            return fallback_id
        
        print(f"Could not find socket ID for {socket_name}")
        print(f"Available modifier keys: {list(field_modifier.keys())}")
        return None
    
    def get_field_modifier_id(self, field_object: bpy.types.Object, 
                             field_modifier: bpy.types.NodesModifier) -> str:
        """Generate unique ID for field modifier"""
        return f"{field_object.name}.{field_modifier.name}"
    
    def get_field_parameter_config(self, field_type: str) -> Dict:
        """Get field parameter configuration for driver creation - updated for class-based architecture"""

        if field_type in ["SPHERE", "BOX"]:
            # Используем новую классовую архитектуру
            from ...core.registry.field_registry import get_field_class
            field_class = get_field_class(field_type)
            if field_class:
                field_instance = field_class()
                params = field_instance.get_parameter_groups()

                # Flatten the parameter structure
                flat_params = {}
                for group_name, group_params in params.items():
                    for param in group_params:
                        flat_params[param['name']] = param

                return flat_params

        return {}
    
    def cleanup_field_connections(self, field_object: bpy.types.Object, 
                                 field_modifier: bpy.types.NodesModifier):
        """Clean up field connections and drivers when field is deleted"""
        
        field_modifier_id = self.get_field_modifier_id(field_object, field_modifier)
        
        if field_modifier_id in self.field_connections:
            # Remove drivers for all connections
            for connection in self.field_connections[field_modifier_id]:
                self.remove_drivers_for_connection(connection)
            
            # Remove connection records
            del self.field_connections[field_modifier_id]
            print(f"Cleaned up field connections for {field_modifier_id}")
    
    def remove_drivers_for_connection(self, connection: Dict):
        """Remove drivers for a specific connection"""
        
        effector_modifier = connection['effector_modifier']
        field_node_name = connection['field_node_name']
        
        if not effector_modifier.node_group:
            return
        
        # Find field node
        field_node = None
        for node in effector_modifier.node_group.nodes:
            if node.name == field_node_name and node.type == 'GROUP':
                field_node = node
                break
        
        if not field_node:
            return
        
        # Remove drivers from field node inputs
        for input_socket in field_node.inputs:
            try:
                input_socket.driver_remove("default_value")
            except:
                pass  # Driver might not exist
    
    def update_field_connections(self, field_object: bpy.types.Object, 
                               field_modifier: bpy.types.NodesModifier):
        """Update field connections when effectors are added or removed"""
        
        field_modifier_id = self.get_field_modifier_id(field_object, field_modifier)
        
        # Find all effectors on the same object
        effectors = []
        for modifier in field_object.modifiers:
            if modifier.type == 'NODES' and modifier.node_group:
                node_group_name = modifier.node_group.name.lower()
                if 'effector' in node_group_name:
                    effectors.append(modifier)
        
        # Create new connections for effectors that don't have field nodes yet
        for effector_modifier in effectors:
            if self.needs_field_connection(effector_modifier, field_modifier_id):
                self.add_field_to_effector(field_object, field_modifier, effector_modifier)
    
    def needs_field_connection(self, effector_modifier: bpy.types.NodesModifier, field_modifier_id: str) -> bool:
        """Check if effector needs a field connection"""
        
        if not effector_modifier.node_group:
            return False
        
        # Check if field node already exists
        for node in effector_modifier.node_group.nodes:
            if node.name == "Field" and node.type == 'GROUP':
                return False
        
        return True

    # =============================================================================
    # FIELD CONNECTION TO EFFECTORS
    # =============================================================================

    def add_field_to_effector(self, field_object: bpy.types.Object,
                             field_modifier: bpy.types.NodesModifier,
                             effector_modifier: bpy.types.NodesModifier):
        """Add field node group to an effector - универсальная версия"""

        try:
            # Определяем тип поля
            field_type = self._detect_field_type_from_modifier(field_modifier)

            # Создаем соответствующий field node group
            field_node_group = self._create_field_node_group_for_effector(field_type)
            if not field_node_group:
                print(f"❌ Failed to create {field_type} field node group")
                return False
            
            # Add field node group to effector
            if not effector_modifier.node_group:
                return False
                
            nodes = effector_modifier.node_group.nodes
            links = effector_modifier.node_group.links
            
            # Add field node group
            field_node = nodes.new('GeometryNodeGroup')
            field_node.node_tree = field_node_group
            field_node.name = "Field"
            field_node.location = (-800, -800)
            
            # Connect to Field input if it exists
            group_input = None
            for node in nodes:
                if node.type == 'GROUP_INPUT':
                    group_input = node
                    break
            
            if group_input:
                # Find Field input socket
                for socket in group_input.outputs:
                    if socket.name == "Field":
                        # Find target node that uses Field input
                        for link in socket.links:
                            target_node = link.to_node
                            target_input = link.to_socket
                            
                            # Remove old connection and create new one
                            links.remove(link)
                            links.new(field_node.outputs['Field'], target_input)
                            break
                        break
            
            # Register the connection
            field_modifier_id = self.get_field_modifier_id(field_object, field_modifier)
            self.register_field_connection(field_modifier_id, field_object, effector_modifier, "Field")
            
            return True
            
        except Exception as e:
            print(f"Failed to add field to effector: {e}")
            return False

    # =============================================================================
    # REAL-TIME PARAMETER SYNCHRONIZATION
    # =============================================================================

    def sync_field_parameters_realtime(self, field_object: bpy.types.Object,
                                     field_modifier: bpy.types.NodesModifier):
        """Realtime synchronization of field parameters without recreating drivers"""
        
        field_modifier_id = self.get_field_modifier_id(field_object, field_modifier)
        
        if field_modifier_id not in self.field_connections:
            return
        
        # Определяем тип поля
        field_type = self._detect_field_type_from_modifier(field_modifier)

        # Get field parameter configuration
        field_params = self.get_field_parameter_config(field_type)
        
        # Update each connected effector
        for connection in self.field_connections[field_modifier_id]:
            self.update_field_node_parameters(field_object, field_modifier, connection, field_params)
    
    def update_field_node_parameters(self, field_object: bpy.types.Object, 
                                   field_modifier: bpy.types.NodesModifier,
                                   connection: Dict, field_params: Dict):
        """Update field node parameters directly without drivers"""
        
        effector_object = connection['effector_object']
        effector_modifier = connection['effector_modifier']
        field_node_name = connection['field_node_name']
        
        # Find the field node group within the effector
        if not effector_modifier.node_group:
            return
        
        field_node = None
        for node in effector_modifier.node_group.nodes:
            if node.name == field_node_name and node.type == 'GROUP':
                field_node = node
                break
        
        if not field_node:
            return
        
        # Check if field modifier is visible (show_viewport)
        field_visible = field_modifier.show_viewport
        
        # Update each parameter
        for param_name, param_info in field_params.items():
            socket_name = param_info.get('socket_name', param_name)
            
            # Get value from field modifier
            actual_socket_id = self.find_actual_socket_id(field_modifier, socket_name)
            if actual_socket_id and actual_socket_id in field_modifier:
                field_value = field_modifier[actual_socket_id]
                
                # Special handling for Enable: set to False if field is hidden
                if socket_name == "Enable" and not field_visible:
                    field_value = False
                
                # Find corresponding input in field node
                for input_socket in field_node.inputs:
                    if input_socket.name == socket_name:
                        try:
                            input_socket.default_value = field_value
                        except:
                            pass  # Some sockets might not be settable
                        break

    # =============================================================================
    # FIELD TYPE DETECTION AND NODE GROUP CREATION
    # =============================================================================

    def _detect_field_type_from_modifier(self, field_modifier):
        """Определить тип поля по модификатору - использует централизованную утилиту"""
        from ..utils.field_detection import detect_field_type_from_modifier
        return detect_field_type_from_modifier(field_modifier)

    def _create_field_node_group_for_effector(self, field_type):
        """Универсальное создание field node group для использования в эффекторах"""
        try:
            from ..registry.field_registry import get_field_class

            # Создаем node group для использования внутри эффекторов
            field_node_group_name = f"{field_type}FieldNodeGroup"

            if field_node_group_name not in bpy.data.node_groups:
                # Получаем класс поля
                field_class = get_field_class(field_type)
                if field_class:
                    field_instance = field_class()
                    # Создаем node group специально для эффекторов
                    field_node_group = field_instance.create_field_node_group_for_effectors("NodeGroup")
                    if field_node_group:
                        field_node_group.name = field_node_group_name
                        print(f"✅ [SYNC] Created {field_type} field node group for effectors: {field_node_group_name}")
                        return field_node_group
                    else:
                        print(f"❌ [SYNC] Failed to create {field_type} field node group")
                        return None
                else:
                    print(f"❌ [SYNC] Unknown field type: {field_type}")
                    return None
            else:
                field_node_group = bpy.data.node_groups[field_node_group_name]
                print(f"✅ [SYNC] Using existing {field_type} field node group: {field_node_group_name}")
                return field_node_group

        except Exception as e:
            print(f"❌ [SYNC] Error creating {field_type} field node group: {e}")
            import traceback
            traceback.print_exc()
            return None


# Global service instance
field_sync_service = FieldSynchronizationService()


def get_field_sync_service() -> FieldSynchronizationService:
    """Get the global field synchronization service"""
    return field_sync_service


def register_field_synchronization():
    """Register field synchronization service"""
    # Register update handler for field synchronization
    if field_sync_update_handler not in bpy.app.handlers.depsgraph_update_post:
        bpy.app.handlers.depsgraph_update_post.append(field_sync_update_handler)
    print("Field synchronization service registered")


def unregister_field_synchronization():
    """Unregister field synchronization service"""
    # Unregister update handler
    if field_sync_update_handler in bpy.app.handlers.depsgraph_update_post:
        bpy.app.handlers.depsgraph_update_post.remove(field_sync_update_handler)
    
    global field_sync_service
    field_sync_service = FieldSynchronizationService()
    print("Field synchronization service unregistered")


def field_sync_update_handler(scene, depsgraph):
    """Handler for automatic field synchronization on parameter changes"""
    try:
        # Check if any field modifiers have been updated
        for update in depsgraph.updates:
            if (hasattr(update, 'id') and 
                hasattr(update.id, 'modifiers') and 
                hasattr(update.id, 'name')):
                
                obj = update.id
                
                # Check all modifiers for field types
                for modifier in obj.modifiers:
                    if (modifier.type == 'NODES' and 
                        modifier.node_group and 
                        'field' in modifier.node_group.name.lower()):
                        
                        # This is a field modifier - trigger sync
                        sync_service = get_field_sync_service()
                        sync_service.sync_field_parameters_realtime(obj, modifier)
        
        # Also check for show_viewport changes in all objects
        for obj in bpy.data.objects:
            if obj.modifiers:
                for modifier in obj.modifiers:
                    if (modifier.type == 'NODES' and 
                        modifier.node_group and 
                        'field' in modifier.node_group.name.lower()):
                        
                        # Sync field to handle show_viewport changes
                        sync_service = get_field_sync_service()
                        sync_service.sync_field_parameters_realtime(obj, modifier)
                        
    except Exception as e:
        # Silent error handling to avoid spam
        pass