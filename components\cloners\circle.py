"""
Circle Cloner - Class-based implementation
Объединяет логику и конфигурацию в один класс
"""

import bpy
import bmesh
import mathutils
from ..base_cloner import BaseCloner


class CircleCloner(BaseCloner):
    """
    Circle Cloner - создает радиальное распределение клонов
    Объединяет всю логику, конфигурацию и UI в одном классе
    """
    
    bl_idname = "CIRCLE"
    bl_label = "Circle Cloner"
    
    def __init__(self):
        super().__init__()
    
    def register_properties(self, props_owner):
        """
        Регистрация всех свойств Circle Cloner
        Заменяет старый circle_config.py
        """
        # Circle Settings
        props_owner.circle_count = bpy.props.IntProperty(
            name="Count",
            description="Number of instances around the circle",
            default=8,
            min=3,
            max=1000
        )
        props_owner.circle_radius = bpy.props.FloatProperty(
            name="Radius",
            description="Radius of the circle",
            default=1.0,  # Базовое значение
            min=0.0
        )
        props_owner.circle_height = bpy.props.FloatProperty(
            name="Height",
            description="Height offset for the circle",
            default=0.0
        )

        # 🆕 НОВЫЕ ПАРАМЕТРЫ из ROADMAP:
        props_owner.circle_spiral_height = bpy.props.FloatProperty(
            name="Spiral Height",
            description="Height increment per full rotation for spiral effect",
            default=0.0
        )
        props_owner.circle_start_angle = bpy.props.FloatProperty(
            name="Start Angle",
            description="Starting angle for partial circles (in degrees)",
            default=0.0,
            min=0.0,
            max=360.0
        )
        props_owner.circle_end_angle = bpy.props.FloatProperty(
            name="End Angle",
            description="Ending angle for partial circles (in degrees)",
            default=360.0,
            min=0.0,
            max=360.0
        )
        props_owner.circle_align_to_normal = bpy.props.BoolProperty(
            name="Align to Normal",
            description="Align instances to surface normals",
            default=True
        )
    
    def get_specific_sockets(self):
        """
        Специфичные сокеты для Circle Cloner

        Returns:
            list: Список специфичных сокетов (name, socket_type, in_out, default)
        """
        return [
            # Основные параметры окружности
            ("Count", "NodeSocketInt", "INPUT", 8),
            ("Radius", "NodeSocketFloat", "INPUT", 1.0),  # Базовое значение
            ("Height", "NodeSocketFloat", "INPUT", 0.0),

            # 🆕 НОВЫЕ ВОЗМОЖНОСТИ из ROADMAP:
            ("Spiral Height", "NodeSocketFloat", "INPUT", 0.0),      # Спиральная высота
            ("Start Angle", "NodeSocketFloat", "INPUT", 0.0),        # Начальный угол
            ("End Angle", "NodeSocketFloat", "INPUT", 360.0),        # Конечный угол
        ]
    
    
    def _create_cloner_logic(self, base_nodes, mode):
        """
        Основная логика Circle Cloner
        
        Args:
            base_nodes: Словарь с базовыми нодами
            mode: Режим клонера
            
        Returns:
            NodeSocket: Финальный выход геометрии
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        
        # Получаем input геометрию в зависимости от режима
        geometry_input = self.get_geometry_input(base_nodes, mode)
        
        # Создаем круговое распределение точек
        circle_points = self._create_circular_distribution(base_nodes)
        
        # Инстансируем геометрию на точки
        instance_node = nodes.new('GeometryNodeInstanceOnPoints')
        instance_node.name = "Instance on Circle"
        instance_node.location = (400, 0)
        links.new(circle_points, instance_node.inputs['Points'])
        links.new(geometry_input, instance_node.inputs['Instance'])

        # Применяем Collection Random для unified клонеров в Collection режиме
        self.apply_collection_randomization(base_nodes, instance_node, mode)
        
        # Получаем индекс для рандомизации
        index_node = nodes.new('GeometryNodeInputIndex')
        index_node.location = (200, -200)
        
        # Применяем face-to-center поворот (специфично для Circle Cloner)
        instances_with_face_rotation = self._apply_face_to_center_rotation(base_nodes, instance_node.outputs['Instances'])
        
        # Применяем instance трансформации
        instances_with_transforms = self.apply_instance_transforms(base_nodes, instances_with_face_rotation)
        
        # Применяем рандомизацию
        randomized_instances = self.apply_random_transforms(base_nodes, instances_with_transforms, index_node.outputs['Index'])
        
        # Применяем глобальные трансформации
        final_geometry = self.apply_global_transforms(base_nodes, randomized_instances)
        
        return final_geometry
    
    def _create_circular_distribution(self, base_nodes):
        """
        Создание кругового распределения точек с поддержкой новых возможностей

        Args:
            base_nodes: Словарь с базовыми нодами

        Returns:
            NodeSocket: Выход с круговыми точками
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']

        # 🆕 Создаем точки вручную для поддержки частичных окружностей и спиралей
        points_geometry = self._create_advanced_circle_points(base_nodes)

        # Применяем height offset
        height_offset_points = self._apply_height_offset(base_nodes, points_geometry)

        # 🆕 Применяем спиральный эффект
        spiral_points = self._apply_spiral_effect(base_nodes, height_offset_points)

        return spiral_points

    def _create_advanced_circle_points(self, base_nodes):
        """
        Создание точек окружности с поддержкой частичных окружностей

        Args:
            base_nodes: Словарь с базовыми нодами

        Returns:
            NodeSocket: Выход с точками окружности
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']

        # Получаем индекс для каждой точки
        index_node = nodes.new('GeometryNodeInputIndex')
        index_node.location = (-600, 0)

        # Преобразуем углы из градусов в радианы
        start_angle_rad = nodes.new('ShaderNodeMath')
        start_angle_rad.operation = 'MULTIPLY'
        start_angle_rad.inputs[1].default_value = 0.017453292  # π/180
        start_angle_rad.location = (-500, 100)
        links.new(group_input.outputs['Start Angle'], start_angle_rad.inputs[0])

        end_angle_rad = nodes.new('ShaderNodeMath')
        end_angle_rad.operation = 'MULTIPLY'
        end_angle_rad.inputs[1].default_value = 0.017453292  # π/180
        end_angle_rad.location = (-500, 50)
        links.new(group_input.outputs['End Angle'], end_angle_rad.inputs[0])

        # Вычисляем диапазон углов
        angle_range = nodes.new('ShaderNodeMath')
        angle_range.operation = 'SUBTRACT'
        angle_range.location = (-400, 75)
        links.new(end_angle_rad.outputs['Value'], angle_range.inputs[0])
        links.new(start_angle_rad.outputs['Value'], angle_range.inputs[1])

        # Вычисляем угол для каждой точки: start_angle + (index / (count-1)) * angle_range
        count_minus_one = nodes.new('ShaderNodeMath')
        count_minus_one.operation = 'SUBTRACT'
        count_minus_one.inputs[1].default_value = 1.0
        count_minus_one.location = (-500, -50)
        links.new(group_input.outputs['Count'], count_minus_one.inputs[0])

        # Нормализованный индекс: index / (count-1)
        normalized_index = nodes.new('ShaderNodeMath')
        normalized_index.operation = 'DIVIDE'
        normalized_index.location = (-400, -25)
        links.new(index_node.outputs['Index'], normalized_index.inputs[0])
        links.new(count_minus_one.outputs['Value'], normalized_index.inputs[1])

        # Угол для текущей точки
        point_angle_offset = nodes.new('ShaderNodeMath')
        point_angle_offset.operation = 'MULTIPLY'
        point_angle_offset.location = (-300, 0)
        links.new(normalized_index.outputs['Value'], point_angle_offset.inputs[0])
        links.new(angle_range.outputs['Value'], point_angle_offset.inputs[1])

        point_angle = nodes.new('ShaderNodeMath')
        point_angle.operation = 'ADD'
        point_angle.location = (-200, 25)
        links.new(start_angle_rad.outputs['Value'], point_angle.inputs[0])
        links.new(point_angle_offset.outputs['Value'], point_angle.inputs[1])

        # Вычисляем X и Y координаты
        cos_angle = nodes.new('ShaderNodeMath')
        cos_angle.operation = 'COSINE'
        cos_angle.location = (-100, 50)
        links.new(point_angle.outputs['Value'], cos_angle.inputs[0])

        sin_angle = nodes.new('ShaderNodeMath')
        sin_angle.operation = 'SINE'
        sin_angle.location = (-100, 0)
        links.new(point_angle.outputs['Value'], sin_angle.inputs[0])

        # 🎯 Масштабируем radius для хорошего визуального разделения
        scale_radius = nodes.new('ShaderNodeMath')
        scale_radius.operation = 'MULTIPLY'
        scale_radius.inputs[1].default_value = 2.5  # Небольшой коэффициент против сжатия
        scale_radius.location = (-50, 25)
        links.new(group_input.outputs['Radius'], scale_radius.inputs[0])

        # Умножаем на масштабированный радиус
        x_coord = nodes.new('ShaderNodeMath')
        x_coord.operation = 'MULTIPLY'
        x_coord.location = (0, 50)
        links.new(cos_angle.outputs['Value'], x_coord.inputs[0])
        links.new(scale_radius.outputs['Value'], x_coord.inputs[1])

        y_coord = nodes.new('ShaderNodeMath')
        y_coord.operation = 'MULTIPLY'
        y_coord.location = (0, 0)
        links.new(sin_angle.outputs['Value'], y_coord.inputs[0])
        links.new(scale_radius.outputs['Value'], y_coord.inputs[1])

        # Создаем вектор позиции
        combine_position = nodes.new('ShaderNodeCombineXYZ')
        combine_position.location = (100, 25)
        combine_position.inputs['Z'].default_value = 0.0
        links.new(x_coord.outputs['Value'], combine_position.inputs['X'])
        links.new(y_coord.outputs['Value'], combine_position.inputs['Y'])

        # Создаем точки из индексов
        points_from_count = nodes.new('GeometryNodePoints')
        points_from_count.location = (-400, -100)
        links.new(group_input.outputs['Count'], points_from_count.inputs['Count'])

        # Устанавливаем позиции точек
        set_position = nodes.new('GeometryNodeSetPosition')
        set_position.name = "Set Circle Positions"
        set_position.location = (200, 0)
        links.new(points_from_count.outputs['Geometry'], set_position.inputs['Geometry'])
        links.new(combine_position.outputs['Vector'], set_position.inputs['Position'])

        return set_position.outputs['Geometry']

    def _apply_height_offset(self, base_nodes, points_input):
        """
        Применение базового height offset

        Args:
            base_nodes: Словарь с базовыми нодами
            points_input: Входной сокет с точками

        Returns:
            NodeSocket: Выход с примененным height offset
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']

        # Создаем вектор высоты
        combine_height = nodes.new('ShaderNodeCombineXYZ')
        combine_height.location = (300, -100)
        combine_height.inputs['X'].default_value = 0.0
        combine_height.inputs['Y'].default_value = 0.0
        links.new(group_input.outputs['Height'], combine_height.inputs['Z'])

        # Применяем height offset
        set_position = nodes.new('GeometryNodeSetPosition')
        set_position.name = "Apply Height Offset"
        set_position.location = (400, 0)
        links.new(points_input, set_position.inputs['Geometry'])
        links.new(combine_height.outputs['Vector'], set_position.inputs['Offset'])

        return set_position.outputs['Geometry']

    def _apply_spiral_effect(self, base_nodes, points_input):
        """
        Применение спирального эффекта

        Args:
            base_nodes: Словарь с базовыми нодами
            points_input: Входной сокет с точками

        Returns:
            NodeSocket: Выход с примененным спиральным эффектом
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']

        # Получаем индекс точки
        index_node = nodes.new('GeometryNodeInputIndex')
        index_node.location = (500, -200)

        # Вычисляем нормализованный индекс: index / (count-1)
        count_minus_one = nodes.new('ShaderNodeMath')
        count_minus_one.operation = 'SUBTRACT'
        count_minus_one.inputs[1].default_value = 1.0
        count_minus_one.location = (500, -250)
        links.new(group_input.outputs['Count'], count_minus_one.inputs[0])

        normalized_index = nodes.new('ShaderNodeMath')
        normalized_index.operation = 'DIVIDE'
        normalized_index.location = (600, -225)
        links.new(index_node.outputs['Index'], normalized_index.inputs[0])
        links.new(count_minus_one.outputs['Value'], normalized_index.inputs[1])

        # Вычисляем спиральную высоту: normalized_index * spiral_height
        spiral_z_offset = nodes.new('ShaderNodeMath')
        spiral_z_offset.operation = 'MULTIPLY'
        spiral_z_offset.location = (700, -200)
        links.new(normalized_index.outputs['Value'], spiral_z_offset.inputs[0])
        links.new(group_input.outputs['Spiral Height'], spiral_z_offset.inputs[1])

        # Создаем вектор спирального смещения
        combine_spiral = nodes.new('ShaderNodeCombineXYZ')
        combine_spiral.location = (800, -175)
        combine_spiral.inputs['X'].default_value = 0.0
        combine_spiral.inputs['Y'].default_value = 0.0
        links.new(spiral_z_offset.outputs['Value'], combine_spiral.inputs['Z'])

        # Применяем спиральное смещение
        set_position = nodes.new('GeometryNodeSetPosition')
        set_position.name = "Apply Spiral Effect"
        set_position.location = (900, -100)
        links.new(points_input, set_position.inputs['Geometry'])
        links.new(combine_spiral.outputs['Vector'], set_position.inputs['Offset'])

        return set_position.outputs['Geometry']
    
    def _apply_face_to_center_rotation(self, base_nodes, instances_input):
        """
        Заглушка для функции поворота к центру
        Пока просто возвращает инстансы без изменений

        Args:
            base_nodes: Словарь с базовыми нодами
            instances_input: Входной сокет с инстансами

        Returns:
            NodeSocket: Выход без изменений
        """
        # Просто возвращаем инстансы без изменений
        return instances_input
    
    def draw_ui(self, layout, props_owner, modifier=None):
        """
        Отрисовка UI для Circle Cloner

        Args:
            layout: UI layout
            props_owner: Объект со свойствами
            modifier: Модификатор (если есть)
        """
        if modifier:
            # Если есть модификатор - используем сокеты модификатора
            self._draw_modifier_ui(layout, modifier)
        else:
            # Если нет модификатора - используем настройки из props_owner
            self._draw_settings_ui(layout, props_owner)

    def _draw_modifier_ui(self, layout, modifier):
        """Отрисовка UI через сокеты модификатора"""
        from ...ui.utils.ui_helpers import display_socket_prop

        # Circle Settings
        box = layout.box()
        box.label(text="Circle Settings", icon='MESH_CIRCLE')

        display_socket_prop(box, modifier, "Count", text="Count")
        display_socket_prop(box, modifier, "Radius", text="Radius")
        display_socket_prop(box, modifier, "Height", text="Height")

        # 🆕 Advanced Circle Settings
        advanced_box = layout.box()
        advanced_box.label(text="Advanced Circle", icon='SETTINGS')

        display_socket_prop(advanced_box, modifier, "Spiral Height", text="Spiral Height")
        display_socket_prop(advanced_box, modifier, "Start Angle", text="Start Angle")
        display_socket_prop(advanced_box, modifier, "End Angle", text="End Angle")
        display_socket_prop(advanced_box, modifier, "Align to Normal", text="Align to Normal")

        # Базовые группы параметров из BaseCloner
        self._draw_base_cloner_ui(layout, None, modifier)

    def _draw_settings_ui(self, layout, props_owner):
        """Отрисовка UI через настройки props_owner"""
        # Circle Settings
        box = layout.box()
        box.label(text="Circle Settings", icon='MESH_CIRCLE')

        col = box.column(align=True)
        col.prop(props_owner, "circle_count", text="Count")
        col.prop(props_owner, "circle_radius", text="Radius")
        col.prop(props_owner, "circle_height", text="Height")

        # 🆕 Advanced Circle Settings
        advanced_box = layout.box()
        advanced_box.label(text="Advanced Circle", icon='SETTINGS')

        advanced_box.prop(props_owner, "circle_spiral_height", text="Spiral Height")
        advanced_box.prop(props_owner, "circle_start_angle", text="Start Angle")
        advanced_box.prop(props_owner, "circle_end_angle", text="End Angle")
        advanced_box.prop(props_owner, "circle_align_to_normal", text="Align to Normal")

        # Базовые группы параметров из BaseCloner
        self._draw_base_cloner_ui(layout, props_owner, None)
    
    def _draw_base_cloner_ui(self, layout, props_owner, modifier):
        """Отрисовка базовых UI групп клонера"""
        from ...ui.utils.ui_helpers import display_socket_prop

        if modifier:
            # Instance Transform через сокеты модификатора
            box = layout.box()
            box.label(text="Instance Transform", icon='CON_TRANSFORM')

            display_socket_prop(box, modifier, "Instance Scale", text="Scale")
            display_socket_prop(box, modifier, "Instance Rotation", text="Rotation")

            # Randomization через сокеты модификатора
            box = layout.box()
            box.label(text="Randomization", icon='RNDCURVE')

            display_socket_prop(box, modifier, "Random Position", text="Position")
            display_socket_prop(box, modifier, "Random Rotation", text="Rotation")
            display_socket_prop(box, modifier, "Random Scale", text="Scale")
            display_socket_prop(box, modifier, "Random Seed", text="Seed")

            # Global Transform через сокеты модификатора
            box = layout.box()
            box.label(text="Global Transform", icon='WORLD_DATA')

            display_socket_prop(box, modifier, "Global Position", text="Position")
            display_socket_prop(box, modifier, "Global Rotation", text="Rotation")

        else:
            # Instance Transform через props_owner (если нет модификатора)
            box = layout.box()
            box.label(text="Instance Transform", icon='CON_TRANSFORM')

            if hasattr(props_owner, 'instance_scale'):
                box.prop(props_owner, "instance_scale", text="Scale")
            if hasattr(props_owner, 'instance_rotation'):
                box.prop(props_owner, "instance_rotation", text="Rotation")

            # Randomization через props_owner
            box = layout.box()
            box.label(text="Randomization", icon='RNDCURVE')

            if hasattr(props_owner, 'random_position'):
                box.prop(props_owner, "random_position", text="Position")
            if hasattr(props_owner, 'random_rotation'):
                box.prop(props_owner, "random_rotation", text="Rotation")
            if hasattr(props_owner, 'random_scale'):
                box.prop(props_owner, "random_scale", text="Scale")
            if hasattr(props_owner, 'random_seed'):
                box.prop(props_owner, "random_seed", text="Seed")
    
    def get_default_parameters(self):
        """Параметры по умолчанию для Circle Cloner"""
        base_defaults = super().get_default_parameters()
        circle_defaults = {
            "count": 8,
            "radius": 2.0,
            "height": 0.0,
            # 🆕 НОВЫЕ ПАРАМЕТРЫ:
            "spiral_height": 0.0,
            "start_angle": 0.0,
            "end_angle": 360.0
        }
        return {**base_defaults, **circle_defaults}
    
    def get_parameter_groups(self):
        """Группировка параметров для UI"""
        base_groups = super().get_cloner_parameter_groups()
        circle_groups = {
            "Circle Settings": ["Count", "Radius", "Height"],
            "Advanced Circle": ["Spiral Height", "Start Angle", "End Angle"]  # 🆕 Новая группа
        }
        return {**circle_groups, **base_groups}


# Экземпляр класса для использования в других модулях
circle_cloner = CircleCloner()


# Функции register/unregister для совместимости с __init__.py
def register():
    """Регистрация Circle Cloner (в новой архитектуре не требуется)"""
    print("✅ Circle Cloner: Using class-based architecture, no registration needed")
    pass


def unregister():
    """Отмена регистрации Circle Cloner (в новой архитектуре не требуется)"""
    print("✅ Circle Cloner: Using class-based architecture, no unregistration needed") 
    pass