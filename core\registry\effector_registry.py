"""
Unified Effector Registry для ClonerPro
Единый реестр всех эффекторов в новой классовой архитектуре
"""

# Импорты классов эффекторов
from ...components.effectors.random import RandomEffector
from ...components.effectors.noise import NoiseEffector
from ...components.effectors.step import StepEffector

# Единый реестр эффекторов - поддерживает как классы, так и функции
EFFECTOR_REGISTRY = {
    # Классовые эффекторы (новая архитектура)
    "RANDOM": {
        'class': RandomEffector,
        'creator': None,  # Будет создан через класс
        'display_name': 'Random',
        'description': 'Applies random transformations to clones',
        'affects': ['position', 'rotation', 'scale'],
        'architecture': 'CLASS'
    },
    "NOISE": {
        'class': NoiseEffector,
        'creator': None,  # Будет создан через класс
        'display_name': 'Noise',
        'description': 'Applies noise-based transformations to clones',
        'affects': ['position', 'rotation', 'scale'],
        'architecture': 'CLASS'
    },
    "STEP": {
        'class': StepEffector,
        'creator': None,  # Будет создан через класс
        'display_name': 'Step',
        'description': 'Applies step-based transformations to clones based on instance index',
        'affects': ['position', 'rotation', 'scale'],
        'architecture': 'CLASS'
    }
}


def get_effector_class(effector_type):
    """
    Получить класс эффектора по типу
    
    Args:
        effector_type: Тип эффектора (например, "RANDOM_CLASS")
        
    Returns:
        BaseEffector: Класс эффектора или None
    """
    if effector_type in EFFECTOR_REGISTRY:
        return EFFECTOR_REGISTRY[effector_type].get('class')
    return None


def get_effector_creator(effector_type):
    """
    Получить функцию создания эффектора по типу
    
    Args:
        effector_type: Тип эффектора (например, "RANDOM")
        
    Returns:
        function: Функция создания эффектора или None
    """
    if effector_type in EFFECTOR_REGISTRY:
        return EFFECTOR_REGISTRY[effector_type].get('creator')
    return None


def create_effector_via_registry(effector_type, name_suffix=""):
    """
    Создать эффектор через реестр (только классовая архитектура)

    Args:
        effector_type: Тип эффектора
        name_suffix: Суффикс для имени

    Returns:
        bpy.types.NodeGroup: Созданная группа нодов
    """
    if effector_type not in EFFECTOR_REGISTRY:
        print(f"[ERROR] Unknown effector type: {effector_type}")
        return None

    effector_info = EFFECTOR_REGISTRY[effector_type]
    effector_class = effector_info.get('class')

    if not effector_class:
        print(f"[ERROR] No class found for effector type: {effector_type}")
        return None

    try:
        effector_instance = effector_class()
        return effector_instance.create_node_group(name_suffix)
    except Exception as e:
        print(f"[ERROR] Failed to create effector {effector_type}: {e}")
        return None


def list_effector_types():
    """
    Получить список всех зарегистрированных типов эффекторов

    Returns:
        list: Список типов эффекторов
    """
    return list(EFFECTOR_REGISTRY.keys())


# Дублирующая функция удалена - используйте list_effector_types()


# Устаревшие методы удалены - все эффекторы теперь классовые


def get_effector_info(effector_type):
    """
    Получить информацию об эффекторе
    
    Args:
        effector_type: Тип эффектора
        
    Returns:
        dict: Информация об эффекторе
    """
    return EFFECTOR_REGISTRY.get(effector_type, {})


def register_effector(effector_type, effector_class, display_name, description, affects=None):
    """
    Регистрация нового эффектора (только классовая архитектура)

    Args:
        effector_type: Тип эффектора (например, "CUSTOM")
        effector_class: Класс эффектора
        display_name: Отображаемое имя
        description: Описание
        affects: Список затрагиваемых трансформаций
    """
    EFFECTOR_REGISTRY[effector_type] = {
        'class': effector_class,
        'creator': None,
        'display_name': display_name,
        'description': description,
        'affects': affects or ['position', 'rotation', 'scale'],
        'architecture': 'CLASS'
    }
    print(f"✅ Registered effector: {effector_type} -> {effector_class.__name__}")
