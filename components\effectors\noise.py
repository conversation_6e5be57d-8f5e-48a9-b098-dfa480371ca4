"""
Noise Effector - Class-based implementation
Объединяет логику и конфигурацию в один класс, сохраняя 1 в 1 функционал оригинала
"""

import bpy
from ..base_effector import BaseEffector


class NoiseEffector(BaseEffector):
    """
    Noise Effector - применяет шумовые трансформации к клонам
    Объединяет всю логику, конфигурацию и UI в одном классе
    Сохраняет 1 в 1 функционал оригинального noise.py
    """

    bl_idname = "NOISE"
    bl_label = "Noise Effector"

    def __init__(self):
        super().__init__()

    def register_properties(self, props_owner):
        """
        Регистрация всех свойств Noise Effector
        Заменяет старый noise_config.py
        """
        # Регистрируем базовые свойства
        super().register_properties(props_owner)

        # Noise Settings - специфичные для Noise Effector
        props_owner.noise_uniform_scale = bpy.props.BoolProperty(
            name="Uniform Scale",
            description="Use the same noise value for all scale axes",
            default=True
        )
        props_owner.noise_symmetric_translation = bpy.props.BoolProperty(
            name="Symmetric Translation",
            description="Use symmetric noise for translation",
            default=False
        )
        props_owner.noise_symmetric_rotation = bpy.props.BoolProperty(
            name="Symmetric Rotation",
            description="Use symmetric noise for rotation",
            default=False
        )

        # Noise Parameters
        props_owner.noise_scale = bpy.props.FloatProperty(
            name="Noise Scale",
            description="Scale of the noise pattern",
            default=0.5,
            min=0.1,
            max=10.0
        )
        props_owner.noise_detail = bpy.props.FloatProperty(
            name="Noise Detail",
            description="Detail level of the noise",
            default=2.0,
            min=0.0,
            max=15.0
        )
        props_owner.noise_roughness = bpy.props.FloatProperty(
            name="Noise Roughness",
            description="Roughness of the noise",
            default=0.5,
            min=0.0,
            max=1.0
        )
        props_owner.noise_lacunarity = bpy.props.FloatProperty(
            name="Noise Lacunarity",
            description="Lacunarity of the noise",
            default=2.0,
            min=0.0,
            max=10.0
        )
        props_owner.noise_distortion = bpy.props.FloatProperty(
            name="Noise Distortion",
            description="Distortion of the noise",
            default=0.0,
            min=-10.0,
            max=10.0
        )

        # Noise Position and Scale
        props_owner.noise_position = bpy.props.FloatVectorProperty(
            name="Noise Position",
            description="Position offset for noise sampling",
            default=(0.0, 0.0, 0.0),
            size=3
        )
        props_owner.noise_xyz_scale = bpy.props.FloatVectorProperty(
            name="Noise XYZ Scale",
            description="Scale multiplier for noise coordinates",
            default=(1.0, 1.0, 1.0),
            size=3
        )

        # Animation
        props_owner.noise_speed = bpy.props.FloatProperty(
            name="Speed",
            description="Animation speed for noise",
            default=0.0,
            min=0.0,
            max=10.0
        )

    def get_specific_sockets(self):
        """
        Специфичные сокеты для Noise Effector

        Returns:
            list: Список специфичных сокетов (name, socket_type, in_out, default, min, max)
        """
        return [
            ("Uniform Scale", "NodeSocketBool", "INPUT", True),
            ("Symmetric Translation", "NodeSocketBool", "INPUT", False),
            ("Symmetric Rotation", "NodeSocketBool", "INPUT", False),

            # Noise Parameters
            ("Noise Scale", "NodeSocketFloat", "INPUT", 0.5, 0.1, 10.0),
            ("Noise Detail", "NodeSocketFloat", "INPUT", 2.0, 0.0, 15.0),
            ("Noise Roughness", "NodeSocketFloat", "INPUT", 0.5, 0.0, 1.0),
            ("Noise Lacunarity", "NodeSocketFloat", "INPUT", 2.0, 0.0, 10.0),
            ("Noise Distortion", "NodeSocketFloat", "INPUT", 0.0, -10.0, 10.0),

            # Position and Scale
            ("Noise Position", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),
            ("Noise XYZ Scale", "NodeSocketVector", "INPUT", (1.0, 1.0, 1.0)),

            # Animation
            ("Speed", "NodeSocketFloat", "INPUT", 0.0, 0.0, 10.0),
        ]

    def get_default_parameters(self):
        """Параметры по умолчанию для Noise Effector"""
        base_defaults = super().get_default_parameters()
        noise_defaults = {
            "uniform_scale": True,
            "symmetric_translation": False,
            "symmetric_rotation": False,
            "noise_scale": 0.5,
            "noise_detail": 2.0,
            "noise_roughness": 0.5,
            "noise_lacunarity": 2.0,
            "noise_distortion": 0.0,
            "noise_position": (0.0, 0.0, 0.0),
            "noise_xyz_scale": (1.0, 1.0, 1.0),
            "speed": 0.0
        }
        return {**base_defaults, **noise_defaults}

    def get_parameter_groups(self):
        """Группировка параметров для UI"""
        base_groups = super().get_effector_parameter_groups()
        noise_groups = {
            "Noise Settings": ["Uniform Scale", "Symmetric Translation", "Symmetric Rotation"],
            "Noise Parameters": ["Noise Scale", "Noise Detail", "Noise Roughness", "Noise Lacunarity", "Noise Distortion"],
            "Noise Position": ["Noise Position", "Noise XYZ Scale"],
            "Animation": ["Speed", "Seed"]
        }
        return {**noise_groups, **base_groups}

    def draw_ui(self, layout, context, modifier):
        """
        Отрисовка UI для Noise Effector
        """
        from ...ui.utils.ui_helpers import display_socket_prop

        # Получаем группы параметров
        parameter_groups = self.get_parameter_groups()

        # Отображаем группы параметров
        for group_name, param_names in parameter_groups.items():
            if param_names:
                # Создаем бокс для группы
                box = layout.box()
                col = box.column(align=True)

                # Заголовок группы с иконкой
                icon_map = {
                    "Noise Settings": "FORCE_TURBULENCE",
                    "Noise Parameters": "TEXTURE",
                    "Noise Position": "EMPTY_ARROWS",
                    "Animation": "TIME",
                    "Effector Settings": "SETTINGS",
                    "Transform": "ORIENTATION_GIMBAL",
                }
                icon = icon_map.get(group_name, "DOT")
                col.label(text=f"{group_name}:", icon=icon)

                # Добавляем параметры
                col.separator()
                for param_name in param_names:
                    display_socket_prop(col, modifier, param_name, text=param_name)

    def _create_effector_logic(self, base_nodes):
        """
        Основная логика Noise Effector - точная копия оригинального функционала

        Args:
            base_nodes: Словарь с базовыми нодами

        Returns:
            NodeSocket: Финальный выход геометрии с примененным эффектом
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        index = base_nodes['index']

        # Get instance position for noise sampling
        instance_position = nodes.new('GeometryNodeInputPosition')
        instance_position.location = (-800, -100)

        # Add noise position offset
        position_offset = nodes.new('ShaderNodeVectorMath')
        position_offset.operation = 'ADD'
        position_offset.location = (-600, -100)
        links.new(instance_position.outputs[0], position_offset.inputs[0])
        links.new(group_input.outputs['Noise Position'], position_offset.inputs[1])

        # Scale position by XYZ scale
        position_scaled = nodes.new('ShaderNodeVectorMath')
        position_scaled.operation = 'MULTIPLY'
        position_scaled.location = (-500, -200)
        links.new(position_offset.outputs[0], position_scaled.inputs[0])
        links.new(group_input.outputs['Noise XYZ Scale'], position_scaled.inputs[1])

        # Animation value (frame * speed + seed)
        frame_info = nodes.new('GeometryNodeInputSceneTime')
        frame_info.location = (-800, -300)

        speed_multiply = nodes.new('ShaderNodeMath')
        speed_multiply.operation = 'MULTIPLY'
        speed_multiply.location = (-600, -300)
        links.new(frame_info.outputs['Frame'], speed_multiply.inputs[0])
        links.new(group_input.outputs['Speed'], speed_multiply.inputs[1])

        animated_value = nodes.new('ShaderNodeMath')
        animated_value.operation = 'ADD'
        animated_value.location = (-500, -300)
        links.new(speed_multiply.outputs[0], animated_value.inputs[0])
        links.new(group_input.outputs['Seed'], animated_value.inputs[1])

        # Position noise (for translation)
        position_noise = nodes.new('ShaderNodeTexNoise')
        position_noise.noise_dimensions = '4D'
        position_noise.location = (-300, -100)
        links.new(position_scaled.outputs[0], position_noise.inputs['Vector'])
        links.new(animated_value.outputs[0], position_noise.inputs['W'])
        links.new(group_input.outputs['Noise Scale'], position_noise.inputs['Scale'])
        links.new(group_input.outputs['Noise Detail'], position_noise.inputs['Detail'])
        links.new(group_input.outputs['Noise Roughness'], position_noise.inputs['Roughness'])
        links.new(group_input.outputs['Noise Lacunarity'], position_noise.inputs['Lacunarity'])
        links.new(group_input.outputs['Noise Distortion'], position_noise.inputs['Distortion'])

        # Rotation noise (with different offset)
        rotation_noise = nodes.new('ShaderNodeTexNoise')
        rotation_noise.noise_dimensions = '4D'
        rotation_noise.location = (-300, -300)
        rotation_offset = nodes.new('ShaderNodeMath')
        rotation_offset.operation = 'ADD'
        rotation_offset.location = (-400, -400)
        links.new(animated_value.outputs[0], rotation_offset.inputs[0])
        rotation_offset.inputs[1].default_value = 42.0  # Different offset
        links.new(position_scaled.outputs[0], rotation_noise.inputs['Vector'])
        links.new(rotation_offset.outputs[0], rotation_noise.inputs['W'])
        links.new(group_input.outputs['Noise Scale'], rotation_noise.inputs['Scale'])
        links.new(group_input.outputs['Noise Detail'], rotation_noise.inputs['Detail'])
        links.new(group_input.outputs['Noise Roughness'], rotation_noise.inputs['Roughness'])
        links.new(group_input.outputs['Noise Lacunarity'], rotation_noise.inputs['Lacunarity'])
        links.new(group_input.outputs['Noise Distortion'], rotation_noise.inputs['Distortion'])

        # Scale noise (with different offset)
        scale_noise = nodes.new('ShaderNodeTexNoise')
        scale_noise.noise_dimensions = '4D'
        scale_noise.location = (-300, -500)
        scale_offset_node = nodes.new('ShaderNodeMath')
        scale_offset_node.operation = 'ADD'
        scale_offset_node.location = (-400, -500)
        links.new(animated_value.outputs[0], scale_offset_node.inputs[0])
        scale_offset_node.inputs[1].default_value = 84.0  # Different offset
        links.new(position_scaled.outputs[0], scale_noise.inputs['Vector'])
        links.new(scale_offset_node.outputs[0], scale_noise.inputs['W'])
        links.new(group_input.outputs['Noise Scale'], scale_noise.inputs['Scale'])
        links.new(group_input.outputs['Noise Detail'], scale_noise.inputs['Detail'])
        links.new(group_input.outputs['Noise Roughness'], scale_noise.inputs['Roughness'])
        links.new(group_input.outputs['Noise Lacunarity'], scale_noise.inputs['Lacunarity'])
        links.new(group_input.outputs['Noise Distortion'], scale_noise.inputs['Distortion'])

        # Convert noise outputs to proper ranges
        # Position: noise * position_range
        position_range = nodes.new('ShaderNodeVectorMath')
        position_range.operation = 'MULTIPLY'
        position_range.location = (0, -100)
        links.new(position_noise.outputs['Color'], position_range.inputs[0])
        links.new(group_input.outputs['Position'], position_range.inputs[1])

        # Rotation: noise * rotation_range
        rotation_range = nodes.new('ShaderNodeVectorMath')
        rotation_range.operation = 'MULTIPLY'
        rotation_range.location = (0, -300)
        links.new(rotation_noise.outputs['Color'], rotation_range.inputs[0])
        links.new(group_input.outputs['Rotation'], rotation_range.inputs[1])

        # Scale: 1.0 + noise * scale_range
        scale_range = nodes.new('ShaderNodeVectorMath')
        scale_range.operation = 'MULTIPLY'
        scale_range.location = (0, -500)
        links.new(scale_noise.outputs['Color'], scale_range.inputs[0])
        links.new(group_input.outputs['Scale'], scale_range.inputs[1])

        scale_final = nodes.new('ShaderNodeVectorMath')
        scale_final.operation = 'ADD'
        scale_final.inputs[1].default_value = (1.0, 1.0, 1.0)
        scale_final.location = (200, -500)
        links.new(scale_range.outputs[0], scale_final.inputs[0])

        # Handle uniform scale
        separate_xyz = nodes.new('ShaderNodeSeparateXYZ')
        separate_xyz.location = (300, -600)
        links.new(scale_final.outputs[0], separate_xyz.inputs[0])

        uniform_scale = nodes.new('ShaderNodeCombineXYZ')
        uniform_scale.location = (400, -500)
        links.new(separate_xyz.outputs[0], uniform_scale.inputs[0])
        links.new(separate_xyz.outputs[0], uniform_scale.inputs[1])
        links.new(separate_xyz.outputs[0], uniform_scale.inputs[2])

        # Switch between uniform and non-uniform scale
        scale_switch = nodes.new('GeometryNodeSwitch')
        scale_switch.input_type = 'VECTOR'
        scale_switch.location = (500, -500)
        links.new(group_input.outputs['Uniform Scale'], scale_switch.inputs[0])  # Switch
        links.new(scale_final.outputs[0], scale_switch.inputs[1])  # False
        links.new(uniform_scale.outputs[0], scale_switch.inputs[2])  # True

        # Combine strength with field mask (как в Random Effector)
        field_strength_combine = nodes.new('ShaderNodeMath')
        field_strength_combine.operation = 'MULTIPLY'
        field_strength_combine.location = (400, -600)
        links.new(group_input.outputs['Strength'], field_strength_combine.inputs[0])  # Base strength
        links.new(group_input.outputs['Field'], field_strength_combine.inputs[1])  # Field mask

        # Apply combined strength multiplier to position and rotation
        position_strength = nodes.new('ShaderNodeVectorMath')
        position_strength.operation = 'MULTIPLY'
        position_strength.location = (300, -100)
        links.new(position_range.outputs[0], position_strength.inputs[0])
        links.new(field_strength_combine.outputs['Value'], position_strength.inputs[1])  # Combined strength

        rotation_strength = nodes.new('ShaderNodeVectorMath')
        rotation_strength.operation = 'MULTIPLY'
        rotation_strength.location = (300, -300)
        links.new(rotation_range.outputs[0], rotation_strength.inputs[0])
        links.new(field_strength_combine.outputs['Value'], rotation_strength.inputs[1])  # Combined strength

        # Apply field mask to scale: 1.0 + (scale - 1.0) * field_strength
        scale_offset = nodes.new('ShaderNodeVectorMath')
        scale_offset.operation = 'SUBTRACT'
        scale_offset.inputs[1].default_value = (1.0, 1.0, 1.0)
        scale_offset.location = (600, -500)
        links.new(scale_switch.outputs[0], scale_offset.inputs[0])

        # Create vector from field_strength_combine for scale multiplication
        field_strength_vector = nodes.new('ShaderNodeCombineXYZ')
        field_strength_vector.location = (400, -650)
        links.new(field_strength_combine.outputs['Value'], field_strength_vector.inputs['X'])
        links.new(field_strength_combine.outputs['Value'], field_strength_vector.inputs['Y'])
        links.new(field_strength_combine.outputs['Value'], field_strength_vector.inputs['Z'])

        # Multiply scale offset by field strength
        scale_mul_field = nodes.new('ShaderNodeVectorMath')
        scale_mul_field.operation = 'MULTIPLY'
        scale_mul_field.location = (700, -500)
        links.new(scale_offset.outputs['Vector'], scale_mul_field.inputs[0])
        links.new(field_strength_vector.outputs['Vector'], scale_mul_field.inputs[1])

        # Add 1.0 back to scale
        final_scale = nodes.new('ShaderNodeVectorMath')
        final_scale.operation = 'ADD'
        final_scale.inputs[1].default_value = (1.0, 1.0, 1.0)
        final_scale.location = (800, -500)
        links.new(scale_mul_field.outputs['Vector'], final_scale.inputs[0])

        # Apply transformations to instances
        # Start with the input geometry
        translate_instances = nodes.new('GeometryNodeTranslateInstances')
        translate_instances.location = (600, -100)
        links.new(group_input.outputs['Geometry'], translate_instances.inputs['Instances'])
        links.new(position_strength.outputs['Vector'], translate_instances.inputs['Translation'])

        # Rotate instances
        rotate_instances = nodes.new('GeometryNodeRotateInstances')
        rotate_instances.location = (800, -200)
        links.new(translate_instances.outputs['Instances'], rotate_instances.inputs['Instances'])
        links.new(rotation_strength.outputs['Vector'], rotate_instances.inputs['Rotation'])

        # Scale instances
        scale_instances = nodes.new('GeometryNodeScaleInstances')
        scale_instances.location = (1000, -300)
        links.new(rotate_instances.outputs['Instances'], scale_instances.inputs['Instances'])
        links.new(final_scale.outputs['Vector'], scale_instances.inputs['Scale'])

        return scale_instances.outputs['Instances']




# Compatibility function for easy integration
def create_noise_effector(name_suffix=""):
    """Create Noise Effector using class-based approach"""
    effector = NoiseEffector()
    return effector.create_node_group(name_suffix)


# Alias for backward compatibility
def create_noise_effector_logic_group(name_suffix=""):
    """Create Noise Effector logic group - class-based compatibility function"""
    return create_noise_effector(name_suffix)
