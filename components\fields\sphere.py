"""
Sphere Field - Class-based implementation
Объединяет логику и конфигурацию в один класс, сохраняя 1 в 1 функционал оригинала
"""

import bpy
from ..base_field import BaseField


class SphereField(BaseField):
    """
    Sphere Field - создает сферическое поле для ограничения действия эффекторов
    Объединяет всю логику, конфигурацию и UI в одном классе
    Сохраняет 1 в 1 функционал оригинального sphere.py
    """
    
    bl_idname = "SPHERE"
    bl_label = "Sphere Field"
    
    def __init__(self):
        super().__init__()
    
    def register_properties(self, props_owner):
        """
        Регистрация всех свойств Sphere Field
        Заменяет старый sphere_config.py
        """
        # Регистрируем базовые свойства
        super().register_properties(props_owner)
        
        # Sphere Settings - специфичные для Sphere Field
        props_owner.sphere_center = bpy.props.FloatVectorProperty(
            name="Center",
            description="Center of the sphere",
            default=(0.0, 0.0, 0.0),
            size=3
        )
        props_owner.sphere_radius = bpy.props.FloatProperty(
            name="Radius",
            description="Radius of the sphere",
            default=5.0,
            min=1.0,
            max=10000.0
        )
        props_owner.sphere_falloff = bpy.props.FloatProperty(
            name="Falloff",
            description="Falloff distance from edge",
            default=0.0,
            min=0.0,
            max=1.0
        )
    
    def get_specific_sockets(self):
        """
        Специфичные сокеты для Sphere Field
        
        Returns:
            list: Список специфичных сокетов (name, socket_type, in_out, default, min, max)
        """
        return [
            ("Center", "NodeSocketVector", "INPUT", (0.0, 0.0, 0.0)),
            ("Radius", "NodeSocketFloat", "INPUT", 5.0, 1.0, 10000.0),
            ("Falloff", "NodeSocketFloat", "INPUT", 0.0, 0.0, 1.0),
        ]
    
    def _create_field_logic(self, base_nodes):
        """
        Основная логика Sphere Field - точная копия оригинального функционала
        
        Args:
            base_nodes: Словарь с базовыми нодами
            
        Returns:
            NodeSocket: Выход силы поля
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        position = base_nodes['position']
        
        # Расчет расстояния до центра сферы
        vector_math_distance = nodes.new('ShaderNodeVectorMath')
        vector_math_distance.operation = 'DISTANCE'
        vector_math_distance.location = (-200, -100)
        links.new(position.outputs[0], vector_math_distance.inputs[0])
        links.new(group_input.outputs['Center'], vector_math_distance.inputs[1])
        
        # Расчет границы falloff (radius * falloff)
        # falloff=0 -> граница=0 (резкая граница на полном радиусе)
        # falloff=0.5 -> граница=radius*0.5 (затухание начинается с половины радиуса)
        # falloff=1.0 -> граница=radius (плавное затухание от центра до края)
        math_falloff_boundary = nodes.new('ShaderNodeMath')
        math_falloff_boundary.operation = 'MULTIPLY'
        math_falloff_boundary.location = (-200, 100)
        links.new(group_input.outputs['Radius'], math_falloff_boundary.inputs[0])
        links.new(group_input.outputs['Falloff'], math_falloff_boundary.inputs[1])
        
        # Map Range: расстояние -> сила поля
        map_range = nodes.new('ShaderNodeMapRange')
        map_range.clamp = True
        map_range.data_type = 'FLOAT'
        map_range.interpolation_type = 'LINEAR'
        map_range.location = (0, 0)
        
        # Подключаем расстояние как входное значение
        links.new(vector_math_distance.outputs['Value'], map_range.inputs['Value'])
        
        # From Min = falloff boundary, From Max = radius
        links.new(math_falloff_boundary.outputs['Value'], map_range.inputs['From Min'])
        links.new(group_input.outputs['Radius'], map_range.inputs['From Max'])
        
        # To Min = 1.0 (полная сила), To Max = 0.0 (нет силы)
        map_range.inputs['To Min'].default_value = 1.0
        map_range.inputs['To Max'].default_value = 0.0
        
        # Map Range: промежуточная сила -> финальная сила (inner/outer strength)
        map_range_final = nodes.new('ShaderNodeMapRange')
        map_range_final.clamp = True
        map_range_final.data_type = 'FLOAT'
        map_range_final.interpolation_type = 'LINEAR'
        map_range_final.location = (200, 0)
        
        # Подключаем промежуточную силу
        links.new(map_range.outputs['Result'], map_range_final.inputs['Value'])
        
        # From Min = 0.0, From Max = 1.0
        map_range_final.inputs['From Min'].default_value = 0.0
        map_range_final.inputs['From Max'].default_value = 1.0
        
        # To Min = Outer Strength, To Max = Inner Strength
        links.new(group_input.outputs['Outer Strength'], map_range_final.inputs['To Min'])
        links.new(group_input.outputs['Inner Strength'], map_range_final.inputs['To Max'])

        # Enable multiply - точно как в оригинале
        enable_multiply = nodes.new('ShaderNodeMath')
        enable_multiply.operation = 'MULTIPLY'
        enable_multiply.location = (400, -100)

        # Подключаем: field_strength * Enable (1.0 или 0.0)
        links.new(map_range_final.outputs['Result'], enable_multiply.inputs[0])  # Field strength
        links.new(group_input.outputs['Enable'], enable_multiply.inputs[1])  # Enable (1.0 or 0.0)

        return enable_multiply.outputs['Value']

    def create_field_node_group_for_effectors(self, name_suffix=""):
        """
        Создание node group сферического поля для использования внутри эффекторов
        Возвращает float значение силы поля в каждой точке (НЕ модификатор)
        """
        # Создание node group для поля (не модификатора)
        group_name = f"SphereField{name_suffix}"
        node_group = bpy.data.node_groups.new(type='GeometryNodeTree', name=group_name)

        node_group.color_tag = 'NONE'
        node_group.description = "Sphere Field - возвращает силу поля для точки в пространстве"
        node_group.default_group_node_width = 140
        node_group.is_modifier = False  # Это НЕ модификатор

        # Интерфейс node group для поля - входы параметров и выход Field
        enable_input = node_group.interface.new_socket(name="Enable", in_out='INPUT', socket_type='NodeSocketBool')
        enable_input.default_value = True

        center_input = node_group.interface.new_socket(name="Center", in_out='INPUT', socket_type='NodeSocketVector')
        center_input.default_value = (0.0, 0.0, 0.0)

        radius_input = node_group.interface.new_socket(name="Radius", in_out='INPUT', socket_type='NodeSocketFloat')
        radius_input.default_value = 5.0
        radius_input.min_value = 1.0
        radius_input.max_value = 10000.0

        falloff_input = node_group.interface.new_socket(name="Falloff", in_out='INPUT', socket_type='NodeSocketFloat')
        falloff_input.default_value = 0.0
        falloff_input.min_value = 0.0
        falloff_input.max_value = 1.0

        inner_strength_input = node_group.interface.new_socket(name="Inner Strength", in_out='INPUT', socket_type='NodeSocketFloat')
        inner_strength_input.default_value = 1.0
        inner_strength_input.min_value = 0.0
        inner_strength_input.max_value = 1.0

        outer_strength_input = node_group.interface.new_socket(name="Outer Strength", in_out='INPUT', socket_type='NodeSocketFloat')
        outer_strength_input.default_value = 0.0
        outer_strength_input.min_value = 0.0
        outer_strength_input.max_value = 1.0

        # ВЫХОД: сила поля (float)
        field_output = node_group.interface.new_socket(name="Field", in_out='OUTPUT', socket_type='NodeSocketFloat')
        field_output.default_value = 0.0

        # Создание узлов (аналогично _create_field_logic, но без Store Attribute)
        nodes = node_group.nodes
        links = node_group.links

        # Группа input/output
        group_input = nodes.new('NodeGroupInput')
        group_input.location = (-600, 0)

        group_output = nodes.new('NodeGroupOutput')
        group_output.location = (600, 0)

        # Position для получения координат точек
        position = nodes.new('GeometryNodeInputPosition')
        position.location = (-400, -200)

        # Та же логика что и в _create_field_logic
        # Расчет расстояния до центра сферы
        vector_math_distance = nodes.new('ShaderNodeVectorMath')
        vector_math_distance.operation = 'DISTANCE'
        vector_math_distance.location = (-200, -100)
        links.new(position.outputs[0], vector_math_distance.inputs[0])
        links.new(group_input.outputs['Center'], vector_math_distance.inputs[1])

        # Расчет границы falloff (radius * falloff)
        math_falloff_boundary = nodes.new('ShaderNodeMath')
        math_falloff_boundary.operation = 'MULTIPLY'
        math_falloff_boundary.location = (-200, 100)
        links.new(group_input.outputs['Radius'], math_falloff_boundary.inputs[0])
        links.new(group_input.outputs['Falloff'], math_falloff_boundary.inputs[1])

        # Map Range: расстояние -> сила поля
        map_range = nodes.new('ShaderNodeMapRange')
        map_range.clamp = True
        map_range.data_type = 'FLOAT'
        map_range.interpolation_type = 'LINEAR'
        map_range.location = (0, 0)

        # Подключаем расстояние как входное значение
        links.new(vector_math_distance.outputs['Value'], map_range.inputs['Value'])
        links.new(math_falloff_boundary.outputs['Value'], map_range.inputs['From Min'])
        links.new(group_input.outputs['Radius'], map_range.inputs['From Max'])
        map_range.inputs['To Min'].default_value = 1.0
        map_range.inputs['To Max'].default_value = 0.0

        # Map Range: промежуточная сила -> финальная сила (inner/outer strength)
        map_range_final = nodes.new('ShaderNodeMapRange')
        map_range_final.clamp = True
        map_range_final.data_type = 'FLOAT'
        map_range_final.interpolation_type = 'LINEAR'
        map_range_final.location = (200, 0)

        links.new(map_range.outputs['Result'], map_range_final.inputs['Value'])
        map_range_final.inputs['From Min'].default_value = 0.0
        map_range_final.inputs['From Max'].default_value = 1.0
        links.new(group_input.outputs['Outer Strength'], map_range_final.inputs['To Min'])
        links.new(group_input.outputs['Inner Strength'], map_range_final.inputs['To Max'])

        # Enable multiply для node group версии тоже
        enable_multiply = nodes.new('ShaderNodeMath')
        enable_multiply.operation = 'MULTIPLY'
        enable_multiply.location = (400, 0)

        # Подключаем: field_strength * Enable
        links.new(map_range_final.outputs['Result'], enable_multiply.inputs[0])
        links.new(group_input.outputs['Enable'], enable_multiply.inputs[1])

        # Подключаем к выходу через enable multiply
        links.new(enable_multiply.outputs['Value'], group_output.inputs['Field'])

        return node_group
    
    def get_default_parameters(self):
        """Параметры по умолчанию для Sphere Field"""
        base_defaults = super().get_default_parameters()
        sphere_defaults = {
            "center": (0.0, 0.0, 0.0),
            "radius": 5.0,
            "falloff": 0.0
        }
        return {**base_defaults, **sphere_defaults}
    
    def get_parameter_groups(self):
        """Группировка параметров для UI - точная копия оригинального sphere_config.py"""
        return {
            "Field Settings": [
                {
                    "name": "Enable",
                    "socket_name": "Enable",
                    "description": "Включить/выключить поле",
                    "type": "BOOLEAN",
                    "default": True,
                    "ui_type": "TOGGLE"
                },
                {
                    "name": "Strength",
                    "socket_name": "Inner Strength",
                    "description": "Сила поля в центре",
                    "type": "FLOAT",
                    "default": 1.0,
                    "min": 0.0,
                    "max": 1.0,
                    "ui_type": "SLIDER"
                }
            ],
            "Spatial Parameters": [
                {
                    "name": "Center",
                    "socket_name": "Center",
                    "description": "Центр сферы",
                    "type": "VECTOR",
                    "default": (0.0, 0.0, 0.0),
                    "ui_type": "VECTOR"
                },
                {
                    "name": "Radius",
                    "socket_name": "Radius",
                    "description": "Радиус сферы",
                    "type": "FLOAT",
                    "default": 5.0,
                    "min": 1.0,
                    "max": 10000.0,
                    "ui_type": "SLIDER"
                }
            ],
            "Falloff Controls": [
                {
                    "name": "Falloff",
                    "socket_name": "Falloff",
                    "description": "Зона затухания (0.0 - 1.0)",
                    "type": "FLOAT",
                    "default": 0.0,
                    "min": 0.0,
                    "max": 1.0,
                    "ui_type": "SLIDER"
                },
                {
                    "name": "Outer Strength",
                    "socket_name": "Outer Strength",
                    "description": "Сила поля на краю",
                    "type": "FLOAT",
                    "default": 0.0,
                    "min": 0.0,
                    "max": 1.0,
                    "ui_type": "SLIDER"
                }
            ]
        }

    def get_field_parameter_groups(self):
        """Алиас для совместимости с UI"""
        return self.get_parameter_groups()
    
    def draw_ui(self, layout, context, modifier):
        """
        Отрисовка UI для Sphere Field - точная копия оригинального draw_sphere_field_parameters
        """
        if not modifier:
            layout.label(text="No modifier found", icon='ERROR')
            return

        parameters = self.get_parameter_groups()

        for group_name, group_params in parameters.items():
            # Создаем простую группу
            box = layout.box()

            # Заголовок группы
            header = box.row()
            header.label(text=group_name, icon='DOWNARROW_HLT')

            # Отображаем все параметры группы используя специальную функцию для Fields
            col = box.column()

            for param in group_params:
                socket_name = param["socket_name"]
                param_name = param["name"]

                # Используем стандартную функцию отображения
                from ...ui.generators.field_ui import display_field_socket_prop
                success = display_field_socket_prop(col, modifier, socket_name, text=param_name)

                if not success:
                    # Показываем отладочную информацию только если не удалось отобразить
                    debug_row = col.row()
                    debug_row.label(text=f"{param_name}: Not found", icon='ERROR')

        # Добавляем секцию управления подключениями к эффекторам ТОЛЬКО ОДИН РАЗ
        self.draw_field_connections(layout, modifier)

    def draw_field_connections(self, layout, modifier):
        """Отрисовка секции подключений филда к эффекторам - точная копия оригинала"""
        if not modifier or not modifier.id_data:
            return

        obj = modifier.id_data

        # Заголовок секции
        box = layout.box()
        header = box.row()
        header.label(text="Effector Connections", icon='LINKED')

        col = box.column()

        # Найти все эффекторы на объекте
        effectors = []
        for mod in obj.modifiers:
            if mod.type == 'NODES' and mod.node_group:
                node_group_name = mod.node_group.name.lower()
                if ('effector' in node_group_name or 'random' in node_group_name or 'noise' in node_group_name):
                    # Проверить, подключен ли уже филд к этому эффектору
                    is_connected = False
                    if mod.node_group:
                        for node in mod.node_group.nodes:
                            if node.name == "Field" and node.type == 'GROUP' and node.node_tree:
                                # Дополнительно проверяем, что это именно field node group
                                if 'field' in node.node_tree.name.lower():
                                    is_connected = True
                                    break

                    effectors.append({
                        'name': mod.name,
                        'connected': is_connected
                    })

        if not effectors:
            col.label(text="No effectors found on this object", icon='INFO')
            return

        # Отображение эффекторов с кнопками подключения/отключения
        for effector in effectors:
            row = col.row()
            # Показываем статус для отладки
            status_text = f"{effector['name']} ({'Connected' if effector['connected'] else 'Not Connected'})"
            row.label(text=status_text)

            if effector['connected']:
                # Кнопка отключения
                disconnect_op = row.operator("clonerpro.disconnect_field_from_effector",
                                        text="Disconnect", icon='UNLINKED')
                disconnect_op.field_modifier_name = modifier.name
                disconnect_op.effector_modifier_name = effector['name']
            else:
                # Кнопка подключения
                connect_op = row.operator("clonerpro.connect_field_to_effector",
                                        text="Connect", icon='LINKED')
                connect_op.field_modifier_name = modifier.name
                connect_op.effector_modifier_name = effector['name']


# Compatibility function for easy integration
def create_sphere_field(name_suffix=""):
    """Create Sphere Field using class-based approach"""
    field = SphereField()
    return field.create_node_group(name_suffix)


# Alias for backward compatibility
def create_sphere_field_modifier(name_suffix=""):
    """Create Sphere Field modifier - class-based compatibility function"""
    return create_sphere_field(name_suffix)


# Compatibility function for node group creation (for use inside effectors)
def create_sphere_field_node_group(name_suffix=""):
    """Create Sphere Field node group for use inside effectors - class-based compatibility function"""
    field = SphereField()
    return field.create_field_node_group_for_effectors(name_suffix)
