"""
Random Effector - Class-based implementation
Объединяет логику и конфигурацию в один класс, сохраняя 1 в 1 функционал оригинала
"""

import bpy
from ..base_effector import BaseEffector


class RandomEffector(BaseEffector):
    """
    Random Effector - применяет случайные трансформации к клонам
    Объединяет всю логику, конфигурацию и UI в одном классе
    Сохраняет 1 в 1 функционал оригинального random.py
    """
    
    bl_idname = "RANDOM"
    bl_label = "Random Effector"
    
    def __init__(self):
        super().__init__()
    
    def register_properties(self, props_owner):
        """
        Регистрация всех свойств Random Effector
        Заменяет старый random_config.py
        """
        # Регистрируем базовые свойства
        super().register_properties(props_owner)
        
        # Random Settings - специфичные для Random Effector
        props_owner.random_uniform_scale = bpy.props.BoolProperty(
            name="Uniform Scale",
            description="Use the same random value for all scale axes",
            default=True
        )
    
    def get_specific_sockets(self):
        """
        Специфичные сокеты для Random Effector
        
        Returns:
            list: Список специфичных сокетов (name, socket_type, in_out, default, min, max)
        """
        return [
            ("Uniform Scale", "NodeSocketBool", "INPUT", True),
        ]
    
    def _create_effector_logic(self, base_nodes):
        """
        Основная логика Random Effector - точная копия оригинального функционала
        
        Args:
            base_nodes: Словарь с базовыми нодами
            
        Returns:
            NodeSocket: Финальный выход геометрии с примененным эффектом
        """
        nodes = base_nodes['nodes']
        links = base_nodes['links']
        group_input = base_nodes['group_input']
        index = base_nodes['index']
        
        # Random position
        random_position = nodes.new('FunctionNodeRandomValue')
        random_position.data_type = 'FLOAT_VECTOR'
        random_position.location = (-400, 200)
        
        # Link seed and ID
        links.new(group_input.outputs['Seed'], random_position.inputs['Seed'])
        links.new(index.outputs['Index'], random_position.inputs['ID'])
        
        # Set random position range (-Position to +Position)
        vector_math_neg = nodes.new('ShaderNodeVectorMath')
        vector_math_neg.operation = 'MULTIPLY'
        vector_math_neg.inputs[1].default_value = (-1.0, -1.0, -1.0)
        vector_math_neg.location = (-600, 300)
        links.new(group_input.outputs['Position'], vector_math_neg.inputs[0])
        
        links.new(vector_math_neg.outputs['Vector'], random_position.inputs['Min'])
        links.new(group_input.outputs['Position'], random_position.inputs['Max'])
        
        # Random rotation
        random_rotation = nodes.new('FunctionNodeRandomValue')
        random_rotation.data_type = 'FLOAT_VECTOR'
        random_rotation.location = (-400, 0)
        
        # Link seed and ID for rotation (with offset)
        seed_offset_rot = nodes.new('ShaderNodeMath')
        seed_offset_rot.operation = 'ADD'
        seed_offset_rot.location = (-600, 0)
        links.new(group_input.outputs['Seed'], seed_offset_rot.inputs[0])
        seed_offset_rot.inputs[1].default_value = 42.0  # Offset for rotation
        links.new(seed_offset_rot.outputs['Value'], random_rotation.inputs['Seed'])
        links.new(index.outputs['Index'], random_rotation.inputs['ID'])
        
        # Set random rotation range (-Rotation to +Rotation)
        vector_math_neg_rot = nodes.new('ShaderNodeVectorMath')
        vector_math_neg_rot.operation = 'MULTIPLY'
        vector_math_neg_rot.inputs[1].default_value = (-1.0, -1.0, -1.0)
        vector_math_neg_rot.location = (-600, 100)
        links.new(group_input.outputs['Rotation'], vector_math_neg_rot.inputs[0])
        
        links.new(vector_math_neg_rot.outputs['Vector'], random_rotation.inputs['Min'])
        links.new(group_input.outputs['Rotation'], random_rotation.inputs['Max'])
        
        # Random scale
        random_scale = nodes.new('FunctionNodeRandomValue')
        random_scale.data_type = 'FLOAT_VECTOR'
        random_scale.location = (-400, -200)

        # For uniform scale
        random_uniform_scale = nodes.new('FunctionNodeRandomValue')
        random_uniform_scale.data_type = 'FLOAT'
        random_uniform_scale.location = (-400, -400)

        # Link seed and ID for scale (with different offset)
        seed_offset_scale = nodes.new('ShaderNodeMath')
        seed_offset_scale.operation = 'ADD'
        seed_offset_scale.location = (-600, -200)
        links.new(group_input.outputs['Seed'], seed_offset_scale.inputs[0])
        seed_offset_scale.inputs[1].default_value = 84.0  # Different offset for scale
        links.new(seed_offset_scale.outputs['Value'], random_scale.inputs['Seed'])
        links.new(index.outputs['Index'], random_scale.inputs['ID'])
        links.new(seed_offset_scale.outputs['Value'], random_uniform_scale.inputs['Seed'])
        links.new(index.outputs['Index'], random_uniform_scale.inputs['ID'])

        # Set scale range (1-Scale to 1+Scale) как в оригинале
        one_minus_scale = nodes.new('ShaderNodeVectorMath')
        one_minus_scale.operation = 'SUBTRACT'
        one_minus_scale.inputs[0].default_value = (1.0, 1.0, 1.0)
        one_minus_scale.location = (-600, -100)
        links.new(group_input.outputs['Scale'], one_minus_scale.inputs[1])

        one_plus_scale = nodes.new('ShaderNodeVectorMath')
        one_plus_scale.operation = 'ADD'
        one_plus_scale.inputs[0].default_value = (1.0, 1.0, 1.0)
        one_plus_scale.location = (-600, -200)
        links.new(group_input.outputs['Scale'], one_plus_scale.inputs[1])

        links.new(one_minus_scale.outputs['Vector'], random_scale.inputs['Min'])
        links.new(one_plus_scale.outputs['Vector'], random_scale.inputs['Max'])

        # For uniform scale (single float)
        scale_max = nodes.new('ShaderNodeMath')
        scale_max.operation = 'MAXIMUM'
        scale_max.location = (-600, -300)
        links.new(group_input.outputs['Scale'], scale_max.inputs[0])  # X

        scale_max_temp = nodes.new('ShaderNodeMath')
        scale_max_temp.operation = 'MAXIMUM'
        scale_max_temp.location = (-700, -350)
        links.new(group_input.outputs['Scale'], scale_max_temp.inputs[0])  # Y
        links.new(group_input.outputs['Scale'], scale_max_temp.inputs[1])  # Z
        links.new(scale_max_temp.outputs[0], scale_max.inputs[1])

        one_minus_uniform = nodes.new('ShaderNodeMath')
        one_minus_uniform.operation = 'SUBTRACT'
        one_minus_uniform.inputs[0].default_value = 1.0
        one_minus_uniform.location = (-600, -400)
        links.new(scale_max.outputs[0], one_minus_uniform.inputs[1])

        one_plus_uniform = nodes.new('ShaderNodeMath')
        one_plus_uniform.operation = 'ADD'
        one_plus_uniform.inputs[0].default_value = 1.0
        one_plus_uniform.location = (-600, -500)
        links.new(scale_max.outputs[0], one_plus_uniform.inputs[1])

        links.new(one_minus_uniform.outputs[0], random_uniform_scale.inputs['Min'])
        links.new(one_plus_uniform.outputs[0], random_uniform_scale.inputs['Max'])
        
        # Switch between uniform and non-uniform scale
        scale_switch = nodes.new('GeometryNodeSwitch')
        scale_switch.input_type = 'VECTOR'
        scale_switch.location = (-200, -300)
        links.new(group_input.outputs['Uniform Scale'], scale_switch.inputs[0])  # Switch
        links.new(random_scale.outputs['Value'], scale_switch.inputs['False'])  # False (vector scale)

        # Create uniform scale vector
        uniform_vector = nodes.new('ShaderNodeCombineXYZ')
        uniform_vector.location = (-300, -400)
        links.new(random_uniform_scale.outputs['Value'], uniform_vector.inputs['X'])
        links.new(random_uniform_scale.outputs['Value'], uniform_vector.inputs['Y'])
        links.new(random_uniform_scale.outputs['Value'], uniform_vector.inputs['Z'])

        links.new(uniform_vector.outputs['Vector'], scale_switch.inputs['True'])  # True (uniform scale)
        
        # Combine strength with field mask (как в оригинале)
        field_strength_combine = nodes.new('ShaderNodeMath')
        field_strength_combine.operation = 'MULTIPLY'
        field_strength_combine.location = (-400, -600)
        links.new(group_input.outputs['Strength'], field_strength_combine.inputs[0])  # Base strength
        links.new(group_input.outputs['Field'], field_strength_combine.inputs[1])  # Field mask

        # Apply combined strength multiplier to position and rotation
        strength_mul_pos = nodes.new('ShaderNodeVectorMath')
        strength_mul_pos.operation = 'MULTIPLY'
        strength_mul_pos.location = (-200, 200)
        links.new(random_position.outputs['Value'], strength_mul_pos.inputs[0])
        links.new(field_strength_combine.outputs['Value'], strength_mul_pos.inputs[1])  # Combined strength

        strength_mul_rot = nodes.new('ShaderNodeVectorMath')
        strength_mul_rot.operation = 'MULTIPLY'
        strength_mul_rot.location = (-200, 0)
        links.new(random_rotation.outputs['Value'], strength_mul_rot.inputs[0])
        links.new(field_strength_combine.outputs['Value'], strength_mul_rot.inputs[1])  # Combined strength

        # Create vector from field_strength_combine for scale multiplication
        field_strength_vector = nodes.new('ShaderNodeCombineXYZ')
        field_strength_vector.location = (-300, -500)
        links.new(field_strength_combine.outputs['Value'], field_strength_vector.inputs['X'])
        links.new(field_strength_combine.outputs['Value'], field_strength_vector.inputs['Y'])
        links.new(field_strength_combine.outputs['Value'], field_strength_vector.inputs['Z'])

        # Scale должен быть 1.0 + (random_scale - 1.0) * field_strength
        # Сначала вычисляем (scale - 1)
        scale_offset = nodes.new('ShaderNodeVectorMath')
        scale_offset.operation = 'SUBTRACT'
        scale_offset.inputs[1].default_value = (1.0, 1.0, 1.0)
        scale_offset.location = (-100, -300)
        links.new(scale_switch.outputs['Output'], scale_offset.inputs[0])

        # Умножаем на field strength
        scale_mul_field = nodes.new('ShaderNodeVectorMath')
        scale_mul_field.operation = 'MULTIPLY'
        scale_mul_field.location = (0, -300)
        links.new(scale_offset.outputs['Vector'], scale_mul_field.inputs[0])
        links.new(field_strength_vector.outputs['Vector'], scale_mul_field.inputs[1])
        
        # Добавляем 1.0 обратно к scale
        final_scale = nodes.new('ShaderNodeVectorMath')
        final_scale.operation = 'ADD'
        final_scale.inputs[1].default_value = (1.0, 1.0, 1.0)
        final_scale.location = (100, -300)
        links.new(scale_mul_field.outputs['Vector'], final_scale.inputs[0])

        # Apply transformations to instances
        # Start with the input geometry
        translate_instances = nodes.new('GeometryNodeTranslateInstances')
        translate_instances.location = (0, 200)
        links.new(group_input.outputs['Geometry'], translate_instances.inputs['Instances'])
        links.new(strength_mul_pos.outputs['Vector'], translate_instances.inputs['Translation'])

        # Rotate instances
        rotate_instances = nodes.new('GeometryNodeRotateInstances')
        rotate_instances.location = (200, 100)
        links.new(translate_instances.outputs['Instances'], rotate_instances.inputs['Instances'])
        links.new(strength_mul_rot.outputs['Vector'], rotate_instances.inputs['Rotation'])

        # Scale instances
        scale_instances = nodes.new('GeometryNodeScaleInstances')
        scale_instances.location = (400, 0)
        links.new(rotate_instances.outputs['Instances'], scale_instances.inputs['Instances'])
        links.new(final_scale.outputs['Vector'], scale_instances.inputs['Scale'])
        
        return scale_instances.outputs['Instances']
    
    def get_default_parameters(self):
        """Параметры по умолчанию для Random Effector"""
        base_defaults = super().get_default_parameters()
        random_defaults = {
            "uniform_scale": True
        }
        return {**base_defaults, **random_defaults}
    
    def get_parameter_groups(self):
        """Группировка параметров для UI"""
        base_groups = super().get_effector_parameter_groups()
        random_groups = {
            "Random Settings": ["Uniform Scale", "Seed"]
        }
        return {**random_groups, **base_groups}

    def draw_ui(self, layout, context, modifier):
        """
        Отрисовка UI для Random Effector
        """
        from ...ui.utils.ui_helpers import display_socket_prop

        # Получаем группы параметров
        parameter_groups = self.get_parameter_groups()

        # Отображаем группы параметров
        for group_name, param_names in parameter_groups.items():
            if param_names:
                # Создаем бокс для группы
                box = layout.box()
                col = box.column(align=True)

                # Заголовок группы с иконкой
                icon_map = {
                    "Random Settings": "FORCE_HARMONIC",
                    "Effector Settings": "SETTINGS",
                    "Transform": "ORIENTATION_GIMBAL",
                }
                icon = icon_map.get(group_name, "DOT")
                col.label(text=f"{group_name}:", icon=icon)

                # Добавляем параметры
                col.separator()
                for param_name in param_names:
                    display_socket_prop(col, modifier, param_name, text=param_name)


# Compatibility function for easy integration
def create_random_effector(name_suffix=""):
    """Create Random Effector using class-based approach"""
    effector = RandomEffector()
    return effector.create_node_group(name_suffix)


# Alias for backward compatibility
def create_random_effector_logic_group(name_suffix=""):
    """Create Random Effector logic group - class-based compatibility function"""
    return create_random_effector(name_suffix)
