"""
ClonerPro Core Module

Центральная точка доступа к основной функциональности ClonerPro.
Содержит неизменяемую базовую логику и константы системы.
"""

# Версия и метаданные аддона
ADDON_VERSION = "1.0.0"
ADDON_NAME = "ClonerPro"
ADDON_DESCRIPTION = "Simplified cloning system for Blender"

# Типы компонентов в системе
COMPONENT_TYPES = {
    'CLONER': 'cloner',
    'EFFECTOR': 'effector', 
    'FIELD': 'field'
}

# Режимы создания клонеров
CREATION_MODES = {
    'OBJECT': 'object',     # Создает новый объект клонера
    'STACKED': 'stacked',   # Модификатор на оригинальном объекте
    'COLLECTION': 'collection'  # Клонирование коллекций
}

# Константы для UI
UI_CONSTANTS = {
    'PANEL_CATEGORY': 'ClonerPro',
    'PANEL_REGION': 'UI',
    'ICON_CLONER': 'DUPLICATE',
    'ICON_EFFECTOR': 'FORCE_TURBULENCE',
    'ICON_FIELD': 'MESH_UVSPHERE'
}

# Базовые настройки системы
SYSTEM_DEFAULTS = {
    'anti_recursion': True,
    'auto_update': True,
    'debug_mode': False,
    'max_instances': 10000
}

# Префиксы для именования объектов
NAMING_PREFIXES = {
    'cloner_object': 'cloner_',
    'cloner_collection': 'CLONERS_',
    'clone_target_collection': 'ClonerTo',
    'node_group': 'ClonerPro_'
}

# Критически важные функции для работы системы
def is_valid_object(obj):
    """Проверяет, является ли объект валидным для клонирования"""
    if not obj:
        return False
    if obj.type not in ['MESH', 'CURVE', 'SURFACE', 'META', 'FONT']:
        return False
    return True

def is_cloner_object(obj):
    """Проверяет, является ли объект клонером"""
    if not obj:
        return False
    return get_cloner_modifier(obj) is not None

def get_cloner_modifier(obj):
    """Получает модификатор клонера из объекта"""
    if not obj:
        return None
    for modifier in obj.modifiers:
        if (modifier.type == 'NODES' and 
            modifier.node_group):
            # Проверяем сначала по метаданным (наиболее надежно)
            if modifier.get("cloner_type") or modifier.get("cloner_mode"):
                return modifier
            
            # Альтернативная проверка: по паттернам имен node groups
            node_group_name = modifier.node_group.name
            cloner_patterns = [
                "GridCloner", "LinearCloner", "CircleCloner", "SpiralCloner",
                "ObjectCloner", "CurvesCloner", "VolumeCloner", "SplineCloner",
                "ClonerPro_"  # Старый префикс для совместимости
            ]
            
            for pattern in cloner_patterns:
                if pattern in node_group_name:
                    return modifier
    return None

def get_component_type_from_name(name):
    """Определяет тип компонента по имени node group"""
    name_upper = name.upper()
    for comp_type in COMPONENT_TYPES:
        if comp_type in name_upper:
            return COMPONENT_TYPES[comp_type]
    return None

# Функции для работы с коллекциями
def ensure_collection_exists(collection_name, parent_collection=None):
    """Гарантирует существование коллекции"""
    import bpy
    
    if collection_name in bpy.data.collections:
        return bpy.data.collections[collection_name]
    
    new_collection = bpy.data.collections.new(collection_name)
    
    if parent_collection:
        parent_collection.children.link(new_collection)
    else:
        bpy.context.scene.collection.children.link(new_collection)
    
    return new_collection

def get_or_create_cloner_collection(obj_name):
    """Получает или создает коллекцию для клонеров объекта"""
    collection_name = f"{NAMING_PREFIXES['cloner_collection']}{obj_name}"
    return ensure_collection_exists(collection_name)

def get_or_create_target_collection():
    """Получает или создает коллекцию ClonerTo"""
    return ensure_collection_exists(NAMING_PREFIXES['clone_target_collection'])


# === SYSTEM SEPARATION ===
UNIFIED_CLONERS = {"GRID", "LINEAR", "CIRCLE", "SPIRAL"}
MESH_CLONERS = {"OBJECT", "CURVES", "VOLUME", "SPLINE"}

def get_cloner_system(cloner_type: str) -> str:
    """
    Определяет к какой системе относится клонер
    
    Returns:
        "unified" | "mesh" | "unknown"
    """
    if cloner_type in UNIFIED_CLONERS:
        return "unified"
    elif cloner_type in MESH_CLONERS:
        return "mesh"
    else:
        return "unknown"

def is_unified_cloner(cloner_type: str) -> bool:
    """Проверка на unified клонер"""
    return cloner_type in UNIFIED_CLONERS

def is_mesh_cloner(cloner_type: str) -> bool:
    """Проверка на mesh клонер"""
    return cloner_type in MESH_CLONERS

# === METADATA UTILITIES ===

def set_cloner_metadata(modifier, cloner_type: str, mode: str, **kwargs):
    """
    Простая функция установки стандартных метаданных
    НЕ класс-менеджер - просто утилита
    
    Теперь интегрирована с UUID системой для обратной совместимости
    """
    # Сначала устанавливаем UUID метаданные если включена UUID поддержка
    use_uuid = kwargs.get("use_uuid", True)  # По умолчанию включено
    
    if use_uuid:
        try:
            from .uuid.manager import BlenderClonerUUIDManager
            # Делегируем UUID менеджеру основную работу с метаданными
            BlenderClonerUUIDManager.set_cloner_uuid_metadata(modifier, cloner_type, mode, **kwargs)
        except ImportError:
            print("⚠️ UUID system not available, using standard metadata")
            use_uuid = False

    # Устанавливаем стандартные метаданные для обратной совместимости
    if not use_uuid:
        # Базовые метаданные для всех клонеров
        modifier["cloner_type"] = cloner_type
        modifier["cloner_mode"] = mode
        modifier["cloner_system"] = get_cloner_system(cloner_type)
        
        # Системные флаги  
        modifier["is_unified_cloner"] = is_unified_cloner(cloner_type)
        modifier["is_mesh_cloner"] = is_mesh_cloner(cloner_type)
        
        # Режимные флаги
        modifier["is_stacked_mode"] = (mode == "STACKED")
        modifier["is_collection_mode"] = (mode == "COLLECTION")
        modifier["is_object_mode"] = (mode == "OBJECT")
        
        # CHAIN METADATA: Добавляем метаданные для цепочек клонеров
        modifier["is_chained_cloner"] = kwargs.get("is_chained", False)
        modifier["previous_cloner_object"] = kwargs.get("previous_cloner", "")
        modifier["next_cloners"] = kwargs.get("next_cloners", "")  # Список через запятую
        modifier["chain_source_object"] = kwargs.get("chain_source_object", "")
        modifier["chain_source_collection"] = kwargs.get("chain_source_collection", "")
        modifier["chain_index"] = kwargs.get("chain_index", 0)
        
        # Дополнительные метаданные
        for key, value in kwargs.items():
            if key not in ["is_chained", "previous_cloner", "next_cloners", 
                           "chain_source_object", "chain_source_collection", "chain_index", "use_uuid"]:
                modifier[key] = value
        
        print(f"✓ Standard Metadata set: {cloner_type} ({mode})")

def get_cloner_info(modifier) -> dict:
    """
    Извлечь информацию о клонере из метаданных
    Возвращает пустой dict если не клонер
    Поддерживает как UUID так и стандартные метаданные
    """
    if not modifier:
        return {}
    
    if not (modifier.type == 'NODES' and 
            modifier.node_group and 
            "cloner_type" in modifier):
        return {}
    
    info = {
        "type": modifier.get("cloner_type", "UNKNOWN"),
        "mode": modifier.get("cloner_mode", "UNKNOWN"),
        "system": modifier.get("cloner_system", "unknown"),
        "is_stacked": modifier.get("is_stacked_mode", False),
        "original_object": modifier.get("original_object", ""),
        "target_object": modifier.get("target_object", ""),
        # CHAIN INFO (Standard)
        "is_chained": modifier.get("is_chained_cloner", False),
        "previous_cloner": modifier.get("previous_cloner_object", ""),
        "next_cloners": modifier.get("next_cloners", ""),
        "chain_source_object": modifier.get("chain_source_object", ""),
        "chain_source_collection": modifier.get("chain_source_collection", ""),
        "chain_index": modifier.get("chain_index", 0)
    }
    
    # Добавляем UUID информацию если доступна
    cloner_uuid = modifier.get("cloner_uuid")
    if cloner_uuid:
        info.update({
            # UUID SYSTEM INFO
            "has_uuid": True,
            "cloner_uuid": cloner_uuid,
            "chain_uuid": modifier.get("chain_uuid", ""),
            "previous_cloner_uuid": modifier.get("previous_cloner_uuid", ""),
            "next_cloner_uuids": modifier.get("next_cloner_uuids", ""),
            "chain_source_uuid": modifier.get("chain_source_uuid", ""),
            "display_name": modifier.get("display_name", f"{info['type']} Cloner"),
            "user_notes": modifier.get("user_notes", ""),
            "creation_timestamp": modifier.get("creation_timestamp", 0),
            "chain_sequence": modifier.get("chain_sequence", 0),
            # Blender-specific UUID data
            "blender_session_uid": modifier.get("blender_session_uid", 0),
            "blender_persistent_uid": modifier.get("blender_persistent_uid", ""),
            "object_fingerprint": modifier.get("object_fingerprint", "")
        })
    else:
        info["has_uuid"] = False
    
    return info

# Основная информация для экспорта
__all__ = [
    'ADDON_VERSION',
    'ADDON_NAME', 
    'ADDON_DESCRIPTION',
    'COMPONENT_TYPES',
    'CREATION_MODES',
    'UI_CONSTANTS',
    'SYSTEM_DEFAULTS',
    'NAMING_PREFIXES',
    'UNIFIED_CLONERS',
    'MESH_CLONERS',
    'is_valid_object',
    'is_cloner_object',
    'get_cloner_modifier',
    'get_component_type_from_name',
    'ensure_collection_exists',
    'get_or_create_cloner_collection',
    'get_or_create_target_collection',
    'get_cloner_system',
    'is_unified_cloner',
    'is_mesh_cloner',
    'set_cloner_metadata',
    'get_cloner_info'
]